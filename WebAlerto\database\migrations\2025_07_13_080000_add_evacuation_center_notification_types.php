<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new evacuation center notification types to the enum
        DB::statement("ALTER TABLE app_notifications MODIFY COLUMN type ENUM(
            'evacuation_center_added',
            'evacuation_center_full',
            'evacuation_center_available',
            'evacuation_center_closed',
            'evacuation_center_reopened',
            'evacuation_center_update',
            'emergency_alert',
            'system_update',
            'general'
        ) DEFAULT 'general'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the new evacuation center notification types
        DB::statement("ALTER TABLE app_notifications MODIFY COLUMN type ENUM(
            'evacuation_center_added',
            'evacuation_center_full',
            'emergency_alert',
            'system_update',
            'general'
        ) DEFAULT 'general'");
    }
};
