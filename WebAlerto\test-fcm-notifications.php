<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== FCM Push Notification Test Script ===\n\n";

// Test 1: Check Firebase Configuration
echo "1. CHECKING FIREBASE CONFIGURATION...\n";
echo "   Project ID: " . config('firebase.projects.app.project_id', 'NOT SET') . "\n";
echo "   Service Account: " . (file_exists(config('firebase.projects.app.credentials.file')) ? 'EXISTS' : 'MISSING') . "\n";

// Test 2: Check Database Setup
echo "\n2. CHECKING DATABASE SETUP...\n";
try {
    $mobileUsers = App\Models\User::where('role', 'mobile_user')->count();
    $activeTokens = App\Models\DeviceToken::where('is_active', true)->count();
    $totalTokens = App\Models\DeviceToken::count();
    
    echo "   Mobile Users: $mobileUsers\n";
    echo "   Active Device Tokens: $activeTokens\n";
    echo "   Total Device Tokens: $totalTokens\n";
    
    // Show sample tokens
    $sampleTokens = App\Models\DeviceToken::where('is_active', true)
        ->whereNotNull('token')
        ->take(3)
        ->get(['id', 'user_id', 'device_type', 'token']);
    
    echo "   Sample Tokens:\n";
    foreach ($sampleTokens as $token) {
        $tokenPreview = $token->token ? substr($token->token, 0, 30) . '...' : 'NULL';
        echo "     ID: {$token->id}, User: {$token->user_id}, Type: {$token->device_type}, Token: {$tokenPreview}\n";
    }
    
} catch (Exception $e) {
    echo "   ERROR: " . $e->getMessage() . "\n";
}

// Test 3: Check FCM Service
echo "\n3. CHECKING FCM SERVICE...\n";
try {
    $fcmService = app(App\Services\FCMService::class);
    echo "   FCM Service: AVAILABLE\n";
} catch (Exception $e) {
    echo "   FCM Service: ERROR - " . $e->getMessage() . "\n";
}

// Test 4: Test with a real FCM token
echo "\n4. TESTING FCM TOKEN VALIDATION...\n";
$validTokens = [];
$invalidTokens = [];

$tokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->pluck('token')
    ->toArray();

foreach ($tokens as $token) {
    if (empty($token) || $token === 'null' || strlen($token) < 50) {
        $invalidTokens[] = substr($token, 0, 20) . '...';
    } else {
        $validTokens[] = substr($token, 0, 20) . '...';
    }
}

echo "   Valid Tokens: " . count($validTokens) . "\n";
echo "   Invalid Tokens: " . count($invalidTokens) . "\n";

if (count($invalidTokens) > 0) {
    echo "   Invalid Token Examples:\n";
    foreach (array_slice($invalidTokens, 0, 3) as $token) {
        echo "     - $token\n";
    }
}

// Test 5: Send a test notification
echo "\n5. SENDING TEST NOTIFICATION...\n";

if (count($validTokens) > 0) {
    try {
        // Get the first valid token for testing
        $testToken = App\Models\DeviceToken::where('is_active', true)
            ->whereNotNull('token')
            ->where('token', '!=', 'null')
            ->whereRaw('LENGTH(token) > 50')
            ->first();
        
        if ($testToken) {
            echo "   Using token ID: {$testToken->id}\n";
            echo "   Token preview: " . substr($testToken->token, 0, 30) . "...\n";
            
            $fcmService = app(App\Services\FCMService::class);
            
            $notificationData = [
                'title' => 'Test Notification',
                'message' => 'This is a test notification from WebAlerto at ' . date('Y-m-d H:i:s'),
                'category' => 'test',
                'test_time' => time()
            ];
            
            echo "   Sending notification...\n";
            $result = $fcmService->sendToToken($testToken->token, $notificationData);
            
            if ($result['success']) {
                echo "   ✅ SUCCESS: Notification sent successfully!\n";
                $messageId = $result['message_id'] ?? 'N/A';
                if (is_array($messageId)) {
                    $messageId = json_encode($messageId);
                }
                echo "   Message ID: " . $messageId . "\n";
            } else {
                echo "   ❌ FAILED: " . ($result['error'] ?? 'Unknown error') . "\n";
            }
            
        } else {
            echo "   ❌ No valid FCM tokens found for testing\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ ERROR: " . $e->getMessage() . "\n";
        echo "   Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "   ❌ No valid tokens available for testing\n";
}

// Test 6: Check Firebase credentials in detail
echo "\n6. CHECKING FIREBASE CREDENTIALS...\n";
try {
    $credentialsPath = config('firebase.projects.app.credentials.file');
    echo "   Credentials Path: $credentialsPath\n";
    
    if (file_exists($credentialsPath)) {
        $credentials = json_decode(file_get_contents($credentialsPath), true);
        echo "   Project ID in file: " . ($credentials['project_id'] ?? 'NOT FOUND') . "\n";
        echo "   Client Email: " . ($credentials['client_email'] ?? 'NOT FOUND') . "\n";
        echo "   Private Key: " . (isset($credentials['private_key']) ? 'PRESENT' : 'MISSING') . "\n";
    } else {
        echo "   ❌ Credentials file not found!\n";
    }
} catch (Exception $e) {
    echo "   ERROR: " . $e->getMessage() . "\n";
}

// Test 7: Manual FCM API test
echo "\n7. MANUAL FCM API TEST...\n";
if (count($validTokens) > 0) {
    try {
        $testToken = App\Models\DeviceToken::where('is_active', true)
            ->whereNotNull('token')
            ->where('token', '!=', 'null')
            ->whereRaw('LENGTH(token) > 50')
            ->first();
        
        if ($testToken) {
            echo "   Testing direct FCM API call...\n";
            
            // This will test if we can reach FCM servers
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/v1/projects/' . config('firebase.projects.app.project_id') . '/messages:send');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "   FCM API Response Code: $httpCode\n";
            if ($httpCode == 401) {
                echo "   ✅ FCM API is reachable (401 = authentication required, which is expected)\n";
            } elseif ($httpCode == 0) {
                echo "   ❌ Cannot reach FCM API (network issue?)\n";
            } else {
                echo "   ⚠️  Unexpected response code\n";
            }
        }
    } catch (Exception $e) {
        echo "   ERROR: " . $e->getMessage() . "\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
echo "Check the results above to identify any issues.\n";
echo "If notifications are being sent but not received, the issue might be:\n";
echo "1. Mobile app not properly registered for FCM\n";
echo "2. Device token is invalid or expired\n";
echo "3. Mobile app is not running or FCM service is disabled\n";
echo "4. Network connectivity issues\n";
echo "5. Firebase project configuration mismatch\n\n";
