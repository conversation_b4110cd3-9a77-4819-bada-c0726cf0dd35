<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Direct Evacuation Center Full Notification Test ===\n\n";

// Get the first evacuation center
$center = App\Models\Evacuation::first();

if (!$center) {
    echo "❌ No evacuation centers found!\n";
    exit;
}

echo "📍 TESTING WITH CENTER: {$center->name} (ID: {$center->id})\n";
echo "📍 Barangay: {$center->barangay}\n";
echo "📍 Current Status: {$center->status}\n\n";

// Check mobile users and tokens
$mobileUsers = App\Models\User::where('role', 'mobile_user')->get();
$activeTokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->count();

echo "📱 Mobile Users: {$mobileUsers->count()}\n";
echo "🔔 Active FCM Tokens: {$activeTokens}\n\n";

if ($mobileUsers->count() === 0 || $activeTokens === 0) {
    echo "⚠️  Cannot send notifications - missing users or tokens\n";
    exit;
}

echo "🔔 SENDING EVACUATION CENTER FULL NOTIFICATION...\n\n";

try {
    // Step 1: Create database notifications
    echo "📝 Step 1: Creating database notifications...\n";
    
    foreach ($mobileUsers as $user) {
        $notification = App\Models\AppNotification::create([
            'user_id' => $user->id,
            'type' => 'evacuation_center_full',
            'title' => 'Evacuation Center Full',
            'message' => "The evacuation center '{$center->name}' in {$center->barangay} is now full. Please find alternative evacuation centers.",
            'data' => json_encode([
                'center_id' => $center->id,
                'center_name' => $center->name,
                'barangay' => $center->barangay,
                'disaster_types' => $center->disaster_type,
                'latitude' => $center->latitude,
                'longitude' => $center->longitude,
                'status' => 'Full'
            ]),
            'read' => false
        ]);
        
        echo "   ✅ Database notification created for user {$user->id}\n";
    }
    
    // Step 2: Send FCM push notifications
    echo "\n📤 Step 2: Sending FCM push notifications...\n";
    
    $fcmService = app(\App\Services\FCMService::class);
    
    // Get disaster types as string
    $disasterTypes = 'General';
    if ($center->disaster_type) {
        if (is_array($center->disaster_type)) {
            $disasterTypes = implode(', ', $center->disaster_type);
        } elseif (is_string($center->disaster_type)) {
            $disasterTypes = $center->disaster_type;
        } else {
            $disasterTypes = json_encode($center->disaster_type);
        }
    }
    
    // Prepare notification data
    $notificationData = [
        'title' => '⚠️ Evacuation Center Full',
        'message' => "The evacuation center '{$center->name}' in {$center->barangay} is now full. Please find alternative evacuation centers.",
        'category' => 'evacuation_center_full',
        'center_id' => $center->id,
        'center_name' => $center->name,
        'barangay' => $center->barangay,
        'disaster_types' => $disasterTypes,
        'latitude' => $center->latitude,
        'longitude' => $center->longitude,
        'status' => 'Full',
        'sound' => 'default',
        'priority' => 'high'
    ];
    
    // Get all FCM tokens
    $tokens = [];
    foreach ($mobileUsers as $user) {
        $deviceTokens = App\Models\DeviceToken::where('user_id', $user->id)
            ->where('is_active', true)
            ->whereNotNull('token')
            ->where('token', '!=', 'null')
            ->whereRaw('LENGTH(token) > 50')
            ->pluck('token')
            ->toArray();
        $tokens = array_merge($tokens, $deviceTokens);
    }
    
    echo "   📱 Found {count($tokens)} FCM tokens\n";
    
    if (count($tokens) > 0) {
        // Send to all tokens
        $result = $fcmService->sendMulticast($tokens, $notificationData);
        
        if ($result['success']) {
            echo "   ✅ FCM notifications sent successfully!\n";
            echo "   📊 Success: {$result['success_count']}, Failed: {$result['failure_count']}\n";
        } else {
            echo "   ❌ FCM sending failed: " . ($result['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "   ❌ No valid FCM tokens found\n";
    }
    
    echo "\n🎯 NOTIFICATION SENT!\n";
    echo "📱 CHECK YOUR MOBILE DEVICE NOW\n";
    echo "You should see a notification about '{$center->name}' being full.\n\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

// Show recent notifications
echo "\n📋 RECENT NOTIFICATIONS IN DATABASE:\n";
$recent = App\Models\AppNotification::where('type', 'evacuation_center_full')
    ->orderBy('created_at', 'desc')
    ->take(3)
    ->get(['title', 'message', 'created_at']);

foreach ($recent as $notif) {
    echo "   - {$notif->title}: " . substr($notif->message, 0, 50) . "...\n";
    echo "     Created: {$notif->created_at->diffForHumans()}\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "NEXT STEPS:\n";
echo str_repeat("=", 60) . "\n";
echo "1. If you received the notification: ✅ System is working!\n";
echo "2. If you didn't receive it: Check mobile app FCM configuration\n";
echo "3. Test the web dashboard by clicking 'Mark as Full' on any center\n";
echo "4. The web dashboard should now send notifications properly\n\n";

echo "=== END TEST ===\n";
