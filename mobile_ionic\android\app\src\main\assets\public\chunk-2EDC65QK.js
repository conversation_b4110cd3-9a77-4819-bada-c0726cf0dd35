import{a as ae}from"./chunk-ZNZVS5VC.js";import"./chunk-XICXROI7.js";import{a as re}from"./chunk-G7IUXWVS.js";import{$ as _,Ca as I,D as a,E as g,H as f,Hb as U,I as c,Lb as J,M as o,Mb as E,N as r,O as l,Ob as K,Pb as X,Qb as Y,R as b,S as h,Sb as Z,T as p,Tb as ee,Ub as te,Vb as ne,W as N,Yb as ie,_ as s,_b as oe,aa as C,ca as Q,da as V,ea as W,fa as O,fc as T,ga as j,gb as $,gc as k,hc as L,jb as q,mb as B,n as x,oa as D,pa as A,qa as y,s as M,sa as S,t as P,va as R,wb as G,xb as H,yb as w}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-P4YSCN2K.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-7D2EH4XU.js";import"./chunk-MMIXVVWR.js";import"./chunk-SV7S5NYR.js";import{h as F}from"./chunk-B7O3QC5Z.js";var pe=i=>({"full-status":i});function ge(i,u){i&1&&(o(0,"span",17),s(1," - No routing available"),r())}function _e(i,u){if(i&1&&(o(0,"div",4)(1,"div",5),s(2,"Status"),r(),o(3,"div",14),l(4,"ion-icon",15),o(5,"span"),s(6),r(),f(7,ge,2,0,"span",16),r()()),i&2){let e=p();a(3),c("ngClass",j(5,pe,e.center.status==="Full")),a(),c("name",e.center.status==="Full"?"warning-outline":"checkmark-circle-outline")("color",e.center.status==="Full"?"warning":"success"),a(2),_(e.center.status),a(),c("ngIf",e.center.status==="Full")}}var se=(()=>{class i{constructor(e,n,t){this.modalCtrl=e,this.router=n,this.toastCtrl=t}dismiss(){this.modalCtrl.dismiss()}viewOnMap(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,viewOnly:"true"}}),this.toastCtrl.create({message:`Showing ${this.center.name} on map`,duration:2e3,color:"success"}).then(e=>e.present())}getDirections(){if(this.center.routing_available===!1){this.toastCtrl.create({message:`${this.center.name} is currently full. Routing is not available.`,duration:3e3,color:"warning"}).then(e=>e.present());return}this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,directions:"true"}}),this.toastCtrl.create({message:`Getting directions to ${this.center.name}`,duration:2e3,color:"success"}).then(e=>e.present())}getDisasterTypeIcon(e){if(!e)return"alert-circle-outline";let n=e.toLowerCase();return e.startsWith("Others:")?"help-circle-outline":n.includes("earthquake")||n.includes("quake")?"earth-outline":n.includes("flood")||n.includes("flash")?"water-outline":n.includes("typhoon")||n.includes("storm")?"thunderstorm-outline":n.includes("fire")?"flame-outline":n.includes("landslide")||n.includes("slide")?"triangle-outline":n.includes("others")?"help-circle-outline":"alert-circle-outline"}getStatusColor(e){if(!e)return"medium";let n=e.toLowerCase();return n.includes("active")||n.includes("open")?"success":n.includes("inactive")||n.includes("closed")?"warning":n.includes("full")?"danger":"medium"}static{this.\u0275fac=function(n){return new(n||i)(g(T),g(I),g(k))}}static{this.\u0275cmp=x({type:i,selectors:[["app-evacuation-center-modal"]],inputs:{center:"center"},standalone:!0,features:[O],decls:24,vars:8,consts:[[1,"modal-container"],[1,"close-button",3,"click"],["name","close-circle","color","danger"],[1,"center-name"],[1,"info-section"],[1,"info-label"],[1,"info-value","contact"],["name","call-outline"],[1,"info-value","address"],["name","location-outline"],["class","info-section",4,"ngIf"],[1,"directions-button"],["expand","block",3,"click","color","disabled"],["slot","start",3,"name"],[1,"info-value","status",3,"ngClass"],[3,"name","color"],["class","full-notice",4,"ngIf"],[1,"full-notice"]],template:function(n,t){n&1&&(o(0,"div",0)(1,"div",1),h("click",function(){return t.dismiss()}),l(2,"ion-icon",2),r(),o(3,"h2",3),s(4),r(),o(5,"div",4)(6,"div",5),s(7,"Contact Number"),r(),o(8,"div",6),l(9,"ion-icon",7),o(10,"span"),s(11),r()()(),o(12,"div",4)(13,"div",5),s(14,"Address"),r(),o(15,"div",8),l(16,"ion-icon",9),o(17,"span"),s(18),r()()(),f(19,_e,8,7,"div",10),o(20,"div",11)(21,"ion-button",12),h("click",function(){return t.getDirections()}),l(22,"ion-icon",13),s(23),r()()()),n&2&&(a(4),_(t.center.name),a(7),_(t.center.contact||"No contact available"),a(7),_(t.center.address),a(),c("ngIf",t.center.status),a(2),c("color",t.center.routing_available===!1?"medium":"primary")("disabled",t.center.routing_available===!1),a(),c("name",t.center.routing_available===!1?"warning-outline":"navigate"),a(),C(" ",t.center.routing_available===!1?"Center Full - No Directions":"Get Directions"," "))},dependencies:[L,w,E,S,D,y],styles:[".modal-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;overflow:hidden;width:100%;max-width:350px;margin:0 auto;position:relative;box-shadow:0 4px 12px #0000001a}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;z-index:10}.close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;background:#fff;border-radius:50%}.center-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;text-align:center;margin:15px 15px 10px;color:#000}.info-section[_ngcontent-%COMP%]{padding:10px 15px;border-bottom:1px solid #f0f0f0}.info-section[_ngcontent-%COMP%]:last-of-type{border-bottom:none}.info-label[_ngcontent-%COMP%]{font-size:14px;color:#09f;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:15px;color:#333}.info-value[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px;min-width:18px}.info-value.contact[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.info-value.address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff4961}.info-value.status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#28a745}.info-value.status.full-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ffc107}.info-value.status[_ngcontent-%COMP%]   .full-notice[_ngcontent-%COMP%]{color:#ffc107;font-size:13px;font-style:italic}.directions-button[_ngcontent-%COMP%]{padding:10px 15px 15px}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 8px;--background: #0099ff;font-weight:500;margin:0}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:5px}"]})}}return i})();function fe(i,u){i&1&&(o(0,"ion-text",12)(1,"p"),s(2,"Search by center name, address, or disaster type"),r()())}function he(i,u){i&1&&(o(0,"div",13),l(1,"ion-spinner",14),o(2,"ion-text",15)(3,"p"),s(4,"Loading evacuation centers..."),r()()())}function Ce(i,u){if(i&1){let e=b();o(0,"div",16),l(1,"ion-icon",17),o(2,"ion-text",18)(3,"p"),s(4),r()(),o(5,"ion-button",19),h("click",function(){M(e);let t=p();return P(t.loadEvacuationCenters())}),l(6,"ion-icon",20),s(7," Try Again "),r()()}if(i&2){let e=p();a(4),_(e.errorMessage)}}function ve(i,u){if(i&1&&(o(0,"ion-badge",26),s(1),r()),i&2){let e=p(2).$implicit;N("color",e.status==="Active"?"success":"warning"),a(),C(" ",e.status," ")}}function xe(i,u){if(i&1&&(o(0,"p")(1,"ion-badge",24),s(2),r(),f(3,ve,2,2,"ion-badge",25),r()),i&2){let e=p().$implicit;a(2),_(e.disaster_type),a(),c("ngIf",e.status)}}function Me(i,u){if(i&1){let e=b();o(0,"ion-item",22),h("click",function(){let t=M(e).$implicit,m=p(2);return P(m.viewOnMap(t))}),l(1,"ion-icon",23),o(2,"ion-label")(3,"h2"),s(4),r(),o(5,"p"),s(6),r(),f(7,xe,4,2,"p",9),r()()}if(i&2){let e=u.$implicit;a(4),_(e.name),a(2),_(e.address),a(),c("ngIf",e.disaster_type)}}function Pe(i,u){if(i&1&&(o(0,"ion-list"),f(1,Me,8,3,"ion-item",21),r()),i&2){let e=p();a(),c("ngForOf",e.locations)}}function be(i,u){if(i&1&&(o(0,"div",27),l(1,"ion-icon",28),o(2,"ion-text",15)(3,"p"),s(4),r()()()),i&2){let e=p();a(4),C('No evacuation centers found matching "',e.searchQuery,'"')}}function Oe(i,u){if(i&1){let e=b();o(0,"div",29),l(1,"ion-icon",30),o(2,"ion-text",15)(3,"h3"),s(4,"Search for Evacuation Centers"),r(),o(5,"p"),s(6,"Enter a name, address, or disaster type to find evacuation centers"),r()(),o(7,"ion-button",31),h("click",function(){M(e);let t=p();return t.searchQuery="all",P(t.onSearch({target:{value:"all"}}))}),s(8," Show All Centers "),r()()}}var Qe=(()=>{class i{constructor(e,n,t,m,d){this.http=e,this.loadingService=n,this.toastCtrl=t,this.router=m,this.modalCtrl=d,this.searchQuery="",this.locations=[],this.allCenters=[],this.isLoading=!1,this.hasError=!1,this.errorMessage=""}ngOnInit(){this.loadEvacuationCenters()}loadEvacuationCenters(){return F(this,null,function*(){yield this.loadingService.showLoading("Loading evacuation centers..."),this.isLoading=!0;try{this.http.get(`${re.apiUrl}/evacuation-centers`).subscribe({next:e=>{console.log("Loaded evacuation centers:",e),this.allCenters=e.data||[],this.isLoading=!1,this.loadingService.dismissLoading()},error:e=>{console.error("Error loading evacuation centers:",e),this.hasError=!0,this.errorMessage="Failed to load evacuation centers. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again later.",duration:3e3,color:"danger"}).then(n=>n.present())}})}catch(e){console.error("Exception loading evacuation centers:",e),this.hasError=!0,this.errorMessage="An unexpected error occurred. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading()}})}onSearch(e){let n=e.target.value.toLowerCase().trim();if(console.log("Searching for:",n),!n){this.locations=[];return}this.locations=this.allCenters.filter(t=>{let m=t.name.toLowerCase().startsWith(n),d=t.name.toLowerCase().includes(n),v=t.address?.toLowerCase().includes(n),z=!1;return t.disaster_type&&(Array.isArray(t.disaster_type)?z=t.disaster_type.some(ce=>ce.toLowerCase().includes(n)):z=t.disaster_type.toLowerCase().includes(n)),m||d||v||z}),this.locations.sort((t,m)=>{let d=t.name.toLowerCase().startsWith(n),v=m.name.toLowerCase().startsWith(n);return d&&!v?-1:!d&&v?1:0})}clearSearch(){this.searchQuery="",this.locations=[]}refreshCenters(e){this.loadEvacuationCenters().then(()=>{e.target.complete()})}viewOnMap(e){return F(this,null,function*(){yield(yield this.modalCtrl.create({component:se,componentProps:{center:e},cssClass:"evacuation-center-modal"})).present()})}static{this.\u0275fac=function(n){return new(n||i)(g(R),g(ae),g(k),g(I),g(T))}}static{this.\u0275cmp=x({type:i,selectors:[["app-search"]],standalone:!0,features:[O],decls:14,vars:8,consts:[[3,"translucent"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","pullingText","Pull to refresh","refreshingSpinner","circles","refreshingText","Refreshing..."],[1,"search-container"],["placeholder","Search evacuation centers by name","animated","true","showCancelButton","focus","debounce","300",3,"ngModelChange","ionInput","ionClear","ngModel"],["color","medium","class","search-hint",4,"ngIf"],[1,"search-results"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],[4,"ngIf"],["class","no-results",4,"ngIf"],["class","empty-state",4,"ngIf"],["color","medium",1,"search-hint"],[1,"loading-container"],["name","circles"],["color","medium"],[1,"error-container"],["name","alert-circle-outline","color","danger","size","large"],["color","danger"],["fill","outline","size","small",3,"click"],["name","refresh-outline","slot","start"],["button","","detail","",3,"click",4,"ngFor","ngForOf"],["button","","detail","",3,"click"],["name","location-outline","slot","start","color","primary"],["color","secondary"],[3,"color",4,"ngIf"],[3,"color"],[1,"no-results"],["name","search-outline","color","medium","size","large"],[1,"empty-state"],["name","search","color","primary","size","large"],["fill","outline",3,"click"]],template:function(n,t){n&1&&(o(0,"ion-header",0),l(1,"ion-toolbar"),r(),o(2,"ion-content")(3,"ion-refresher",1),h("ionRefresh",function(d){return t.refreshCenters(d)}),l(4,"ion-refresher-content",2),r(),o(5,"div",3)(6,"ion-searchbar",4),W("ngModelChange",function(d){return V(t.searchQuery,d)||(t.searchQuery=d),d}),h("ionInput",function(d){return t.onSearch(d)})("ionClear",function(){return t.clearSearch()}),r(),f(7,fe,3,0,"ion-text",5),r(),o(8,"div",6),f(9,he,5,0,"div",7)(10,Ce,8,1,"div",8)(11,Pe,2,1,"ion-list",9)(12,be,5,1,"div",10)(13,Oe,9,0,"div",11),r()()),n&2&&(c("translucent",!0),a(6),Q("ngModel",t.searchQuery),a(),c("ngIf",!t.searchQuery),a(2),c("ngIf",t.isLoading),a(),c("ngIf",t.hasError),a(),c("ngIf",t.locations.length>0),a(),c("ngIf",t.searchQuery&&t.locations.length===0&&!t.isLoading&&!t.hasError),a(),c("ngIf",!t.searchQuery&&!t.isLoading&&!t.hasError&&t.allCenters.length>0))},dependencies:[L,H,w,U,J,E,K,X,Y,Z,ee,te,ne,ie,oe,G,S,A,y,B,$,q],styles:["ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-content[_ngcontent-%COMP%]{--background: #f8f9fa}.search-container[_ngcontent-%COMP%]{padding:10px 16px 0}.search-container[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--border-radius: 10px;--box-shadow: 0 2px 6px rgba(0, 0, 0, .1);--placeholder-color: var(--ion-color-medium);--icon-color: var(--ion-color-primary)}.search-container[_ngcontent-%COMP%]   .search-hint[_ngcontent-%COMP%]{font-size:12px;margin:0 0 10px 16px;display:block}.search-results[_ngcontent-%COMP%]{padding:0 16px 16px}.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:32px 16px;min-height:200px}.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:16px}.loading-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:16px}ion-list[_ngcontent-%COMP%]{background:transparent;padding:0}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px;--background: white;margin-bottom:10px;border-radius:10px;--border-radius: 10px;box-shadow:0 2px 4px #0000000d}ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:500;margin-bottom:4px;color:var(--ion-color-dark)}ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium);margin:2px 0}ion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{margin-right:6px;padding:4px 8px;border-radius:4px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:500;margin:8px 0;color:var(--ion-color-dark)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:20px}"]})}}return i})();export{Qe as SearchPage};
