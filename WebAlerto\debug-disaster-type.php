<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

$center = App\Models\Evacuation::first();

echo "=== Debugging Disaster Type ===\n";
echo "Center: {$center->name}\n";
echo "Disaster Type Raw: ";
var_dump($center->disaster_type);
echo "Type: " . gettype($center->disaster_type) . "\n";

if (is_array($center->disaster_type)) {
    echo "Array contents: " . json_encode($center->disaster_type) . "\n";
    echo "Imploded: " . implode(', ', $center->disaster_type) . "\n";
} elseif (is_string($center->disaster_type)) {
    echo "String value: " . $center->disaster_type . "\n";
} else {
    echo "Other type, converting to string: " . json_encode($center->disaster_type) . "\n";
}

echo "=== End Debug ===\n";
