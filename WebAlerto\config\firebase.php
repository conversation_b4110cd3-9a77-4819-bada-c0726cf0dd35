<?php

return [
    'projects' => [
        'app' => [
            'credentials' => [
                'file' => storage_path('app/' . env('FIREBASE_CREDENTIALS', 'firebase-service-account.json')),
            ],
            'database' => [
                'url' => env('FIREBASE_DATABASE_URL', 'https://last-5acaf-default-rtdb.firebaseio.com'),
            ],
            'dynamic_links' => [
                'default_domain' => env('FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN', 'last-5acaf.page.link')
            ],
            'storage' => [
                'default_bucket' => env('FIREBASE_STORAGE_DEFAULT_BUCKET', 'last-5acaf.appspot.com'),
            ],
            'project_id' => env('FIREBASE_PROJECT_ID', 'last-5acaf'),
            'messaging_sender_id' => env('FIREBASE_MESSAGING_SENDER_ID', '************'),
            'app_id' => env('FIREBASE_APP_ID', '1:************:android:c7c81cb0ccca4f30cb7815'),
        ],
    ],
    'batch_size' => env('FIREBASE_BATCH_SIZE', 500),
    'notification' => [
        'categories' => [
            'Earthquake',
            'Flood',
            'Fire',
            'Typhoon',
            'General'
        ],
        'severities' => [
            'low',
            'medium',
            'high'
        ]
    ]
];