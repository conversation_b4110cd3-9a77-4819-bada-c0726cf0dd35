import{a as rt}from"./chunk-NNX4H53H.js";import{a as at,c as gt}from"./chunk-2IVG2Z4K.js";import{a as st}from"./chunk-AYSFXGM6.js";import{b as ot}from"./chunk-DUSKIWGF.js";import{a as it}from"./chunk-LT5RLULD.js";import"./chunk-3J7GGTVR.js";import{a as nt}from"./chunk-G7IUXWVS.js";import"./chunk-ICWJVXBH.js";import{$ as P,Aa as B,Ca as J,D as l,H as v,Hb as Y,I as h,K as k,Lb as H,M as i,Mb as q,N as r,O as f,R as T,S as _,T as u,_ as d,_b as Q,aa as L,cc as Z,d as I,ec as X,fa as R,ga as E,gc as tt,hc as et,m as O,mb as W,n as D,oa as $,pa as z,qa as V,s as b,sa as U,t as y,va as G,yb as j,zb as K}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as A,b as S,g as pt,h as m}from"./chunk-B7O3QC5Z.js";var c=pt(gt());var dt=s=>({"full-status":s});function ut(s,C){if(s&1){let t=T();i(0,"app-real-time-navigation",26),_("routeUpdated",function(e){b(t);let o=u();return y(o.onNavigationRouteUpdated(e))})("navigationStopped",function(){b(t);let e=u();return y(e.onNavigationStopped())}),r()}if(s&2){let t=u();h("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function mt(s,C){if(s&1&&(i(0,"span",36),d(1),r()),s&2){let t=u().$implicit,n=u();l(),L(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function ht(s,C){if(s&1){let t=T();i(0,"div",27),_("click",function(){let e=b(t).$implicit,o=u();return y(o.selectCenterFromList(e))}),i(1,"div",28)(2,"h4"),d(3),r(),i(4,"p",29),d(5),r(),i(6,"div",30),v(7,mt,2,1,"span",31),i(8,"span",32),d(9),r(),i(10,"span",33),d(11),r()()(),i(12,"div",34),f(13,"ion-icon",35),r()()}if(s&2){let t=C.$implicit,n=u();l(3),P(t.name),l(2),P(t.address),l(2),h("ngIf",n.userLocation),l(2),L("\u{1F465} ",t.capacity||"N/A"," capacity"),l(),h("ngClass",E(6,dt,t.routing_available===!1)),l(),L(" ",t.routing_available===!1?"\u{1F534} Full":"\u{1F535} Available"," ")}}function ft(s,C){if(s&1&&(i(0,"div",28)(1,"h3"),d(2),r(),i(3,"p"),d(4),r()()),s&2){let t=u();l(2),P(t.selectedCenter.name),l(2),P(t.selectedCenter.address)}}function _t(s,C){if(s&1&&(i(0,"div",47)(1,"span",48),d(2),r(),i(3,"span",36),d(4),r()()),s&2){let t=u(2);l(2),P(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),l(2),P(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function Ct(s,C){if(s&1&&(i(0,"div",47)(1,"span",48),d(2),r(),i(3,"span",36),d(4),r()()),s&2){let t=u(2);l(2),P(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),l(2),P(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function Mt(s,C){if(s&1&&(i(0,"div",47)(1,"span",48),d(2),r(),i(3,"span",36),d(4),r()()),s&2){let t=u(2);l(2),P(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),l(2),P(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function vt(s,C){if(s&1){let t=T();i(0,"button",49),_("click",function(){b(t);let e=u(2);return y(e.startRealTimeNavigation(e.selectedCenter))}),f(1,"ion-icon",50),i(2,"span"),d(3,"Start Real-time Navigation"),r()()}}function Pt(s,C){if(s&1){let t=T();i(0,"div",37)(1,"div",38),f(2,"ion-icon",39),i(3,"span"),d(4,"Choose Transportation"),r()(),i(5,"div",40)(6,"button",41),_("click",function(){b(t);let e=u();return y(e.selectTransportMode("walking"))}),f(7,"ion-icon",42),i(8,"span"),d(9,"Walk"),r(),v(10,_t,5,2,"div",43),r(),i(11,"button",41),_("click",function(){b(t);let e=u();return y(e.selectTransportMode("cycling"))}),f(12,"ion-icon",44),i(13,"span"),d(14,"Cycle"),r(),v(15,Ct,5,2,"div",43),r(),i(16,"button",41),_("click",function(){b(t);let e=u();return y(e.selectTransportMode("driving"))}),f(17,"ion-icon",45),i(18,"span"),d(19,"Drive"),r(),v(20,Mt,5,2,"div",43),r()(),v(21,vt,4,0,"button",46),r()}if(s&2){let t=u();l(6),k("active",t.selectedTransportMode==="walking"),l(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),l(),k("active",t.selectedTransportMode==="cycling"),l(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),l(),k("active",t.selectedTransportMode==="driving"),l(4),h("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),l(),h("ngIf",t.selectedTransportMode&&t.routeInfo&&t.selectedCenter)}}function xt(s,C){if(s&1&&(i(0,"span",48),d(1),r()),s&2){let t=u(2);l(),L(" ",t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)," ")}}function Ot(s,C){if(s&1&&(i(0,"span",36),d(1),r()),s&2){let t=u(2);l(),L(" (",t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance),") ")}}function bt(s,C){if(s&1){let t=T();i(0,"div",51)(1,"div",52)(2,"div",53),f(3,"ion-icon",54),r(),i(4,"div",55)(5,"div",56),d(6),r(),i(7,"div",57),v(8,xt,2,1,"span",58)(9,Ot,2,1,"span",31),r()()(),i(10,"div",59)(11,"ion-button",60),_("click",function(){b(t);let e=u();return y(e.startRealTimeNavigation(e.selectedCenter))}),f(12,"ion-icon",61),d(13," Start "),r()()()}if(s&2){let t=u();l(3),h("name",t.selectedTransportMode==="walking"?"walk-outline":t.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),l(3),P(t.selectedCenter.name),l(2),h("ngIf",t.routeInfo[t.selectedTransportMode]),l(),h("ngIf",t.routeInfo[t.selectedTransportMode])}}var Gt=(()=>{class s{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.shouldAutoRouteEmergency=!1,this.currentNavigationRoute=null,this.loadingCtrl=O(X),this.toastCtrl=O(tt),this.alertCtrl=O(Z),this.http=O(G),this.router=O(J),this.route=O(B),this.osmRouting=O(it),this.mapboxRouting=O(at),this.enhancedDownload=O(rt)}ngOnInit(){console.log("\u{1F535} FLOOD MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F535} FLOOD MAP: New center to highlight:",this.newCenterId)),t.emergency==="true"&&t.autoRoute==="true"&&(console.log("\u{1F6A8} Emergency navigation triggered for flood map"),t.notification==="true"&&(console.log("\u{1F4F1} Emergency triggered by notification:",{category:t.category,severity:t.severity,title:t.title,message:t.message}),this.showNotificationEmergencyAlert(t)),this.shouldAutoRouteEmergency=!0)})}ngAfterViewInit(){return m(this,null,function*(){console.log("\u{1F535} FLOOD MAP: View initialized, loading map..."),setTimeout(()=>m(this,null,function*(){yield this.loadFloodMap()}),100)})}loadFloodMap(){return m(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading flood evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield ot.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,o=n.coords.longitude;console.log(`\u{1F535} FLOOD MAP: User location [${e}, ${o}]`),this.userLocation={lat:e,lng:o},this.initializeMap(e,o),yield this.loadFloodCenters(e,o),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing ${this.evacuationCenters.length} flood evacuation centers`,duration:3e3,color:"primary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F535} FLOOD MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFloodMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F535} FLOOD MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("flood-map"))throw console.error("\u{1F535} FLOOD MAP: Container #flood-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=c.map("flood-map").setView([t,n],13),this.addTileLayerWithFallback(),this.userMarker=c.marker([t,n],{icon:c.icon({iconUrl:"assets/myLocation.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFloodCenters(t,n){return m(this,null,function*(){try{console.log("\u{1F535} FLOOD MAP: Fetching flood centers...");let e=[];try{e=(yield I(this.http.get(`${nt.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F535} FLOOD MAP: Total centers received from API:",e?.length||0)}catch(o){console.error("\u274C API failed:",o),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(o=>Array.isArray(o.disaster_type)?o.disaster_type.some(a=>a==="Flood"):o.disaster_type==="Flood"),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),console.log("\u{1F535} FLOOD MAP: Filtered centers:",this.evacuationCenters.map(o=>`${o.name} (${JSON.stringify(o.disaster_type)})`)),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Flood Centers",message:"No flood evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(o=>{let a=Number(o.latitude),p=Number(o.longitude);if(!isNaN(a)&&!isNaN(p)){let g=c.marker([a,p],{icon:c.icon({iconUrl:"assets/forFlood.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),M=this.calculateDistance(t,n,a,p);g.on("click",()=>{console.log("\u{1F30A} FLOOD: Marker clicked for center:",o.name),o.routing_available===!1?this.alertCtrl.create({header:"Center Full",message:`${o.name} is currently full. Routing is not available.`,buttons:["OK"]}).then(N=>N.present()):this.showNavigationPanel(o)});let x=this.newCenterId&&o.id.toString()===this.newCenterId,w=o.status||"Active",F=o.routing_available===!1,lt=F?"\u{1F534}":"\u{1F535}",ct=F?"<p><em>\u26A0\uFE0F Center is Full - No routing available</em></p>":"<p><em>Click marker for route options</em></p>";g.bindPopup(`
            <div class="evacuation-popup">
              <h3>${lt} ${o.name} ${x?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Flood Center</p>
              <p><strong>Status:</strong> ${w}</p>
              <p><strong>Distance:</strong> ${(M/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
              ${ct}
              ${x?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),x&&(g.openPopup(),this.map.setView([a,p],15),this.toastCtrl.create({message:`\u{1F195} New flood evacuation center: ${o.name}`,duration:5e3,color:"primary",position:"top"}).then(N=>N.present())),g.addTo(this.map),console.log(`\u{1F535} Added flood marker: ${o.name}`)}}),console.log("\u{1F535} Showing simple markers without auto-routing..."),this.evacuationCenters.length>0){let o=c.latLngBounds([]);o.extend([t,n]),this.evacuationCenters.forEach(a=>{o.extend([Number(a.latitude),Number(a.longitude)])}),this.map.fitBounds(o,{padding:[50,50]})}}catch(e){console.error("\u{1F535} FLOOD MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading flood centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return m(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F535} FLOOD MAP: No user location or evacuation centers available");return}try{console.log("\u{1F535} FLOOD MAP: Finding 2 nearest flood centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No flood evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing routes to ${t.length} nearest flood centers`,duration:4e3,color:"primary"})).present()}catch(t){console.error("\u{1F535} FLOOD MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(o=>S(A({},o),{distance:this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude))})).sort((o,a)=>o.distance-a.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let o=Number(n.latitude),a=Number(n.longitude);if(!isNaN(o)&&!isNaN(a)){let p=c.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #0066CC"></div>
              <img src="assets/forFlood.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),g=c.marker([o,a],{icon:p});g.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Flood</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),g.addTo(this.map),this.nearestMarkers.push(g)}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers&&(this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[]),this.map.eachLayer(t=>{(t instanceof c.GeoJSON||t instanceof c.Polyline||t.options&&(t.options.color==="#0066CC"||t.options.color==="#17a2b8"||t.options.color==="#3880ff"||t.isRouteLayer))&&this.map.removeLayer(t)})}calculateRoutes(t){return m(this,null,function*(){if(this.userLocation){this.routeLayer=c.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n];if(e.routing_available===!1){console.log(`\u{1F535} FLOOD MAP: Skipping route to ${e.name} - routing not available`);continue}let o=Number(e.latitude),a=Number(e.longitude);if(!isNaN(o)&&!isNaN(a))try{console.log(`\u{1F535} FLOOD MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let p=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),g=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,o,p);if(g&&g.routes&&g.routes.length>0){let M=g.routes[0];c.polyline(M.geometry.coordinates.map(w=>[w[1],w[0]]),{color:"#0066CC",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=M.duration,this.routeDistance=M.distance),console.log(`\u2705 FLOOD MAP: Added Mapbox route to ${e.name} (${(M.distance/1e3).toFixed(2)}km, ${(M.duration/60).toFixed(0)}min)`)}}catch(p){console.error(`\u{1F535} Error calculating Mapbox route to center ${n+1}:`,p)}}}})}calculateRoute(t,n){return m(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F535} FLOOD MAP: No user location available for routing");return}let e=this.osmRouting.convertTravelModeToProfile(n),o=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o.routes&&o.routes.length>0){let a=o.routes[0],p=this.osmRouting.convertToGeoJSON(a);this.clearRoutes(),this.routeLayer||(this.routeLayer=c.layerGroup().addTo(this.map));let g=c.geoJSON(p,{style:{color:"#0066CC",weight:4,opacity:.8}});g.isRouteLayer=!0,g.addTo(this.routeLayer),console.log(`\u{1F535} FLOOD MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F535} FLOOD MAP: Error calculating route:",e)}})}showOfflineMarkerInfo(t,n){return m(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Flood Center</p>
          <p><strong>Distance:</strong> ${(n/1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Status:</strong> ${t.status||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,buttons:[{text:"Open in Maps",handler:()=>{this.openInExternalMaps(t)}},{text:"Close",role:"cancel"}]})).present()})}openInExternalMaps(t,n){return m(this,null,function*(){let e=Number(t.latitude),o=Number(t.longitude),a="walking";n==="driving"?a="driving":n==="cycling"&&(a="bicycling");let p=`https://www.google.com/maps/dir/?api=1&destination=${e},${o}&travelmode=${a}`;try{window.open(p,"_system")}catch(g){console.error("Error opening external maps:",g),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}showNavigationPanel(t){return m(this,null,function*(){console.log("\u{1F30A} FLOOD: showNavigationPanel called for:",t.name),console.log("\u{1F30A} FLOOD: Setting selectedCenter to:",t),this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},console.log("\u{1F30A} FLOOD: selectedCenter is now:",this.selectedCenter),yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.showRouteFooter=!1,this.routeInfo={}}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}calculateAllRoutes(t){return m(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let o=this.mapboxRouting.convertTravelModeToProfile(e),a=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),o);if(a.routes&&a.routes.length>0){let p=a.routes[0];this.routeInfo[e]={duration:p.duration,distance:p.distance},console.log(`\u{1F30A} FLOOD: ${e} route calculated - ${(p.distance/1e3).toFixed(2)}km, ${Math.round(p.duration/60)}min`)}}catch(o){console.error(`\u{1F30A} FLOOD: Error calculating ${e} route:`,o)}})}showRouteOnMap(t,n){return m(this,null,function*(){if(this.userLocation){this.clearRoutes();try{let e=this.osmRouting.convertTravelModeToProfile(n),o=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o.routes&&o.routes.length>0){let a=o.routes[0],p=this.osmRouting.convertToGeoJSON(a);this.routeLayer||(this.routeLayer=c.layerGroup().addTo(this.map));let g=c.geoJSON(p,{style:{color:"#0066CC",weight:4,opacity:.8}});g.isRouteLayer=!0,g.addTo(this.routeLayer)}}catch(e){console.error("Error showing route on map:",e)}}})}startNavigation(){return m(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"primary",position:"top"})).present()})}formatTime(t){if(!t)return"";let n=Math.round(t/60);if(n<60)return`${n}m`;{let e=Math.floor(n/60),o=n%60;return`${e}h ${o}m`}}formatDistance(t){return t?t<1e3?`${Math.round(t)}m`:`${(t/1e3).toFixed(1)}km`:""}routeToCenter(t,n){return m(this,null,function*(){if(this.userLocation)try{this.clearRoutes(),console.log(`\u{1F30A} FLOOD: Creating Mapbox route to ${t.name} via ${n}`);let e=this.mapboxRouting.convertTravelModeToProfile(n),o=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o&&o.routes&&o.routes.length>0){let a=o.routes[0],p="#0066CC";this.routeLayer=c.layerGroup().addTo(this.map);let g=c.polyline(a.geometry.coordinates.map(x=>[x[1],x[0]]),{color:p,weight:5,opacity:.8});g.isRouteLayer=!0,g.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F535} Route: ${(a.distance/1e3).toFixed(2)}km, ${(a.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(g.getBounds(),{padding:[50,50]}),console.log(`\u2705 FLOOD: Successfully created route with ${a.geometry.coordinates.length} points`)}}catch(e){console.error("\u{1F30A} Error routing to flood center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,e,o){let p=t*Math.PI/180,g=e*Math.PI/180,M=(e-t)*Math.PI/180,x=(o-n)*Math.PI/180,w=Math.sin(M/2)*Math.sin(M/2)+Math.cos(p)*Math.cos(g)*Math.sin(x/2)*Math.sin(x/2);return 6371e3*(2*Math.atan2(Math.sqrt(w),Math.sqrt(1-w)))}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return m(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F535} Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return m(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("flood-map",this.map,"Flood",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return m(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{yield(yield this.toastCtrl.create({message:"\u{1F30A} Calculating routes to nearest flood centers...",duration:2e3,color:"primary"})).present(),yield this.routeToTwoNearestCenters()}catch(t){console.error("\u{1F30A} Error routing to nearest centers:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistanceInKm(t){if(!this.userLocation)return"N/A";let n=Number(t.latitude),e=Number(t.longitude);return isNaN(n)||isNaN(e)?"N/A":(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,n,e)/1e3).toFixed(1)}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t);let n=Number(t.latitude),e=Number(t.longitude);this.map.setView([n,e],15)}navigateWithMode(t){this.selectedTransportMode=t,this.showRouteFooter=!0,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}addTileLayerWithFallback(){let t=[{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors",name:"OpenStreetMap"},{url:"https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors, \xA9 CartoDB",name:"CartoDB Light"},{url:"https://{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png",attribution:"\xA9 OpenStreetMap contributors, \xA9 OpenStreetMap France",name:"OpenStreetMap France"}],n=0,e=null,o=()=>{if(n>=t.length){console.error("\u{1F535} FLOOD MAP: All tile providers failed, using offline placeholder"),this.addOfflinePlaceholder();return}let a=t[n];console.log(`\u{1F535} FLOOD MAP: Trying tile provider: ${a.name}`),e&&this.map.removeLayer(e),e=c.tileLayer(a.url,{attribution:a.attribution,maxZoom:19,errorTileUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="}),e.on("tileerror",p=>{console.warn(`\u{1F535} FLOOD MAP: Tile error with ${a.name}:`,p),n++,setTimeout(o,1e3)}),e.on("tileload",()=>{console.log(`\u{1F535} FLOOD MAP: Successfully loaded tiles from ${a.name}`)}),e.addTo(this.map)};o()}addOfflinePlaceholder(){let t=document.createElement("canvas");t.width=256,t.height=256;let n=t.getContext("2d");n&&(n.fillStyle="#f0f0f0",n.fillRect(0,0,256,256),n.fillStyle="#999",n.font="14px Arial",n.textAlign="center",n.fillText("Map tiles",128,120),n.fillText("unavailable",128,140));let e=t.toDataURL();c.tileLayer(e,{attribution:"Offline Mode - Map tiles unavailable"}).addTo(this.map)}ionViewWillEnter(){console.log("\u{1F30A} FLOOD MAP: View will enter - refreshing data..."),this.map&&this.userLocation&&this.loadFloodCenters(this.userLocation.lat,this.userLocation.lng)}ionViewWillLeave(){this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){return m(this,null,function*(){if(console.log("\u{1F9ED} Starting real-time navigation to flood center:",t.name),!this.selectedTransportMode){console.error("\u274C No transport mode selected");return}yield this.routeToCenter(t,this.selectedTransportMode),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.closeNavigationPanel(),this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name} via ${this.selectedTransportMode}`,duration:3e3,color:"primary"}).then(n=>n.present()),console.log("\u2705 Flood map real-time navigation setup complete")})}onNavigationRouteUpdated(t){console.log("\u{1F504} Flood map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Flood map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=c.geoJSON(n,{style:{color:"#17a2b8",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}showNotificationEmergencyAlert(t){return m(this,null,function*(){yield(yield this.alertCtrl.create({header:"\u{1F30A} FLOOD EMERGENCY",subHeader:t.title||"Emergency Notification",message:`
        <div style="text-align: left;">
          <p><strong>Alert:</strong> ${t.message||"Flood emergency detected"}</p>
          <p><strong>Severity:</strong> ${(t.severity||"medium").toUpperCase()}</p>
          <p><strong>Action:</strong> Routing to nearest flood evacuation centers</p>
        </div>
      `,buttons:[{text:"Navigate Now",role:"confirm",cssClass:"alert-button-confirm",handler:()=>{console.log("\u{1F6A8} User confirmed emergency navigation for flood")}},{text:"View Map Only",role:"cancel",cssClass:"alert-button-cancel",handler:()=>{console.log("\u{1F4CD} User chose to view flood map without auto-routing"),this.shouldAutoRouteEmergency=!1}}],cssClass:"emergency-alert"})).present()})}static{this.\u0275fac=function(n){return new(n||s)}}static{this.\u0275cmp=D({type:s,selectors:[["app-flood-map"]],standalone:!0,features:[R],decls:36,vars:14,consts:[[3,"translucent"],["color","primary"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","flood-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForFlood.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForFlood.png","alt","Download",1,"control-icon"],["src","assets/compassForFlood.png","alt","Route to Nearest",1,"control-icon"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["fill","clear","size","small",3,"click"],["name","close-outline"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],["class","footer-content",4,"ngIf"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"status",3,"ngClass"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["class","route-info",4,"ngIf"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"route-info"],[1,"time"],[1,"start-navigation-btn",3,"click"],["name","navigate"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],[1,"route-info-footer"],["class","time",4,"ngIf"],[1,"footer-actions"],["fill","solid","color","primary","size","small",3,"click"],["name","navigate","slot","start"]],template:function(n,e){n&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),_("click",function(){return e.goBack()}),f(4,"ion-icon",4),r()()()(),i(5,"ion-content",5),f(6,"div",6),i(7,"div",7)(8,"ion-button",8),_("click",function(){return e.showAllCenters()}),f(9,"img",9),r(),i(10,"ion-button",8),_("click",function(){return e.downloadMap()}),f(11,"img",10),r(),i(12,"ion-button",8),_("click",function(){return e.routeToNearestCenters()}),f(13,"img",11),r()(),v(14,ut,1,3,"app-real-time-navigation",12),i(15,"div",13)(16,"div",14)(17,"div",15)(18,"div",16)(19,"h3"),d(20,"\u{1F30A} All Flood Centers"),r(),i(21,"p"),d(22),r()(),i(23,"ion-button",17),_("click",function(){return e.closeAllCentersPanel()}),f(24,"ion-icon",18),r()(),i(25,"div",19),v(26,ht,14,8,"div",20),r()()(),i(27,"div",21)(28,"div",14)(29,"div",15),v(30,ft,5,2,"div",22),i(31,"ion-button",17),_("click",function(){return e.closeNavigationPanel()}),f(32,"ion-icon",18),r()(),v(33,Pt,22,10,"div",23),r()(),i(34,"div",24),v(35,bt,14,4,"div",25),r()()),n&2&&(h("translucent",!0),l(5),h("fullscreen",!0),l(9),h("ngIf",e.navigationDestination),l(),k("show",e.showAllCentersPanel),l(7),L("",e.evacuationCenters.length," centers available"),l(4),h("ngForOf",e.evacuationCenters),l(),k("show",e.selectedCenter),l(3),h("ngIf",e.selectedCenter),l(3),h("ngIf",e.selectedCenter),l(),k("show",e.selectedCenter&&e.selectedTransportMode),l(),h("ngIf",e.selectedCenter&&e.selectedTransportMode))},dependencies:[et,j,K,Y,H,q,Q,U,$,z,V,W,st],styles:["#flood-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #3880ff;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(56,128,255,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(56, 128, 255, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1500;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-primary);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--ion-color-light);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--ion-color-primary-tint);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);box-shadow:0 2px 8px #00000014;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #3880ff26;border-color:var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;font-weight:500;padding:2px 8px;border-radius:8px;background:var(--ion-color-primary-tint);color:var(--ion-color-primary);width:fit-content}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]{margin-left:12px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1400;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #3880ff;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary-tint);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-primary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-primary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#06c;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px;box-shadow:0 2px 8px #0003}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-primary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#06c;font-size:18px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}"]})}}return s})();export{Gt as FloodMapPage};
