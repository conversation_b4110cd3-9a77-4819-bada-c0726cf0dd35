import{b as l}from"./chunk-TTLEF7YW.js";import{a as d}from"./chunk-G7IUXWVS.js";import{d as n,i as s,m as o,va as c}from"./chunk-Q5Y64KIB.js";import{h as a}from"./chunk-B7O3QC5Z.js";var w=(()=>{class i{constructor(){this.http=o(c),this.offlineStorage=o(l),this.defaultEmergencyContacts=[{id:"pnp-911",name:"Philippine National Police",number:"911",type:"police",description:"Emergency hotline for police assistance",available24h:!0,priority:1},{id:"bfp-911",name:"Bureau of Fire Protection",number:"911",type:"fire",description:"Fire emergency and rescue services",available24h:!0,priority:1},{id:"red-cross",name:"Philippine Red Cross",number:"143",type:"medical",description:"Medical emergency and disaster response",available24h:!0,priority:2},{id:"ndrrmc",name:"NDRRMC",number:"(02) 8911-1406",type:"government",description:"National Disaster Risk Reduction and Management Council",available24h:!0,priority:2},{id:"coast-guard",name:"Philippine Coast Guard",number:"(02) 8527-8481",type:"rescue",description:"Maritime search and rescue operations",available24h:!0,priority:3},{id:"meralco",name:"Meralco Emergency",number:"16211",type:"utility",description:"Power outage and electrical emergency",available24h:!0,priority:4},{id:"maynilad",name:"Maynilad Emergency",number:"1626",type:"utility",description:"Water service emergency",available24h:!0,priority:4},{id:"alerto-support",name:"ALERTO Support",number:"+63 ************",type:"general",description:"ALERTO system support and assistance",available24h:!1,priority:5}],this.defaultDisasterInfo=[{id:"earthquake-prep",disasterType:"Earthquake",title:"Earthquake Preparedness",instructions:["Drop, Cover, and Hold On immediately","Stay away from windows, mirrors, and heavy objects","If outdoors, move away from buildings and power lines","If in a vehicle, pull over and stay inside","Do not run outside during shaking"],preparationSteps:["Secure heavy furniture and appliances","Identify safe spots in each room","Practice earthquake drills regularly","Keep emergency supplies accessible","Know how to turn off gas, water, and electricity"],emergencyKit:["First aid kit and medications","Flashlight and extra batteries","Battery-powered radio","Water (1 gallon per person per day)","Non-perishable food for 3 days","Emergency contact list","Cash and important documents","Whistle for signaling help"]},{id:"flood-prep",disasterType:"Flood",title:"Flood Preparedness",instructions:["Move to higher ground immediately","Avoid walking or driving through flood waters","Stay away from downed power lines","Listen to emergency broadcasts","Do not drink flood water"],preparationSteps:["Know your evacuation routes","Keep sandbags or flood barriers ready","Elevate utilities above potential flood levels","Create a family communication plan","Sign up for flood warnings"],emergencyKit:["Waterproof containers for documents","Life jackets or flotation devices","Water purification tablets","Waterproof flashlight","Emergency food and water","Battery-powered radio","First aid supplies","Emergency contact information"]},{id:"typhoon-prep",disasterType:"Typhoon",title:"Typhoon Preparedness",instructions:["Stay indoors and away from windows","Secure or bring in outdoor objects","Avoid using electrical appliances","Stay in the strongest part of the building","Listen to weather updates continuously"],preparationSteps:["Install storm shutters or board up windows","Trim trees and secure outdoor furniture","Stock up on emergency supplies","Charge all electronic devices","Fill bathtubs and containers with water"],emergencyKit:["Battery-powered radio and flashlights","Extra batteries","Non-perishable food for several days","Water storage containers","First aid kit and medications","Duct tape and plastic sheeting","Emergency contact list","Cash and important documents"]},{id:"fire-prep",disasterType:"Fire",title:"Fire Safety",instructions:["Evacuate immediately when alarm sounds","Stay low to avoid smoke inhalation","Feel doors before opening them","Use stairs, never elevators","Call 911 once you are safe"],preparationSteps:["Install smoke detectors in every room","Create and practice escape plans","Keep fire extinguishers accessible","Clear escape routes of obstacles","Teach children about fire safety"],emergencyKit:["Fire extinguisher (ABC type)","Smoke masks or cloth for breathing","Flashlight for dark conditions","Emergency ladder for upper floors","Important documents in fireproof safe","Emergency contact list","First aid supplies","Battery-powered radio"]}]}initializeEmergencyContacts(){return a(this,null,function*(){try{let e=this.defaultEmergencyContacts,r=this.defaultDisasterInfo;try{let t=yield n(this.http.get(`${d.apiUrl}/emergency-contacts`));t.contacts&&(e=t.contacts),t.disasterInfo&&(r=t.disasterInfo)}catch{console.log("Using default emergency contacts (API not available)")}yield this.offlineStorage.cacheEmergencyContacts(e),yield this.offlineStorage.cacheDisasterInfo(r),console.log(`\u{1F4DE} Initialized ${e.length} emergency contacts`),console.log(`\u{1F32A}\uFE0F Initialized ${r.length} disaster preparedness guides`)}catch(e){console.error("Failed to initialize emergency contacts:",e)}})}getEmergencyContacts(){return a(this,null,function*(){try{let e=yield this.offlineStorage.getCachedEmergencyContacts();return e&&e.length>0?e:(yield this.offlineStorage.cacheEmergencyContacts(this.defaultEmergencyContacts),this.defaultEmergencyContacts)}catch(e){return console.error("Failed to get emergency contacts:",e),this.defaultEmergencyContacts}})}getDisasterInfo(){return a(this,null,function*(){try{let e=yield this.offlineStorage.getCachedDisasterInfo();return e&&e.length>0?e:(yield this.offlineStorage.cacheDisasterInfo(this.defaultDisasterInfo),this.defaultDisasterInfo)}catch(e){return console.error("Failed to get disaster info:",e),this.defaultDisasterInfo}})}getContactsByType(e){return a(this,null,function*(){return(yield this.getEmergencyContacts()).filter(t=>t.type===e).sort((t,p)=>t.priority-p.priority)})}getDisasterInfoByType(e){return a(this,null,function*(){return(yield this.getDisasterInfo()).find(t=>t.disasterType===e)||null})}getHighPriorityContacts(){return a(this,null,function*(){return(yield this.getEmergencyContacts()).filter(r=>r.priority<=2).sort((r,t)=>r.priority-t.priority)})}static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275prov=s({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();export{w as a};
