<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Testing Evacuation Center Status Update ===\n\n";

// Get the first evacuation center
$center = App\Models\Evacuation::first();

if (!$center) {
    echo "❌ No evacuation centers found in database!\n";
    echo "Please add an evacuation center first.\n";
    exit;
}

echo "📍 TESTING WITH EVACUATION CENTER:\n";
echo "   ID: {$center->id}\n";
echo "   Name: {$center->name}\n";
echo "   Current Status: {$center->status}\n";
echo "   Current is_full: " . ($center->is_full ? 'true' : 'false') . "\n";
echo "   Barangay: {$center->barangay}\n\n";

// Check if there are mobile users
$mobileUsers = App\Models\User::where('role', 'mobile_user')->count();
echo "📱 Mobile Users in Database: {$mobileUsers}\n";

// Check active FCM tokens
$activeTokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->count();
echo "🔔 Active FCM Tokens: {$activeTokens}\n\n";

if ($mobileUsers === 0) {
    echo "⚠️  WARNING: No mobile users found. Notifications won't be sent.\n\n";
}

if ($activeTokens === 0) {
    echo "⚠️  WARNING: No valid FCM tokens found. Push notifications won't work.\n\n";
}

// Test 1: Simulate marking center as Full via status field
echo "🧪 TEST 1: Marking center as Full (via status field)...\n";

try {
    $oldStatus = $center->status;
    
    // Update status to Full
    $center->update(['status' => 'Full']);
    
    echo "   ✅ Status updated from '{$oldStatus}' to 'Full'\n";
    
    // Manually trigger the notification (simulating what the controller does)
    if ($oldStatus !== 'Full') {
        echo "   📤 Triggering evacuation center full notification...\n";
        
        // Get the controller and call the notification method
        $controller = new App\Http\Controllers\EvacuationManagementController();
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('sendEvacuationCenterFullNotification');
        $method->setAccessible(true);
        $method->invoke($controller, $center);
        
        echo "   ✅ Notification triggered successfully!\n";
        echo "   📱 Check your mobile device for the notification\n\n";
    } else {
        echo "   ℹ️  Center was already Full, no notification sent\n\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n\n";
}

// Test 2: Reset and test via is_full field
echo "🧪 TEST 2: Resetting center to Available...\n";

try {
    $center->update(['status' => 'Active', 'is_full' => false]);
    echo "   ✅ Center reset to Active and not full\n\n";
} catch (Exception $e) {
    echo "   ❌ Error resetting: " . $e->getMessage() . "\n\n";
}

echo "🧪 TEST 3: Marking center as Full (via is_full field)...\n";

try {
    $oldIsFull = $center->is_full;
    
    // Update is_full to true
    $center->update(['is_full' => true]);
    
    echo "   ✅ is_full updated from " . ($oldIsFull ? 'true' : 'false') . " to true\n";
    
    // Manually trigger the notification
    if (!$oldIsFull) {
        echo "   📤 Triggering evacuation center full notification...\n";
        
        $controller = new App\Http\Controllers\EvacuationManagementController();
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('sendEvacuationCenterFullNotification');
        $method->setAccessible(true);
        $method->invoke($controller, $center);
        
        echo "   ✅ Notification triggered successfully!\n";
        echo "   📱 Check your mobile device for the notification\n\n";
    } else {
        echo "   ℹ️  Center was already full, no notification sent\n\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n\n";
}

// Check recent notifications in database
echo "📋 RECENT NOTIFICATIONS IN DATABASE:\n";
$recentNotifications = App\Models\AppNotification::where('type', 'evacuation_center_full')
    ->orderBy('created_at', 'desc')
    ->take(3)
    ->get(['title', 'message', 'created_at']);

if ($recentNotifications->count() > 0) {
    foreach ($recentNotifications as $notification) {
        echo "   - {$notification->title}: {$notification->message}\n";
        echo "     Created: {$notification->created_at->diffForHumans()}\n";
    }
} else {
    echo "   ❌ No evacuation center full notifications found in database\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "INSTRUCTIONS FOR WEB DASHBOARD TEST:\n";
echo str_repeat("=", 60) . "\n\n";

echo "1. 🌐 Open the web dashboard in your browser\n";
echo "2. 🏥 Go to the evacuation centers page\n";
echo "3. 🔘 Click on an evacuation center's status button\n";
echo "4. ✅ Click 'Mark as Full'\n";
echo "5. 📱 Check your mobile device for notifications\n\n";

echo "If you still don't receive notifications from the web dashboard:\n";
echo "- Check browser console for JavaScript errors\n";
echo "- Verify the AJAX request is being sent\n";
echo "- Check Laravel logs for any errors\n\n";

echo "=== END TEST ===\n";
