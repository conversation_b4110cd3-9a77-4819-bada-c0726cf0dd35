var s=(e,t)=>{e.componentOnReady?e.componentOnReady().then(a=>t(a)):r(()=>t(e))},o=e=>e.componentOnReady!==void 0,u=(e,t=[])=>{let a={};return t.forEach(n=>{e.hasAttribute(n)&&(e.getAttribute(n)!==null&&(a[n]=e.getAttribute(n)),e.removeAttribute(n))}),a};var c=(e,t,a,n)=>e.addEventListener(t,a,n),d=(e,t,a,n)=>e.removeEventListener(t,a,n),l=(e,t=e)=>e.shadowRoot||t,r=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e);var f=e=>{if(e.focus(),e.classList.contains("ion-focusable")){let t=e.closest("ion-app");t&&t.setFocus([e])}};var m=(e,t,a)=>Math.max(e,Math.min(t,a));var b=e=>{if(e){let t=e.changedTouches;if(t&&t.length>0){let a=t[0];return{x:a.clientX,y:a.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}};var h=(e,t)=>{if(e??(e={}),t??(t={}),e===t)return!0;let a=Object.keys(e);if(a.length!==Object.keys(t).length)return!1;for(let n of a)if(!(n in t)||e[n]!==t[n])return!1;return!0};export{s as a,o as b,u as c,c as d,d as e,l as f,r as g,f as h,m as i,b as j,h as k};
