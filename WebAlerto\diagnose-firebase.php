<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Firebase Configuration Diagnosis ===\n\n";

// Check environment variables
echo "1. ENVIRONMENT VARIABLES:\n";
echo "   APP_ENV: " . env('APP_ENV', 'NOT SET') . "\n";
echo "   FIREBASE_PROJECT_ID: " . env('FIREBASE_PROJECT_ID', 'NOT SET') . "\n";
echo "   FIREBASE_CREDENTIALS: " . env('FIREBASE_CREDENTIALS', 'NOT SET') . "\n";

// Check config files
echo "\n2. CONFIG FILES:\n";
$configPath = config_path('firebase.php');
echo "   Firebase config exists: " . (file_exists($configPath) ? 'YES' : 'NO') . "\n";

if (file_exists($configPath)) {
    $config = config('firebase');
    echo "   Project ID from config: " . ($config['projects']['app']['project_id'] ?? 'NOT SET') . "\n";
    echo "   Credentials file path: " . ($config['projects']['app']['credentials']['file'] ?? 'NOT SET') . "\n";
}

// Check for service account files
echo "\n3. SERVICE ACCOUNT FILES:\n";
$possiblePaths = [
    storage_path('app/firebase-credentials.json'),
    storage_path('firebase-credentials.json'),
    base_path('firebase-credentials.json'),
    base_path('storage/app/firebase-credentials.json'),
    base_path('config/firebase-credentials.json')
];

foreach ($possiblePaths as $path) {
    $exists = file_exists($path);
    echo "   $path: " . ($exists ? 'EXISTS' : 'NOT FOUND') . "\n";
    
    if ($exists) {
        try {
            $content = json_decode(file_get_contents($path), true);
            echo "     - Project ID: " . ($content['project_id'] ?? 'NOT FOUND') . "\n";
            echo "     - Type: " . ($content['type'] ?? 'NOT FOUND') . "\n";
            echo "     - Client Email: " . ($content['client_email'] ?? 'NOT FOUND') . "\n";
        } catch (Exception $e) {
            echo "     - ERROR reading file: " . $e->getMessage() . "\n";
        }
    }
}

// Check Laravel storage directory permissions
echo "\n4. STORAGE PERMISSIONS:\n";
$storagePath = storage_path();
echo "   Storage path: $storagePath\n";
echo "   Storage writable: " . (is_writable($storagePath) ? 'YES' : 'NO') . "\n";
echo "   Storage/app exists: " . (is_dir(storage_path('app')) ? 'YES' : 'NO') . "\n";
echo "   Storage/app writable: " . (is_writable(storage_path('app')) ? 'YES' : 'NO') . "\n";

// Test FCM token format
echo "\n5. FCM TOKEN ANALYSIS:\n";
$tokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->get(['id', 'token', 'created_at']);

foreach ($tokens as $token) {
    $tokenValue = $token->token;
    $length = strlen($tokenValue);
    $isValid = $length > 100 && strpos($tokenValue, ':') !== false;
    
    echo "   Token ID {$token->id}:\n";
    echo "     - Length: $length characters\n";
    echo "     - Format: " . ($isValid ? 'VALID FCM FORMAT' : 'INVALID FORMAT') . "\n";
    echo "     - Preview: " . substr($tokenValue, 0, 50) . "...\n";
    echo "     - Created: " . $token->created_at . "\n\n";
}

// Create a sample Firebase credentials file template
echo "\n6. CREATING SAMPLE CREDENTIALS TEMPLATE:\n";
$templatePath = storage_path('app/firebase-credentials-template.json');
$template = [
    "type" => "service_account",
    "project_id" => "last-5acaf",
    "private_key_id" => "YOUR_PRIVATE_KEY_ID",
    "private_key" => "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n",
    "client_email" => "<EMAIL>",
    "client_id" => "YOUR_CLIENT_ID",
    "auth_uri" => "https://accounts.google.com/o/oauth2/auth",
    "token_uri" => "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url" => "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url" => "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40last-5acaf.iam.gserviceaccount.com"
];

try {
    file_put_contents($templatePath, json_encode($template, JSON_PRETTY_PRINT));
    echo "   ✅ Template created at: $templatePath\n";
    echo "   📝 Edit this file with your actual Firebase credentials\n";
} catch (Exception $e) {
    echo "   ❌ Failed to create template: " . $e->getMessage() . "\n";
}

// Instructions
echo "\n=== INSTRUCTIONS TO FIX ===\n\n";
echo "1. GET FIREBASE SERVICE ACCOUNT CREDENTIALS:\n";
echo "   - Go to Firebase Console: https://console.firebase.google.com/\n";
echo "   - Select your project: last-5acaf\n";
echo "   - Go to Project Settings > Service Accounts\n";
echo "   - Click 'Generate new private key'\n";
echo "   - Download the JSON file\n\n";

echo "2. INSTALL CREDENTIALS:\n";
echo "   - Save the downloaded JSON file as: storage/app/firebase-credentials.json\n";
echo "   - Or update your .env file with FIREBASE_CREDENTIALS path\n\n";

echo "3. UPDATE CONFIG:\n";
echo "   - Make sure config/firebase.php points to the correct credentials file\n";
echo "   - Verify the project_id matches: last-5acaf\n\n";

echo "4. TEST MOBILE APP:\n";
echo "   - Make sure your mobile app is running\n";
echo "   - Check if FCM is properly initialized in the mobile app\n";
echo "   - Verify the mobile app is using the same Firebase project\n\n";

echo "5. RE-RUN THIS SCRIPT:\n";
echo "   - After fixing the credentials, run: php diagnose-firebase.php\n";
echo "   - Then test notifications: php test-fcm-notifications.php\n\n";

echo "=== END DIAGNOSIS ===\n";
