import{a as rt}from"./chunk-NNX4H53H.js";import{a as at,c as mt}from"./chunk-2IVG2Z4K.js";import{a as st}from"./chunk-AYSFXGM6.js";import{b as it}from"./chunk-DUSKIWGF.js";import{a as ot}from"./chunk-LT5RLULD.js";import"./chunk-3J7GGTVR.js";import{a as nt}from"./chunk-G7IUXWVS.js";import"./chunk-ICWJVXBH.js";import{$ as f,Ca as G,D as s,H as y,Hb as H,I as P,K as C,Lb as K,M as i,Mb as Y,N as o,O as h,R as T,S as _,T as v,_ as r,_b as Q,aa as x,cc as X,d as D,ec as Z,fa as $,ga as z,gc as tt,hc as et,m as k,mb as B,n as R,oa as q,pa as V,qa as j,s as S,sa as U,t as E,va as W,yb as J}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as N,b as I,g as ut,h as M}from"./chunk-B7O3QC5Z.js";var m=ut(mt());var ht=c=>({"full-status":c});function _t(c,b){if(c&1){let t=T();i(0,"app-real-time-navigation",45),_("routeUpdated",function(n){S(t);let e=v();return E(e.onNavigationRouteUpdated(n))})("navigationStopped",function(){S(t);let n=v();return E(n.onNavigationStopped())}),o()}if(c&2){let t=v();P("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function ft(c,b){if(c&1&&(i(0,"span",55),r(1),o()),c&2){let t=v().$implicit,a=v();s(),x(" \u{1F4CD} ",a.calculateDistanceInKm(t)," km away ")}}function Ct(c,b){if(c&1){let t=T();i(0,"div",46),_("click",function(){let n=S(t).$implicit,e=v();return E(e.selectCenterFromList(n))}),i(1,"div",47)(2,"h4"),r(3),o(),i(4,"p",48),r(5),o(),i(6,"div",49),y(7,ft,2,1,"span",50),i(8,"span",51),r(9),o(),i(10,"span",52),r(11),o()()(),i(12,"div",53),h(13,"ion-icon",54),o()()}if(c&2){let t=b.$implicit,a=v();s(3),f(t.name),s(2),f(t.address),s(2),P("ngIf",a.userLocation),s(2),x("\u{1F465} ",t.capacity||"N/A"," capacity"),s(),P("ngClass",z(6,ht,t.status==="Full"||t.routing_available===!1)),s(),x(" ",t.routing_available===!1||t.status==="Full"?"\u{1F534} Full":"\u{1F7E2} Available"," ")}}function vt(c,b){if(c&1&&(i(0,"div",47)(1,"h3"),r(2),o(),i(3,"p"),r(4),o()()),c&2){let t=v();s(2),f(t.selectedCenter.name),s(2),f(t.selectedCenter.address)}}function Mt(c,b){if(c&1&&(i(0,"div",66)(1,"span",67),r(2),o(),i(3,"span",55),r(4),o()()),c&2){let t=v(2);s(2),f(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),f(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function Pt(c,b){if(c&1&&(i(0,"div",66)(1,"span",67),r(2),o(),i(3,"span",55),r(4),o()()),c&2){let t=v(2);s(2),f(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),s(2),f(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function xt(c,b){if(c&1&&(i(0,"div",66)(1,"span",67),r(2),o(),i(3,"span",55),r(4),o()()),c&2){let t=v(2);s(2),f(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),f(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function Ot(c,b){if(c&1){let t=T();i(0,"button",68),_("click",function(){S(t);let n=v(2);return E(n.startNavigation())}),h(1,"ion-icon",69),i(2,"span"),r(3,"Start Navigation"),o()()}}function yt(c,b){if(c&1){let t=T();i(0,"div",56)(1,"div",57),h(2,"ion-icon",58),i(3,"span"),r(4,"Choose Transportation"),o()(),i(5,"div",59)(6,"button",60),_("click",function(){S(t);let n=v();return E(n.selectTransportMode("walking"))}),h(7,"ion-icon",61),i(8,"span"),r(9,"Walk"),o(),y(10,Mt,5,2,"div",62),o(),i(11,"button",60),_("click",function(){S(t);let n=v();return E(n.selectTransportMode("cycling"))}),h(12,"ion-icon",63),i(13,"span"),r(14,"Cycle"),o(),y(15,Pt,5,2,"div",62),o(),i(16,"button",60),_("click",function(){S(t);let n=v();return E(n.selectTransportMode("driving"))}),h(17,"ion-icon",64),i(18,"span"),r(19,"Drive"),o(),y(20,xt,5,2,"div",62),o()(),y(21,Ot,4,0,"button",65),o()}if(c&2){let t=v();s(6),C("active",t.selectedTransportMode==="walking"),s(4),P("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),C("active",t.selectedTransportMode==="cycling"),s(4),P("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),s(),C("active",t.selectedTransportMode==="driving"),s(4),P("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),P("ngIf",t.selectedTransportMode&&t.routeInfo)}}var jt=(()=>{class c{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.allMarkers=[],this.evacuationCenters=[],this.centerCounts={earthquake:0,typhoon:0,flood:0,fire:0,landslide:0,others:0,multiple:0,total:0},this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.userLocation=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.showAllCentersPanel=!1,this.showFilterPanel=!1,this.currentFilter="all",this.loadingCtrl=k(Z),this.toastCtrl=k(tt),this.alertCtrl=k(X),this.http=k(W),this.router=k(G),this.osmRouting=k(ot),this.mapboxRouting=k(at),this.enhancedDownload=k(rt)}ngOnInit(){return M(this,null,function*(){console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing..."),this.currentFilter="all",this.showFilterPanel=!1,yield this.loadAllMaps()})}loadAllMaps(){return M(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading all evacuation centers...",spinner:"crescent"});yield t.present();try{let a=yield it.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),n=a.coords.latitude,e=a.coords.longitude;this.userLocation={lat:n,lng:e},console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${n}, ${e}]`),this.initializeMap(n,e),yield this.loadAllCenters(n,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,duration:3e3,color:"secondary",position:"top"})).present()}catch(a){yield t.dismiss(),console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map",a),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadAllMaps()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,a){console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${t}, ${a}]`),this.map&&this.map.remove(),this.map=m.map("all-maps").setView([t,a],12),m.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=m.marker([t,a],{icon:m.icon({iconUrl:"assets/myLocation.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadAllCenters(t,a){return M(this,null,function*(){try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");let n=yield D(this.http.get(`${nt.apiUrl}/evacuation-centers`));if(console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:",n.data?.length||0),this.evacuationCenters=n.data||[],this.centerCounts.earthquake=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Earthquake"):e.disaster_type==="Earthquake").length,this.centerCounts.typhoon=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Typhoon"):e.disaster_type==="Typhoon").length,this.centerCounts.flood=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Flood"):e.disaster_type==="Flood"||e.disaster_type==="Flash Flood").length,this.centerCounts.fire=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Fire"):e.disaster_type==="Fire").length,this.centerCounts.landslide=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.includes("Landslide"):e.disaster_type==="Landslide").length,this.centerCounts.others=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.some(d=>d==="Others"||typeof d=="string"&&d.startsWith("Others:")):e.disaster_type==="Others"||typeof e.disaster_type=="string"&&e.disaster_type.startsWith("Others:")).length,this.centerCounts.multiple=this.evacuationCenters.filter(e=>Array.isArray(e.disaster_type)?e.disaster_type.length>1:!1).length,this.centerCounts.total=this.evacuationCenters.length,console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:",this.centerCounts),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Evacuation Centers",message:"No evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.allMarkers.forEach(e=>e.remove()),this.allMarkers=[],this.evacuationCenters.forEach(e=>{let d=Number(e.latitude),g=Number(e.longitude);if(!isNaN(d)&&!isNaN(g)){let l="assets/Location.png",p="\u26AA",u="Others",O=Array.isArray(e.disaster_type)?e.disaster_type:[e.disaster_type];if(O.length>1)l="assets/forMultiple.png",p="\u{1F518}",u="Multiple",console.log(`\u{1F5FA}\uFE0F Multi-type center: ${e.name} supports ${O.join(", ")}`);else{let F=Array.isArray(e.disaster_type)?e.disaster_type[0]:e.disaster_type;if(typeof F=="string"&&F.startsWith("Others:"))l="assets/forOthers.png",p="\u{1F7E3}",u="Others";else switch(F){case"Earthquake":l="assets/forEarthquake.png",p="\u{1F7E0}",u="Earthquake";break;case"Typhoon":l="assets/forTyphoon.png",p="\u{1F7E2}",u="Typhoon";break;case"Flood":l="assets/forFlood.png",p="\u{1F535}",u="Flood";break;case"Fire":l="assets/forFire.png",p="\u{1F534}",u="Fire";break;case"Landslide":l="assets/forLandslide.png",p="\u{1F7E4}",u="Landslide";break;case"Others":l="assets/forOthers.png",p="\u{1F7E3}",u="Others";break;default:l="assets/forOthers.png",p="\u{1F7E3}",u="Others";break}}let w=m.marker([d,g],{icon:m.icon({iconUrl:l,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});w.disasterType=u,w.centerData=e;let lt=this.calculateDistance(t,a,d,g);w.on("click",()=>{this.showNavigationPanel(e)});let ct=Array.isArray(e.disaster_type)?e.disaster_type.join(", "):e.disaster_type||"General",pt=e.status||"Active",L=e.routing_available===!1||e.status==="Full",dt=L?"\u{1F534}":p,gt=L?"<p><em>\u26A0\uFE0F Center is Full - No routing available</em></p>":"<p><em>Click marker for route options</em></p>";w.bindPopup(`
            <div class="evacuation-popup">
              <h3>${dt} ${e.name}</h3>
              <p><strong>Type:</strong> ${ct}</p>
              <p><strong>Status:</strong> ${pt}</p>
              <p><strong>Distance:</strong> ${(lt/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              ${gt}
            </div>
          `),w.addTo(this.map),this.allMarkers.push(w),console.log(`\u{1F5FA}\uFE0F Added ${e.disaster_type} marker: ${e.name}`)}}),this.evacuationCenters.length>0){let e=m.latLngBounds([]);e.extend([t,a]),this.evacuationCenters.forEach(d=>{e.extend([Number(d.latitude),Number(d.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(n){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers",n),yield(yield this.toastCtrl.create({message:"Error loading evacuation centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,a,n,e){let g=t*Math.PI/180,l=n*Math.PI/180,p=(n-t)*Math.PI/180,u=(e-a)*Math.PI/180,O=Math.sin(p/2)*Math.sin(p/2)+Math.cos(g)*Math.cos(l)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(O),Math.sqrt(1-O)))}routeToTwoNearestCenters(){return M(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");return}try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing routes to ${t.length} nearest centers via ${this.travelMode}`,duration:3e3,color:"success",position:"top"})).present()}catch(t){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,a){return this.evacuationCenters.map(e=>I(N({},e),{distance:this.calculateDistance(t,a,Number(e.latitude),Number(e.longitude))})).sort((e,d)=>e.distance-d.distance).slice(0,2)}addPulsingMarkers(t){t.forEach((a,n)=>{let e=Number(a.latitude),d=Number(a.longitude);if(!isNaN(e)&&!isNaN(d)){let g="assets/Location.png",l="#3880ff";typeof a.disaster_type=="string"&&a.disaster_type.startsWith("Others:")?(g="assets/forOthers.png",l="#9333ea"):a.disaster_type==="Earthquake"?(g="assets/forEarthquake.png",l="#ff9500"):a.disaster_type==="Typhoon"?(g="assets/forTyphoon.png",l="#2dd36f"):a.disaster_type==="Flood"?(g="assets/forFlood.png",l="#3dc2ff"):a.disaster_type==="Fire"?(g="assets/forFire.png",l="#ef4444"):a.disaster_type==="Landslide"?(g="assets/forLandslide.png",l="#8b5a2b"):a.disaster_type==="Others"&&(g="assets/forOthers.png",l="#9333ea");let p=m.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${l}"></div>
              <img src="${g}" class="marker-icon" />
              <div class="marker-label">${n+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=m.marker([e,d],{icon:p});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${n+1}</h3>
            <h4>${a.name}</h4>
            <p><strong>Type:</strong> ${a.disaster_type}</p>
            <p><strong>Distance:</strong> ${(a.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${a.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}calculateRoutes(t){return M(this,null,function*(){if(this.userLocation){this.routeLayer=m.layerGroup().addTo(this.map);for(let a=0;a<t.length;a++){let n=t[a],e=Number(n.latitude),d=Number(n.longitude);if(!isNaN(e)&&!isNaN(d))try{let g=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,d,e,g);if(l&&l.routes&&l.routes.length>0){let p=l.routes[0],u="#3880ff";n.disaster_type==="Earthquake"?u="#ff9500":n.disaster_type==="Typhoon"?u="#2dd36f":n.disaster_type==="Flash Flood"&&(u="#3dc2ff"),m.polyline(p.geometry.coordinates.map(A=>[A[1],A[0]]),{color:u,weight:4,opacity:.8,dashArray:a===0?void 0:"10, 10"}).addTo(this.routeLayer),a===0&&(this.routeTime=p.duration,this.routeDistance=p.distance),console.log(`\u{1F5FA}\uFE0F Route ${a+1}: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min`)}}catch(g){console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${a+1}:`,g)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.map.eachLayer(t=>{(t instanceof m.GeoJSON||t instanceof m.Polyline||t.options&&(t.options.color||t.isRouteLayer))&&(["#ff9500","#2dd36f","#3dc2ff","#3880ff","#008000","#0066CC","#ef4444","#dc3545","#ffa500","#17a2b8","#007bff"].includes(t.options.color)||t.isRouteLayer||t.isNavigationRoute)&&this.map.removeLayer(t)}),this.routeTime=0,this.routeDistance=0}changeTravelMode(t){return M(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showNavigationPanel(t){return M(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={}}selectTransportMode(t){return M(this,null,function*(){this.selectedTransportMode=t,this.selectedCenter&&this.routeInfo[t]&&(yield this.routeToCenter(this.selectedCenter,t))})}calculateAllRoutes(t){return M(this,null,function*(){if(!this.userLocation)return;if(t.routing_available===!1){console.log(`Skipping route calculation for ${t.name} - routing not available`);return}let a=Number(t.latitude),n=Number(t.longitude);if(isNaN(a)||isNaN(n))return;let e=["walking","cycling","driving"];for(let d of e)try{let g=this.mapboxRouting.convertTravelModeToProfile(d),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,n,a,g);if(l&&l.routes&&l.routes.length>0){let p=l.routes[0];this.routeInfo[d]={duration:p.duration,distance:p.distance}}}catch(g){console.error(`Error calculating ${d} route:`,g)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let a=t/1e3;return a<1?`${Math.round(t)} m`:`${a.toFixed(1)} km`}startNavigation(){return M(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"success",position:"top"})).present()})}routeToCenter(t,a){return M(this,null,function*(){if(this.userLocation){if(t.routing_available===!1){yield(yield this.toastCtrl.create({message:`\u26A0\uFE0F Routing not available for ${t.name} - Center is currently full`,duration:3e3,color:"warning",position:"top"})).present();return}try{this.clearRoutes();let n=Number(t.latitude),e=Number(t.longitude);if(!isNaN(n)&&!isNaN(e)){let d=this.mapboxRouting.convertTravelModeToProfile(a),g=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,n,d);if(g&&g.routes&&g.routes.length>0){let l=g.routes[0],p="#3880ff",u="\u{1F535}";t.disaster_type==="Earthquake"?(p="#ff9500",u="\u{1F7E0}"):t.disaster_type==="Typhoon"?(p="#2dd36f",u="\u{1F7E2}"):t.disaster_type==="Flash Flood"&&(p="#3dc2ff",u="\u{1F535}"),this.routeLayer=m.layerGroup().addTo(this.map);let O=m.polyline(l.geometry.coordinates.map(w=>[w[1],w[0]]),{color:p,weight:5,opacity:.8});O.isRouteLayer=!0,O.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`${u} Route: ${(l.distance/1e3).toFixed(2)}km, ${(l.duration/60).toFixed(0)}min via ${a}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(O.getBounds(),{padding:[50,50]})}}}catch(n){console.error("\u{1F5FA}\uFE0F Error routing to center:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}}})}goBack(){if(this.showFilterPanel){this.closeFilterPanel();return}if(this.showAllCentersPanel){this.closeAllCentersPanel();return}if(this.selectedCenter){this.closeNavigationPanel();return}this.router.navigate(["/tabs/home"])}showAllCenters(){this.showAllCentersPanel=!0,this.selectedCenter=null}closeAllCentersPanel(){this.showAllCentersPanel=!1}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t)}calculateDistanceInKm(t){return this.userLocation?(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude))/1e3).toFixed(1):"N/A"}downloadMap(){return M(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("all-maps",this.map,"All-Evacuation-Centers",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(a=>a.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} All maps navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F All maps real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let a=this.osmRouting.convertToGeoJSON(t),n=m.geoJSON(a,{style:{color:"#007bff",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);n.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}toggleFilterPanel(){this.showFilterPanel=!this.showFilterPanel,this.showFilterPanel&&(this.showAllCentersPanel=!1,this.selectedCenter=null)}closeFilterPanel(){this.showFilterPanel=!1}applyFilter(t){return M(this,null,function*(){console.log(`\u{1F3AF} Applying filter: ${t}`),this.currentFilter=t,yield(yield this.loadingCtrl.create({message:`Filtering ${t==="all"?"all":t} centers...`,duration:1e3})).present(),this.allMarkers.forEach(l=>{let p=l.disasterType;t==="all"?this.map.hasLayer(l)||l.addTo(this.map):p===t?this.map.hasLayer(l)||l.addTo(this.map):this.map.hasLayer(l)&&this.map.removeLayer(l)}),this.closeFilterPanel();let n=this.allMarkers.filter(l=>t==="all"?!0:l.disasterType===t).length,e=`\u{1F3AF} Showing ${n} ${t==="all"?"evacuation centers":t+" centers"}`,d="primary";n===0&&t!=="all"&&(e=`\u26A0\uFE0F No ${t} evacuation centers found`,d="warning"),yield(yield this.toastCtrl.create({message:e,duration:3e3,color:d,position:"top"})).present()})}static{this.\u0275fac=function(a){return new(a||c)}}static{this.\u0275cmp=R({type:c,selectors:[["app-all-maps"]],standalone:!0,features:[$],decls:158,vars:48,consts:[[3,"translucent"],["color","secondary"],[3,"fullscreen"],["id","all-maps",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/allPin.png","alt","Filter Pins",1,"control-icon"],["src","assets/ListOfEvacuationArea.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForSeeWholeMap.png","alt","Download",1,"control-icon"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],["fill","clear",1,"back-btn",3,"click"],["src","assets/backIcon.png","alt","Back",1,"back-icon"],[1,"header-info"],[1,"disaster-counts"],[1,"count-row"],[1,"disaster-icon"],[1,"disaster-label"],[1,"disaster-count"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[1,"filter-panel"],[1,"filter-options"],[1,"filter-option",3,"click"],[1,"filter-icon"],["src","assets/allPin.png","alt","All",1,"disaster-type-icon"],[1,"filter-info"],[1,"filter-label"],[1,"filter-count"],["src","assets/forEarthquake.png","alt","Earthquake",1,"disaster-type-icon"],["src","assets/forTyphoon.png","alt","Typhoon",1,"disaster-type-icon"],["src","assets/forFlood.png","alt","Flood",1,"disaster-type-icon"],["src","assets/forFire.png","alt","Fire",1,"disaster-type-icon"],["src","assets/forLandslide.png","alt","Landslide",1,"disaster-type-icon"],["src","assets/forOthers.png","alt","Others",1,"disaster-type-icon"],["src","assets/forMultiple.png","alt","Multiple",1,"disaster-type-icon"],[1,"filter-overlay",3,"click"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["fill","clear","size","small",3,"click"],["name","close-outline"],["class","transport-options",4,"ngIf"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"status",3,"ngClass"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["class","route-info",4,"ngIf"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"route-info"],[1,"time"],[1,"start-navigation-btn",3,"click"],["name","navigate"]],template:function(a,n){a&1&&(i(0,"ion-header",0),h(1,"ion-toolbar",1),o(),i(2,"ion-content",2),h(3,"div",3),i(4,"div",4)(5,"ion-button",5),_("click",function(){return n.toggleFilterPanel()}),h(6,"img",6),o(),i(7,"ion-button",5),_("click",function(){return n.showAllCenters()}),h(8,"img",7),o(),i(9,"ion-button",5),_("click",function(){return n.downloadMap()}),h(10,"img",8),o()(),y(11,_t,1,3,"app-real-time-navigation",9),i(12,"div",10)(13,"div",11)(14,"div",12)(15,"ion-button",13),_("click",function(){return n.closeAllCentersPanel()}),h(16,"img",14),o(),i(17,"div",15)(18,"h3"),r(19,"\u{1F5FA}\uFE0F All Evacuation Centers"),o(),i(20,"p"),r(21),o()()(),i(22,"div",16)(23,"div",17)(24,"span",18),r(25,"\u{1F7E0}"),o(),i(26,"span",19),r(27,"Earthquake:"),o(),i(28,"span",20),r(29),o()(),i(30,"div",17)(31,"span",18),r(32,"\u{1F7E2}"),o(),i(33,"span",19),r(34,"Typhoon:"),o(),i(35,"span",20),r(36),o()(),i(37,"div",17)(38,"span",18),r(39,"\u{1F535}"),o(),i(40,"span",19),r(41,"Flood:"),o(),i(42,"span",20),r(43),o()(),i(44,"div",17)(45,"span",18),r(46,"\u{1F534}"),o(),i(47,"span",19),r(48,"Fire:"),o(),i(49,"span",20),r(50),o()(),i(51,"div",17)(52,"span",18),r(53,"\u{1F7E4}"),o(),i(54,"span",19),r(55,"Landslide:"),o(),i(56,"span",20),r(57),o()(),i(58,"div",17)(59,"span",18),r(60,"\u{1F7E3}"),o(),i(61,"span",19),r(62,"Others:"),o(),i(63,"span",20),r(64),o()(),i(65,"div",17)(66,"span",18),r(67,"\u{1F518}"),o(),i(68,"span",19),r(69,"Multiple:"),o(),i(70,"span",20),r(71),o()()(),i(72,"div",21),y(73,Ct,14,8,"div",22),o()()(),i(74,"div",23),_("click",function(){return n.closeAllCentersPanel()}),o(),i(75,"div",24)(76,"div",11)(77,"div",12)(78,"ion-button",13),_("click",function(){return n.closeFilterPanel()}),h(79,"img",14),o(),i(80,"div",15)(81,"h3"),r(82,"\u{1F3AF} Filter Pins"),o(),i(83,"p"),r(84,"Show/hide pins by disaster type"),o()()(),i(85,"div",25)(86,"div",26),_("click",function(){return n.applyFilter("all")}),i(87,"div",27),h(88,"img",28),o(),i(89,"div",29)(90,"span",30),r(91,"Show All Pins"),o(),i(92,"span",31),r(93),o()()(),i(94,"div",26),_("click",function(){return n.applyFilter("Earthquake")}),i(95,"div",27),h(96,"img",32),o(),i(97,"div",29)(98,"span",30),r(99,"Earthquake Centers"),o(),i(100,"span",31),r(101),o()()(),i(102,"div",26),_("click",function(){return n.applyFilter("Typhoon")}),i(103,"div",27),h(104,"img",33),o(),i(105,"div",29)(106,"span",30),r(107,"Typhoon Centers"),o(),i(108,"span",31),r(109),o()()(),i(110,"div",26),_("click",function(){return n.applyFilter("Flood")}),i(111,"div",27),h(112,"img",34),o(),i(113,"div",29)(114,"span",30),r(115,"Flood Centers"),o(),i(116,"span",31),r(117),o()()(),i(118,"div",26),_("click",function(){return n.applyFilter("Fire")}),i(119,"div",27),h(120,"img",35),o(),i(121,"div",29)(122,"span",30),r(123,"Fire Centers"),o(),i(124,"span",31),r(125),o()()(),i(126,"div",26),_("click",function(){return n.applyFilter("Landslide")}),i(127,"div",27),h(128,"img",36),o(),i(129,"div",29)(130,"span",30),r(131,"Landslide Centers"),o(),i(132,"span",31),r(133),o()()(),i(134,"div",26),_("click",function(){return n.applyFilter("Others")}),i(135,"div",27),h(136,"img",37),o(),i(137,"div",29)(138,"span",30),r(139,"Other Disasters"),o(),i(140,"span",31),r(141),o()()(),i(142,"div",26),_("click",function(){return n.applyFilter("Multiple")}),i(143,"div",27),h(144,"img",38),o(),i(145,"div",29)(146,"span",30),r(147,"Multiple Types"),o(),i(148,"span",31),r(149),o()()()()()(),i(150,"div",39),_("click",function(){return n.closeFilterPanel()}),o(),i(151,"div",40)(152,"div",11)(153,"div",12),y(154,vt,5,2,"div",41),i(155,"ion-button",42),_("click",function(){return n.closeNavigationPanel()}),h(156,"ion-icon",43),o()(),y(157,yt,22,10,"div",44),o()()()),a&2&&(P("translucent",!0),s(2),P("fullscreen",!0),s(9),P("ngIf",n.navigationDestination),s(),C("show",n.showAllCentersPanel),s(9),x("",n.centerCounts.total," centers available"),s(8),f(n.centerCounts.earthquake),s(7),f(n.centerCounts.typhoon),s(7),f(n.centerCounts.flood),s(7),f(n.centerCounts.fire),s(7),f(n.centerCounts.landslide),s(7),f(n.centerCounts.others),s(7),f(n.centerCounts.multiple),s(2),P("ngForOf",n.evacuationCenters),s(),C("show",n.showAllCentersPanel),s(),C("show",n.showFilterPanel),s(11),C("active",n.currentFilter==="all"),s(7),x("",n.centerCounts.total," centers"),s(),C("active",n.currentFilter==="Earthquake"),s(7),x("",n.centerCounts.earthquake," centers"),s(),C("active",n.currentFilter==="Typhoon"),s(7),x("",n.centerCounts.typhoon," centers"),s(),C("active",n.currentFilter==="Flood"),s(7),x("",n.centerCounts.flood," centers"),s(),C("active",n.currentFilter==="Fire"),s(7),x("",n.centerCounts.fire," centers"),s(),C("active",n.currentFilter==="Landslide"),s(7),x("",n.centerCounts.landslide," centers"),s(),C("active",n.currentFilter==="Others"),s(7),x("",n.centerCounts.others," centers"),s(),C("active",n.currentFilter==="Multiple"),s(7),x("",n.centerCounts.multiple," centers"),s(),C("show",n.showFilterPanel),s(),C("show",n.selectedCenter),s(3),P("ngIf",n.selectedCenter),s(3),P("ngIf",n.selectedCenter))},dependencies:[et,J,H,K,Y,Q,U,q,V,j,B,st],styles:["#all-maps[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:95px;right:15px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: var(--ion-color-primary);--border-radius: 12px;width:48px;height:48px;box-shadow:0 2px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.control-btn[_ngcontent-%COMP%]   .control-icon[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:350px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:20px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light);gap:12px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-medium);margin:0;padding:8px;min-width:auto;width:auto;height:auto}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-secondary);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]{margin-bottom:20px;padding:16px;background:var(--ion-color-light);border-radius:12px}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:6px 0;font-size:14px}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{font-size:16px;width:20px;text-align:center}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-dark);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%]{font-weight:600;color:var(--ion-color-secondary);min-width:24px;text-align:right}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:8px;background:#fff;border:1px solid var(--ion-color-light);border-radius:12px;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{background:var(--ion-color-light);border-color:var(--ion-color-primary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#0000004d;z-index:1999;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}.filter-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:350px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.filter-panel.show[_ngcontent-%COMP%]{right:0}.filter-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:20px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light);gap:12px}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-medium);margin:0;padding:8px;min-width:auto;width:auto;height:auto}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-secondary);line-height:1.2}.filter-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:8px;background:#fff;border:2px solid var(--ion-color-light);border-radius:12px;cursor:pointer;transition:all .2s ease;gap:12px}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]:hover{background:var(--ion-color-light);border-color:var(--ion-color-primary-tint)}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option.active[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-weight:600}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]{width:40px;height:40px;display:flex;align-items:center;justify-content:center}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]   .disaster-type-icon[_ngcontent-%COMP%]{width:32px;height:32px;object-fit:contain}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]   .filter-info[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:2px}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]   .filter-info[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);line-height:1.2}.filter-panel[_ngcontent-%COMP%]   .filter-options[_ngcontent-%COMP%]   .filter-option[_ngcontent-%COMP%]   .filter-info[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%]{font-size:13px;color:var(--ion-color-medium)}.filter-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#0000004d;z-index:1999;opacity:0;visibility:hidden;transition:all .3s ease}.filter-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.transport-controls[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;z-index:1000;max-width:280px}.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-light-rgb), .3);border-radius:8px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border-radius:50%;opacity:.6;animation:_ngcontent-%COMP%_pulse 2s infinite;z-index:1}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:40px;height:40px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:var(--ion-color-primary);color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary-tint);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-primary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-primary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-primary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-secondary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:4px 0;color:var(--ion-color-dark);font-size:14px;font-weight:500}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success);font-size:18px}"]})}}return c})();export{jt as AllMapsPage};
