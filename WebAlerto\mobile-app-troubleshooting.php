<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Mobile App FCM Troubleshooting Guide ===\n\n";

// Check current FCM tokens
echo "1. CURRENT FCM TOKENS IN DATABASE:\n";
echo "-----------------------------------\n";

$tokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->get(['id', 'user_id', 'token', 'device_type', 'created_at', 'last_used_at']);

if ($tokens->count() > 0) {
    foreach ($tokens as $token) {
        $tokenAge = $token->created_at->diffForHumans();
        $lastUsed = $token->last_used_at ? $token->last_used_at->diffForHumans() : 'Never';
        
        echo "   Token ID: {$token->id}\n";
        echo "   User ID: {$token->user_id}\n";
        echo "   Device: {$token->device_type}\n";
        echo "   Created: {$tokenAge}\n";
        echo "   Last Used: {$lastUsed}\n";
        echo "   Token: " . substr($token->token, 0, 50) . "...\n";
        echo "   ---\n";
    }
} else {
    echo "   ❌ No valid FCM tokens found!\n";
    echo "   This means your mobile app is not properly registering FCM tokens.\n";
}

echo "\n2. MOBILE APP CHECKLIST:\n";
echo "------------------------\n";
echo "Please check the following in your mobile app:\n\n";

echo "   📱 FIREBASE CONFIGURATION:\n";
echo "   - Verify google-services.json contains project_id: 'last-5acaf'\n";
echo "   - Check if Firebase is properly initialized in your app\n";
echo "   - Ensure FCM plugin is installed and configured\n\n";

echo "   🔔 NOTIFICATION PERMISSIONS:\n";
echo "   - Check if notification permissions are granted\n";
echo "   - Go to device Settings > Apps > WebAlerto > Notifications\n";
echo "   - Make sure notifications are enabled\n\n";

echo "   🔄 TOKEN REGISTRATION:\n";
echo "   - Check if FCM token is being retrieved in the app\n";
echo "   - Verify token is being sent to backend API\n";
echo "   - Look for any errors in mobile app console/logs\n\n";

echo "   📶 NETWORK & APP STATE:\n";
echo "   - Ensure device has internet connection\n";
echo "   - Keep the app running (foreground or background)\n";
echo "   - Try force-closing and reopening the app\n\n";

echo "3. TESTING STEPS:\n";
echo "-----------------\n";
echo "   Step 1: Open your mobile app\n";
echo "   Step 2: Check if FCM token is registered (should appear in database)\n";
echo "   Step 3: Keep the app open\n";
echo "   Step 4: Run this command to send a test notification:\n";
echo "           php test-fcm-notifications.php\n";
echo "   Step 5: Check if you receive the notification\n\n";

echo "4. ADVANCED DEBUGGING:\n";
echo "----------------------\n";
echo "   If you still don't receive notifications:\n\n";
echo "   A) Check mobile app logs for FCM errors\n";
echo "   B) Verify Firebase project settings match\n";
echo "   C) Test with a different device or emulator\n";
echo "   D) Check if FCM is working in other apps on your device\n\n";

echo "5. SEND TEST NOTIFICATION NOW:\n";
echo "------------------------------\n";
echo "   Would you like to send a test notification right now?\n";
echo "   Make sure your mobile app is open and run:\n\n";
echo "   📝 php test-fcm-notifications.php\n\n";

echo "6. BACKEND STATUS:\n";
echo "------------------\n";
echo "   ✅ Firebase credentials: WORKING\n";
echo "   ✅ FCM service: WORKING\n";
echo "   ✅ Database: WORKING\n";
echo "   ✅ Notifications sent: SUCCESS\n";
echo "   ❓ Mobile app receiving: NEEDS TESTING\n\n";

echo "=== END TROUBLESHOOTING GUIDE ===\n";
echo "\nThe backend is working perfectly. The issue is likely in the mobile app configuration.\n";
echo "Follow the checklist above to resolve the issue.\n\n";
