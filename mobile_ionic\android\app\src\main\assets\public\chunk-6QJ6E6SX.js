import{a as $}from"./chunk-4VT2BRDL.js";import{a as Z}from"./chunk-BCZAIQLD.js";import"./chunk-KE2FHCRG.js";import{a as b}from"./chunk-G7IUXWVS.js";import"./chunk-2GT6F2KJ.js";import"./chunk-ICWJVXBH.js";import{Ca as v,D as m,E as d,Fb as N,H as P,Hb as D,I as M,Lb as U,M as t,Mb as q,N as n,Nb as H,O as x,R as y,S as c,T as C,Yb as Y,Zb as G,_ as o,_b as K,bc as J,ca as p,cc as Q,da as u,ea as h,fa as k,gb as S,hb as E,hc as X,ib as R,jb as I,kb as F,lb as W,mb as A,n as w,nb as z,s as f,sa as T,t as _,va as O,vb as V,wb as j,yb as B,zb as L}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{h as l}from"./chunk-B7O3QC5Z.js";function te(g,ne){if(g&1){let a=y();t(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",17)(3,"strong"),o(4,"Terms and Conditions"),n()(),t(5,"ion-buttons",18)(6,"ion-button",15),c("click",function(){f(a);let e=C();return _(e.closeTermsModal())}),x(7,"ion-icon",19),n()()()(),t(8,"ion-content",20)(9,"div",21)(10,"h1",22)(11,"strong"),o(12,"Terms and Conditions"),n()(),t(13,"p",23),o(14,"Effective Date: April 26, 2025"),n(),t(15,"p",24),o(16,'Welcome to Evacuation Mapping System ("we", "our", or "us"). These '),t(17,"strong"),o(18,"Terms and Conditions"),n(),o(19,' ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.'),n(),t(20,"section")(21,"h2",22),o(22,"1. User Eligibility"),n(),t(23,"p"),o(24,"To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete."),n()(),t(25,"section")(26,"h2",22),o(27,"2. Account Registration"),n(),t(28,"p"),o(29,"You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account."),n()(),t(30,"section")(31,"h2",22),o(32,"3. Use of Service"),n(),t(33,"p"),o(34,"You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account."),n()(),t(35,"section")(36,"h2",22),o(37,"4. Modifications"),n(),t(38,"p"),o(39,"We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes."),n()(),t(40,"section")(41,"h2",22),o(42,"5. Limitation of Liability"),n(),t(43,"p"),o(44,"We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service."),n()(),t(45,"section")(46,"h2",22),o(47,"6. Termination"),n(),t(48,"p"),o(49,"We may suspend or terminate your access to the Service if you violate these Terms."),n()()(),t(50,"div",25)(51,"ion-button",26),c("click",function(){f(a);let e=C();return _(e.closeTermsModal())}),o(52," Close "),n(),t(53,"ion-button",27),c("click",function(){f(a);let e=C();return _(e.acceptTermsFromModal())}),o(54," I Have Read and Accept the Terms and Conditions "),n()()()}}var ue=(()=>{class g{constructor(a,i,e,r,s,ee){this.authService=a,this.router=i,this.http=e,this.platform=r,this.alertController=s,this.fcmService=ee,this.user={email:"",password:"",confirmPassword:""},this.acceptTerms=!1,this.hasReadTerms=!1,this.isTermsModalOpen=!1,this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return l(this,null,function*(){console.log("\u{1F525} Register page initializing..."),yield this.initializeFCMToken()})}onRegister(){return l(this,null,function*(){if(!this.user.email||!this.user.email.trim()){yield this.presentAlert("Registration Failed","Email is required.");return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.user.email)){yield this.presentAlert("Registration Failed","Please enter a valid email address.");return}if(!this.user.password||this.user.password.length<8){yield this.presentAlert("Registration Failed","Password must be at least 8 characters long.");return}if(this.user.password!==this.user.confirmPassword){yield this.presentAlert("Registration Failed","Passwords do not match!");return}if(!this.hasReadTerms||!this.acceptTerms){yield this.presentAlert("Registration Failed","Please read and accept the Terms and Conditions to continue.");return}this.authService.register({email:this.user.email,password:this.user.password,password_confirmation:this.user.confirmPassword}).subscribe({next:i=>l(this,null,function*(){console.log("Registration successful:",i),this.fcmTokenReady&&this.fcmToken&&(yield this.registerTokenWithEndpoints({token:this.fcmToken,device_type:"android",user_id:i.user?.id})),yield this.presentAlert("Registration Successful","Your account has been created successfully. Please log in."),this.router.navigate(["/login"])}),error:i=>l(this,null,function*(){console.error("Registration error:",i);let e="Unknown error occurred";if(i.status===0)e="Cannot connect to server. Please check your network connection and ensure the backend server is running.";else if(i.status===422)if(i.error?.errors){let r=i.error.errors;r.password&&r.password.length>0?e=r.password[0]:r.email&&r.email.length>0?e=r.email[0]:e="Validation failed: "+JSON.stringify(r)}else e=i.error?.message||"Validation failed";else i.status===500?e="Server error: "+(i.error?.message||"Internal server error"):i.error?.message&&(e=i.error.message);yield this.presentAlert("Registration Failed",e)})})})}initializeFCMToken(){return l(this,null,function*(){try{this.platform.is("capacitor")?(console.log("Getting FCM token..."),this.fcmToken=yield this.fcmService.getFCMToken(),this.fcmToken?(this.fcmTokenReady=!0,console.log("FCM token ready:",this.fcmToken)):console.log("No FCM token available")):console.log("FCM not available on this platform")}catch(a){console.error("Error initializing FCM token:",a)}})}registerTokenWithEndpoints(a){return l(this,null,function*(){a.project_id||(a.project_id=b.firebase.projectId);let i=[`${b.apiUrl}/device-token`];for(let e of i)try{let r=yield this.http.post(e,a).toPromise();console.log(`FCM token registered with ${e}:`,r),localStorage.setItem("fcm_token",this.fcmToken);break}catch(r){console.error(`Error registering token with ${e}:`,r)}})}presentAlert(a,i){return l(this,null,function*(){yield(yield this.alertController.create({header:a,message:i,buttons:["OK"]})).present()})}goToLogin(){this.router.navigate(["/login"])}copyToken(){return l(this,null,function*(){try{yield navigator.clipboard.writeText(this.fcmToken),yield(yield this.alertController.create({header:"Copied!",message:"FCM token copied to clipboard",buttons:["OK"]})).present()}catch(a){console.error("Error copying token:",a)}})}openTermsModal(){this.isTermsModalOpen=!0}closeTermsModal(){this.isTermsModalOpen=!1}acceptTermsFromModal(){this.hasReadTerms=!0,this.acceptTerms=!0,this.closeTermsModal()}static{this.\u0275fac=function(i){return new(i||g)(d($),d(v),d(O),d(z),d(Q),d(Z))}}static{this.\u0275cmp=w({type:g,selectors:[["app-register"]],standalone:!0,features:[k],decls:28,vars:7,consts:[[1,"register-bg"],[1,"register-wrapper",2,"margin-top","100px"],[2,"text-align","left","color","black"],[1,"register-form",3,"ngSubmit"],[1,"input-group"],["type","email","name","email","placeholder","Email","required","",1,"modern-input",3,"ngModelChange","ngModel"],["type","password","name","password","placeholder","Password","required","",1,"modern-input",3,"ngModelChange","ngModel"],["type","password","name","confirmPassword","placeholder","Confirm Password","required","",1,"modern-input",3,"ngModelChange","ngModel"],[1,"terms-section"],["name","acceptTerms",1,"terms-checkbox",3,"ngModelChange","ngModel","disabled"],[1,"terms-text"],[1,"terms-link",3,"click"],["expand","block","type","submit",1,"modern-btn",3,"disabled"],[1,"ion-text-left","ion-margin-top"],[2,"color","black"],[3,"click"],[1,"terms-modal",3,"willDismiss","isOpen"],[1,"modal-title"],["slot","end"],["name","close"],[1,"ion-padding"],[1,"terms-content"],[1,"modal-section-title"],[1,"effective-date"],[1,"welcome"],[1,"modal-buttons"],["expand","block","fill","outline",3,"click"],["expand","block",1,"accept-btn",3,"click"]],template:function(i,e){i&1&&(t(0,"ion-content",0)(1,"div",1)(2,"h2",2),o(3,"Sign Up Here!"),n(),t(4,"form",3),c("ngSubmit",function(){return e.onRegister()}),t(5,"div",4)(6,"ion-input",5),h("ngModelChange",function(s){return u(e.user.email,s)||(e.user.email=s),s}),n()(),t(7,"div",4)(8,"ion-input",6),h("ngModelChange",function(s){return u(e.user.password,s)||(e.user.password=s),s}),n()(),t(9,"div",4)(10,"ion-input",7),h("ngModelChange",function(s){return u(e.user.confirmPassword,s)||(e.user.confirmPassword=s),s}),n()(),t(11,"div",8)(12,"ion-checkbox",9),h("ngModelChange",function(s){return u(e.acceptTerms,s)||(e.acceptTerms=s),s}),n(),t(13,"span",10),o(14,"I accept the "),t(15,"a",11),c("click",function(){return e.openTermsModal()}),o(16,"Terms and Conditions"),n()()(),t(17,"ion-button",12),o(18," Sign Up "),n()(),t(19,"div",13)(20,"ion-text",14),o(21,"Already have an account? "),n(),t(22,"a",15),c("click",function(){return e.goToLogin()}),t(23,"strong")(24,"u"),o(25,"Log In"),n()()()()()(),t(26,"ion-modal",16),c("willDismiss",function(){return e.closeTermsModal()}),P(27,te,55,0,"ng-template"),n()),i&2&&(m(6),p("ngModel",e.user.email),m(2),p("ngModel",e.user.password),m(2),p("ngModel",e.user.confirmPassword),m(2),p("ngModel",e.acceptTerms),M("disabled",!e.hasReadTerms),m(5),M("disabled",!e.acceptTerms),m(9),M("isOpen",e.isTermsModalOpen))},dependencies:[X,B,L,N,D,U,q,H,Y,G,K,J,V,j,A,F,S,E,W,I,R,T],styles:[".register-bg[_ngcontent-%COMP%]{--background: white;display:flex;align-items:center;justify-content:center;min-height:100vh}.register-wrapper[_ngcontent-%COMP%]{width:100%;max-width:350px;padding:2rem;margin:0 auto;display:flex;flex-direction:column;align-items:flex-start;text-align:left}.logo-container[_ngcontent-%COMP%]{margin-bottom:3rem}.app-logo[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;background:#fff3;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a}.register-form[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:1.5rem}.input-group[_ngcontent-%COMP%]{width:100%}.modern-input[_ngcontent-%COMP%]{--background: transparent;--color: black;--placeholder-color: rgba(0, 0, 0, .7);--padding-start: 0;--padding-end: 0;border:none;border-bottom:1px solid rgba(0,0,0,.5);border-radius:0;font-size:1rem;padding:12px 0}.modern-input[_ngcontent-%COMP%]:focus-within{border-bottom-color:#000}.forgot-link[_ngcontent-%COMP%]{text-align:right;font-size:.95rem}.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none}.register-link[_ngcontent-%COMP%]{font-size:1rem;color:#444}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none;font-weight:600}.terms-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin:1rem 0;color:#000;font-size:.95rem}.terms-checkbox[_ngcontent-%COMP%]{--background: transparent;--border-color: rgba(0, 0, 0, .7);--checkmark-color: black}.terms-text[_ngcontent-%COMP%]{flex:1;text-align:left}.terms-notice[_ngcontent-%COMP%]{margin-top:8px;text-align:center}.terms-notice[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#000000b3;font-style:italic}.modern-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%;margin-top:1rem}.modern-btn[_ngcontent-%COMP%]:disabled{--background: rgba(255, 255, 255, .5);--color: rgba(79, 172, 254, .5)}.login-link[_ngcontent-%COMP%]{margin-top:1.5rem;color:#000;font-size:1rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:underline;font-weight:600}.terms-link[_ngcontent-%COMP%]{color:#007bff;text-decoration:underline;cursor:pointer;font-weight:600}.terms-link[_ngcontent-%COMP%]:hover{color:#007bffcc}.terms-modal[_ngcontent-%COMP%]{--height: 90%;--border-radius: 16px}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-light)}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   h1.modal-section-title[_ngcontent-%COMP%]{color:#4facfe;text-align:center;margin-bottom:10px;font-size:1.5rem}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .effective-date[_ngcontent-%COMP%]{text-align:center;color:#666;font-style:italic;margin-bottom:20px}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome[_ngcontent-%COMP%]{color:#444;line-height:1.6;margin-bottom:20px;text-align:justify}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]{margin-bottom:20px}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   h2.modal-section-title[_ngcontent-%COMP%]{color:#4facfe;margin-bottom:10px;font-size:1.1rem;font-weight:600}.terms-modal[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#444;line-height:1.6;margin-bottom:12px;text-align:justify}.terms-modal[_ngcontent-%COMP%]   .modal-buttons[_ngcontent-%COMP%]{margin-top:30px;display:flex;flex-direction:column;gap:10px}.terms-modal[_ngcontent-%COMP%]   .modal-buttons[_ngcontent-%COMP%]   .accept-btn[_ngcontent-%COMP%]{--background: #4facfe;--color: white;font-weight:600}"]})}}return g})();export{ue as RegisterPage};
