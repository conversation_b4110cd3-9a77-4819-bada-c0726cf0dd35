<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory; 
use Illuminate\Database\Eloquent\Model;

class Evacuation extends Model
{
    //
    use HasFactory;
    
    protected $table = 'evacuations';
    protected $fillable = [
        'name',
        'building_name',
        'street_name',
        'barangay',
        'city',
        'province',
        'postal_code',
        'latitude',
        'longitude',
        'capacity',
        'contact',
        'disaster_type',
        'status',
        'is_full'
    ];
    
    protected $casts = [
        'disaster_type' => 'array',
        'contact' => 'array',
        'is_full' => 'boolean'
    ];
}
