<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b912acc4-41c4-41e9-a8c0-a4f0f038bc6e" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/.tsbuildinfo" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/.tsbuildinfo" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common_http.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common_http.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common_http.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_common_http.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_core.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_core.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_core.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_forms.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_forms.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_forms.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_forms.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_platform-browser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_platform-browser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_platform-browser.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_platform-browser.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_router.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_router.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_router.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@angular_router.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor-firebase_messaging.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor-firebase_messaging.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor-firebase_messaging.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor-firebase_messaging.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_core.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_core.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_core.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_filesystem.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_filesystem.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_filesystem.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_filesystem.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_geolocation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_geolocation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_geolocation.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_geolocation.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_haptics.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_haptics.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_haptics.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_haptics.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_local-notifications.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_local-notifications.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_local-notifications.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@capacitor_local-notifications.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular_standalone.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular_standalone.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular_standalone.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_angular_standalone.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_storage-angular.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_storage-angular.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_storage-angular.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/@ionic_storage-angular.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/_metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/_metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-2JEBYUUE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-2JEBYUUE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-2JEBYUUE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-2JEBYUUE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-3J4LGGM5.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-3J4LGGM5.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-3J4LGGM5.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-3J4LGGM5.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-4AF7KAXZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-4AF7KAXZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-4AF7KAXZ.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-4AF7KAXZ.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-676MQPOE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-676MQPOE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-676MQPOE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-676MQPOE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-6ECVN26U.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-6ECVN26U.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-6ECVN26U.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-6ECVN26U.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7IZRYL2Z.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7IZRYL2Z.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7IZRYL2Z.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7IZRYL2Z.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7Q5HCUSL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7Q5HCUSL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7Q5HCUSL.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-7Q5HCUSL.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-CJ5MJUPJ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-CJ5MJUPJ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-CJ5MJUPJ.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-CJ5MJUPJ.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-E6UG7NSK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-E6UG7NSK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-E6UG7NSK.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-E6UG7NSK.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EAE2VPRF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EAE2VPRF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EAE2VPRF.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EAE2VPRF.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EUT5B6DW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EUT5B6DW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EUT5B6DW.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-EUT5B6DW.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HEDKW4S6.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HEDKW4S6.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HEDKW4S6.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HEDKW4S6.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HV7BMKCE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HV7BMKCE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HV7BMKCE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-HV7BMKCE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IMYZMB7I.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IMYZMB7I.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IMYZMB7I.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IMYZMB7I.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IRM3YHRN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IRM3YHRN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IRM3YHRN.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-IRM3YHRN.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-L6BHBXTE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-L6BHBXTE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-L6BHBXTE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-L6BHBXTE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LRF5MG5N.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LRF5MG5N.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LRF5MG5N.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LRF5MG5N.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LVBQUHDE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LVBQUHDE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LVBQUHDE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-LVBQUHDE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MCFBZ5YE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MCFBZ5YE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MCFBZ5YE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MCFBZ5YE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MZOFVHKN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MZOFVHKN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MZOFVHKN.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-MZOFVHKN.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-NZN5AKWE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-NZN5AKWE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-NZN5AKWE.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-NZN5AKWE.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OGKSRTVA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OGKSRTVA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OGKSRTVA.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OGKSRTVA.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OLIQZTCK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OLIQZTCK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OLIQZTCK.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-OLIQZTCK.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PS7JNHEC.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PS7JNHEC.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PUVVNQKQ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PUVVNQKQ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PUVVNQKQ.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-PUVVNQKQ.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-QIQUUHTH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-QIQUUHTH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-QIQUUHTH.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-QIQUUHTH.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RBEPSNWY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RBEPSNWY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RBEPSNWY.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RBEPSNWY.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RHG7RFIU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RHG7RFIU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RHG7RFIU.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-RHG7RFIU.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-TWXMTMU7.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-TWXMTMU7.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-TWXMTMU7.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-TWXMTMU7.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-XYJ3Z5FP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-XYJ3Z5FP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-XYJ3Z5FP.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-XYJ3Z5FP.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YHFEF6YI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YHFEF6YI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YHFEF6YI.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YHFEF6YI.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YYDXBVMM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YYDXBVMM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YYDXBVMM.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-YYDXBVMM.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-ZWBDDU4U.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-ZWBDDU4U.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-ZWBDDU4U.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/chunk-ZWBDDU4U.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/core-js-VRTNGSXT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/core-js-VRTNGSXT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/core-js-VRTNGSXT.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/core-js-VRTNGSXT.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/dom-3FHNNQ6P.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/dom-3FHNNQ6P.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/dom-3FHNNQ6P.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/dom-3FHNNQ6P.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/focus-visible-ETNT35MK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/focus-visible-ETNT35MK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/focus-visible-ETNT35MK.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/focus-visible-ETNT35MK.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/hardware-back-button-WYJ3J23T.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/hardware-back-button-WYJ3J23T.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/hardware-back-button-WYJ3J23T.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/hardware-back-button-WYJ3J23T.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/html2canvas.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/html2canvas.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/html2canvas.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/html2canvas.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index3-HV7UWTVX.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index3-HV7UWTVX.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index3-HV7UWTVX.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index3-HV7UWTVX.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index9-ET534LSX.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index9-ET534LSX.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index9-ET534LSX.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/index9-ET534LSX.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/input-shims-2MBIK4VS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/input-shims-2MBIK4VS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/input-shims-2MBIK4VS.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/input-shims-2MBIK4VS.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons_icons.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons_icons.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons_icons.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ionicons_icons.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-4047cb68-R5ZISBYP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-4047cb68-R5ZISBYP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-4047cb68-R5ZISBYP.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-4047cb68-R5ZISBYP.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-MEFC6WVB.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-MEFC6WVB.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-MEFC6WVB.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/ios.transition-MEFC6WVB.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/keyboard2-X2DXB6VP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/keyboard2-X2DXB6VP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/keyboard2-X2DXB6VP.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/keyboard2-X2DXB6VP.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/leaflet.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/leaflet.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/leaflet.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/leaflet.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-30ce8d1b-5JLF32S4.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-30ce8d1b-5JLF32S4.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-30ce8d1b-5JLF32S4.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-30ce8d1b-5JLF32S4.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-RGFWQGNF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-RGFWQGNF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-RGFWQGNF.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/md.transition-RGFWQGNF.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs_operators.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs_operators.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs_operators.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/rxjs_operators.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/shadow-css-7F4QWFRD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/shadow-css-7F4QWFRD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/shadow-css-7F4QWFRD.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/shadow-css-7F4QWFRD.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/status-tap-MY3AAKXU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/status-tap-MY3AAKXU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/status-tap-MY3AAKXU.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/status-tap-MY3AAKXU.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/swipe-back-546GSLDA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/swipe-back-546GSLDA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/swipe-back-546GSLDA.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/swipe-back-546GSLDA.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-7OK4KL3X.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-7OK4KL3X.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-7OK4KL3X.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-7OK4KL3X.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-JQLZW4OP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-JQLZW4OP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-JQLZW4OP.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-JQLZW4OP.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OFE4VD5L.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OFE4VD5L.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OFE4VD5L.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OFE4VD5L.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OTHR3NCA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OTHR3NCA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OTHR3NCA.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-OTHR3NCA.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-WQ7IGJWG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-WQ7IGJWG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-WQ7IGJWG.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/vite/deps/web-WQ7IGJWG.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/capacitor.build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/app/capacitor.build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/capacitor.settings.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/capacitor.settings.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/interfaces/evacuation-center.interface.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/interfaces/evacuation-center.interface.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/all-maps.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/all-maps.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/earthquake-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/earthquake-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/map/evacuation-center-details.component.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/map/evacuation-center-details.component.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/map/evacuation-center-details.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/map/evacuation-center-details.component.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/map/map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/map/map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/search/evacuation-center-modal.component.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/environments/environment.prod.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/environments/environment.prod.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/environments/environment.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/environments/environment.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=LocalEmulator, isTemplate=false, identifier=path=C:\Users\<USER>\.android\avd\Medium_Phone.avd)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zbDBDPU1ZIpeb1uVhLUgFWp7zP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "revise",
    "kotlin-language-version-configured": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="android.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b912acc4-41c4-41e9-a8c0-a4f0f038bc6e" name="Changes" comment="" />
      <created>1751990754156</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751990754156</updated>
    </task>
    <servers />
  </component>
</project>