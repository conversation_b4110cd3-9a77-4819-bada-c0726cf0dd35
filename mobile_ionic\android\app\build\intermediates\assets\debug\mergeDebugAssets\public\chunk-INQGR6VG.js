import{Ca as d,E as l,Hb as b,M as o,N as i,O as s,S as r,_ as a,fa as m,hc as h,mb as u,n as c,sa as p,yb as f}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-B7O3QC5Z.js";var v=(()=>{class n{constructor(t){this.router=t}ngOnInit(){this.checkAuthenticationStatus()}checkAuthenticationStatus(){let t=localStorage.getItem("token"),e=localStorage.getItem("onboardingComplete");console.log("\u{1F50D} Intro page - Auth status check:",{hasToken:!!t,onboardingComplete:e==="true"}),t&&e==="true"?(console.log("\u2705 User authenticated & onboarded \u2192 navigating to tabs/home"),this.router.navigate(["/tabs/home"])):t&&e!=="true"?(console.log("\u2705 User authenticated but not onboarded \u2192 navigating to welcome"),this.router.navigate(["/welcome"])):!t&&e==="true"&&(console.log("\u{1F511} User not authenticated but has onboarded before \u2192 navigating to login"),this.router.navigate(["/login"]))}goToLogin(){this.router.navigate(["/login"])}goToRegister(){this.router.navigate(["/register"])}static{this.\u0275fac=function(e){return new(e||n)(l(d))}}static{this.\u0275cmp=c({type:n,selectors:[["app-intro"]],standalone:!0,features:[m],decls:9,vars:0,consts:[[1,"intro-bg"],[1,"intro-wrapper"],["src","assets/ALERTO.png"],[1,"button-container"],["expand","block",1,"login-btn",3,"click"],["expand","block","fill","outline",1,"signup-btn",3,"click"]],template:function(e,g){e&1&&(o(0,"ion-content",0)(1,"div",1)(2,"div"),s(3,"img",2),i(),o(4,"div",3)(5,"ion-button",4),r("click",function(){return g.goToLogin()}),a(6," Log In "),i(),o(7,"ion-button",5),r("click",function(){return g.goToRegister()}),a(8," Sign Up "),i()()()())},dependencies:[h,f,b,p,u],styles:[".intro-bg[_ngcontent-%COMP%]{--background: white;display:flex;align-items:center;justify-content:center;min-height:100vh}.intro-wrapper[_ngcontent-%COMP%]{width:100%;max-width:350px;padding:2rem;margin:0 auto;display:flex;flex-direction:column;align-items:flex-start;text-align:left}.logo-container[_ngcontent-%COMP%]{margin-bottom:3rem;align-self:center}.main-icon[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;background:#fff3;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a}.title-container[_ngcontent-%COMP%]{margin-bottom:3rem;align-self:center}.app-title[_ngcontent-%COMP%]{font-size:2.2rem;font-weight:700;color:#000;margin:0 0 .5rem;text-shadow:none;letter-spacing:-.5px}.app-subtitle[_ngcontent-%COMP%]{font-size:1.1rem;color:#ffffffe6;margin:0;font-weight:300}.button-container[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:1rem}.login-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%;margin-top:1rem}.signup-btn[_ngcontent-%COMP%]{--color: #007bff;--border-color: #007bff;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%}.signup-btn[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .1)}@media (max-height: 600px){.intro-wrapper[_ngcontent-%COMP%]{padding:1rem}.logo-container[_ngcontent-%COMP%]{margin-bottom:2rem}.main-icon[_ngcontent-%COMP%]{width:80px;height:80px;padding:15px}.title-container[_ngcontent-%COMP%]{margin-bottom:2rem}.app-title[_ngcontent-%COMP%]{font-size:2rem}}"]})}}return n})();export{v as IntroPage};
