import{a as xe}from"./chunk-ZNZVS5VC.js";import{a as _e,b as Ne,c as Te}from"./chunk-2IVG2Z4K.js";import{b as x}from"./chunk-DUSKIWGF.js";import{a as te}from"./chunk-LT5RLULD.js";import"./chunk-XICXROI7.js";import{b as Pe}from"./chunk-TTLEF7YW.js";import{a as T}from"./chunk-G7IUXWVS.js";import"./chunk-ICWJVXBH.js";import{$ as S,Aa as ce,Ab as ue,B as ae,Bb as ge,Cb as pe,D as p,E as V,Eb as he,Gb as me,H as k,Hb as j,I as C,Ib as fe,Jb as ve,K as re,Kb as Ce,Lb as K,M as d,Mb as G,N as m,O as b,Ob as Z,Pb as U,Qb as we,R as N,Rb as ye,S as _,T as M,Vb as be,Zb as X,_ as f,_b as Q,aa as O,ba as se,cc as Me,d as W,fa as A,fc as ee,gc as H,hc as q,m as E,mb as le,n as D,pa as Y,qa as R,s as I,sa as F,t as $,va as J,w as ie,yb as z,zb as de}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-P4YSCN2K.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-7D2EH4XU.js";import"./chunk-MMIXVVWR.js";import"./chunk-SV7S5NYR.js";import{g as ne,h as v}from"./chunk-B7O3QC5Z.js";var l=ne(Te());function De(g,P){g&1&&(d(0,"div",8),b(1,"ion-spinner",9),d(2,"p"),f(3,"Calculating travel times..."),m()())}function Ae(g,P){if(g&1&&(d(0,"ion-chip",13),b(1,"ion-icon",24),d(2,"ion-label"),f(3),m()()),g&2){let e=M(2);C("color",e.getStatusColor(e.center.status)),p(3),S(e.center.status)}}function Re(g,P){if(g&1&&(d(0,"div",16)(1,"ion-item",17),b(2,"ion-icon",25),d(3,"ion-label")(4,"h2"),f(5,"Capacity"),m(),d(6,"p"),f(7),m()()()()),g&2){let e=M(2);p(7),O("",e.center.capacity," people")}}function Fe(g,P){if(g&1){let e=N();d(0,"ion-card",26),_("click",function(){let o=I(e).$implicit,n=M(2);return $(n.selectTravelMode(o.mode))}),d(1,"ion-card-header"),b(2,"ion-icon",27),d(3,"ion-card-title"),f(4),m()(),d(5,"ion-card-content")(6,"div",28)(7,"div",29),b(8,"ion-icon",30),d(9,"span"),f(10),m()(),d(11,"div",31),b(12,"ion-icon",32),d(13,"span"),f(14),m()()(),d(15,"ion-button",33),_("click",function(){let o=I(e).$implicit,n=M(2);return $(n.selectTravelMode(o.mode))}),f(16," Select "),b(17,"ion-icon",34),m()()()}if(g&2){let e=P.$implicit,t=M(2);p(2),C("name",e.icon)("color",e.color),p(2),O(" ",e.mode==="foot-walking"?"Walking":e.mode==="cycling-regular"?"Cycling":"Driving"," "),p(6),S(t.formatTime(e.time)),p(4),S(t.formatDistance(e.distance)),p(),C("color",e.color)}}function ze(g,P){if(g&1&&(d(0,"div",10)(1,"h1",11),f(2),m(),d(3,"div",12)(4,"ion-chip",13),b(5,"ion-icon",14),d(6,"ion-label"),f(7),m()(),k(8,Ae,4,2,"ion-chip",15),m(),d(9,"div",16)(10,"ion-item",17),b(11,"ion-icon",18),d(12,"ion-label")(13,"h2"),f(14,"Address"),m(),d(15,"p"),f(16),m()()()(),d(17,"div",16)(18,"ion-item",17),b(19,"ion-icon",19),d(20,"ion-label")(21,"h2"),f(22,"Contact"),m(),d(23,"p"),f(24),m()()()(),k(25,Re,8,1,"div",20),d(26,"div",21)(27,"h2"),f(28,"Travel Time Estimates"),m(),d(29,"div",22),k(30,Fe,18,6,"ion-card",23),m()()()),g&2){let e=M();p(2),S(e.center.name),p(2),C("color",e.getStatusColor(e.center.status)),p(),C("name",e.getDisasterTypeIcon(e.center.disaster_type)),p(2),S(e.getDisasterTypeDisplay(e.center.disaster_type)||"General"),p(),C("ngIf",e.center.status),p(8),S(e.center.address||"No address available"),p(8),S(e.center.contact||"No contact information available"),p(),C("ngIf",e.center.capacity),p(5),C("ngForOf",e.travelEstimates)}}var Ee=(()=>{class g{constructor(e,t,o,n){this.modalCtrl=e,this.http=t,this.toastCtrl=o,this.osmRouting=n,this.travelEstimates=[],this.selectedMode="foot-walking",this.isLoading=!0}ngOnInit(){return v(this,null,function*(){this.isLoading=!0,yield this.calculateTravelTimes(),this.isLoading=!1})}calculateTravelTimes(){return v(this,null,function*(){let e=[{id:"foot-walking",name:"Walking",icon:"walk-outline",color:"primary"},{id:"cycling-regular",name:"Cycling",icon:"bicycle-outline",color:"success"},{id:"driving-car",name:"Driving",icon:"car-outline",color:"danger"}];this.travelEstimates=[];let t=Number(this.center.latitude),o=Number(this.center.longitude);if(console.log("Calculating travel times with coordinates:",{userLat:this.userLat,userLng:this.userLng,centerLat:t,centerLng:o}),isNaN(t)||isNaN(o)||isNaN(this.userLat)||isNaN(this.userLng)){console.error("Invalid coordinates for travel time calculations:",{userLat:this.userLat,userLng:this.userLng,centerLat:t,centerLng:o}),this.toastCtrl.create({message:"Invalid coordinates. Using estimated travel times.",duration:3e3,color:"warning",position:"bottom"}).then(n=>n.present()),this.useFallbackCalculations(e);return}for(let n of e)try{let r=yield this.getTravelTimeEstimate(this.userLat,this.userLng,t,o,n.id);this.travelEstimates.push({mode:n.id,time:r.time,distance:r.distance,icon:n.icon,color:n.color})}catch(r){if(console.error(`Error calculating ${n.name} time:`,r),this.travelEstimates.length===0){let c="Using estimated travel times due to connection issues";r.message&&(r.message.includes("Invalid coordinates")?c="Invalid coordinates. Using estimated travel times.":r.message.includes("API Error")&&(c=`${r.message}. Using estimated travel times.`)),this.toastCtrl.create({message:c,duration:3e3,color:"warning",position:"bottom"}).then(h=>h.present())}let a=this.calculateStraightLineDistance(this.userLat,this.userLng,t,o),s;switch(n.id){case"foot-walking":s=5e3/3600;break;case"cycling-regular":s=15e3/3600;break;case"driving-car":s=4e4/3600;break;default:s=5e3/3600}let i=a/s;this.travelEstimates.push({mode:n.id,time:i,distance:a,icon:n.icon,color:n.color})}})}useFallbackCalculations(e){let t=Number(this.center.latitude),o=Number(this.center.longitude),n=isNaN(this.userLat)?10.3157:this.userLat,r=isNaN(this.userLng)?123.8854:this.userLng,a=isNaN(t)?10.3257:t,s=isNaN(o)?123.8954:o,i=this.calculateStraightLineDistance(n,r,a,s);console.log(`Using fallback calculation with distance: ${i} meters`);for(let c of e){let h;switch(c.id){case"foot-walking":h=5e3/3600;break;case"cycling-regular":h=15e3/3600;break;case"driving-car":h=4e4/3600;break;default:h=5e3/3600}let u=i/h;this.travelEstimates.push({mode:c.id,time:u,distance:i,icon:c.icon,color:c.color})}}getTravelTimeEstimate(e,t,o,n,r){return v(this,null,function*(){if([e,t,o,n].some(a=>typeof a!="number"||isNaN(a)))throw console.error("Invalid coordinates for travel time estimate:",{startLat:e,startLng:t,endLat:o,endLng:n}),new Error("Invalid coordinates");if(Math.abs(e)>90||Math.abs(o)>90||Math.abs(t)>180||Math.abs(n)>180)throw console.error("Coordinates out of range for travel time estimate:",{startLat:e,startLng:t,endLat:o,endLng:n}),new Error("Coordinates out of range");console.log(`Calculating OpenStreetMap route from [${e}, ${t}] to [${o}, ${n}] using mode: ${r}`);try{let a=this.osmRouting.convertTravelModeToProfile(r),s=yield this.osmRouting.getDirections(t,e,n,o,a,{geometries:"geojson",overview:"simplified",steps:!1});if(!s.routes||s.routes.length===0)throw new Error("No routes found");let i=s.routes[0];return console.log(`Received OpenStreetMap response for ${r} route:`,{duration:i.duration,distance:i.distance}),{time:i.duration,distance:i.distance}}catch(a){throw console.error(`Failed to fetch ${r} route from OpenStreetMap:`,a),a.message?a.message.includes("Invalid API key")?new Error("Invalid OpenRouteService API key. Please check your token configuration."):a.message.includes("Rate limit exceeded")?new Error("Too many requests to OpenRouteService. Please wait a moment and try again."):a.message.includes("Network error")?new Error("Network error. Please check your internet connection."):a.message.includes("No routes found")?new Error("No route could be calculated between these points."):new Error(`OpenStreetMap routing error: ${a.message}`):a}})}calculateStraightLineDistance(e,t,o,n){let a=e*Math.PI/180,s=o*Math.PI/180,i=(o-e)*Math.PI/180,c=(n-t)*Math.PI/180,h=Math.sin(i/2)*Math.sin(i/2)+Math.cos(a)*Math.cos(s)*Math.sin(c/2)*Math.sin(c/2);return 6371e3*(2*Math.atan2(Math.sqrt(h),Math.sqrt(1-h)))}formatTime(e){let t=Math.round(e/60);if(t<60)return`${t} min`;{let o=Math.floor(t/60),n=t%60;return`${o} hr ${n} min`}}formatDistance(e){return e<1e3?`${Math.round(e)} m`:`${(e/1e3).toFixed(2)} km`}selectTravelMode(e){this.selectedMode=e,this.dismiss(e)}dismiss(e){this.modalCtrl.dismiss({selectedMode:e||null})}getDisasterTypeIcon(e){if(!e)return"alert-circle-outline";let t=Array.isArray(e)?e[0]:e;if(!t)return"alert-circle-outline";let o=t.toLowerCase();return t.startsWith("Others:")?"help-circle-outline":o.includes("earthquake")||o.includes("quake")?"earth-outline":o.includes("flood")||o.includes("flash")?"water-outline":o.includes("typhoon")||o.includes("storm")?"thunderstorm-outline":o.includes("fire")?"flame-outline":o.includes("landslide")||o.includes("slide")?"triangle-outline":o.includes("others")?"help-circle-outline":"alert-circle-outline"}getDisasterTypeDisplay(e){return e?Array.isArray(e)?e.join(", "):e:"General"}getStatusColor(e){if(!e)return"medium";let t=e.toLowerCase();return t.includes("active")||t.includes("open")?"success":t.includes("inactive")||t.includes("closed")?"warning":t.includes("full")?"danger":"medium"}static{this.\u0275fac=function(t){return new(t||g)(V(ee),V(J),V(H),V(te))}}static{this.\u0275cmp=D({type:g,selectors:[["app-evacuation-center-details"]],inputs:{center:"center",userLat:"userLat",userLng:"userLng"},standalone:!0,features:[A],decls:14,vars:2,consts:[[1,"ion-no-border"],["slot","start"],[3,"click"],["name","chevron-back-outline","slot","icon-only"],[1,"ion-padding"],["class","loading-container",4,"ngIf"],["class","details-container",4,"ngIf"],["expand","block","color","medium",3,"click"],[1,"loading-container"],["name","circles"],[1,"details-container"],[1,"center-name"],[1,"center-type"],[3,"color"],[3,"name"],[3,"color",4,"ngIf"],[1,"info-section"],["lines","none"],["name","location-outline","slot","start","color","primary"],["name","call-outline","slot","start","color","primary"],["class","info-section",4,"ngIf"],[1,"travel-section"],[1,"travel-cards"],["class","travel-card",3,"click",4,"ngFor","ngForOf"],["name","information-circle-outline"],["name","people-outline","slot","start","color","primary"],[1,"travel-card",3,"click"],[1,"travel-icon",3,"name","color"],[1,"travel-info"],[1,"travel-time"],["name","time-outline"],[1,"travel-distance"],["name","navigate-outline"],["expand","block","fill","clear",3,"click","color"],["name","arrow-forward-outline","slot","end"]],template:function(t,o){t&1&&(d(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),_("click",function(){return o.dismiss()}),b(4,"ion-icon",3),m()(),d(5,"ion-title"),f(6,"Evacuation Center"),m()()(),d(7,"ion-content",4),k(8,De,4,0,"div",5)(9,ze,31,9,"div",6),m(),d(10,"ion-footer",0)(11,"ion-toolbar")(12,"ion-button",7),_("click",function(){return o.dismiss()}),f(13," Back to Map "),m()()()),t&2&&(p(8),C("ngIf",o.isLoading),p(),C("ngIf",!o.isLoading))},dependencies:[q,z,de,ue,ge,pe,he,me,j,Ce,K,G,Z,U,be,X,Q,F,Y,R],styles:["ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium)}.details-container[_ngcontent-%COMP%]{padding-bottom:20px}.center-name[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0 0 8px;color:var(--ion-color-dark)}.center-type[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px;flex-wrap:wrap}.info-section[_ngcontent-%COMP%]{margin-bottom:16px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0;--background: transparent}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:600;margin-bottom:4px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]{margin-top:24px}.travel-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:16px;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 12px #00000014;transition:transform .2s ease,box-shadow .2s ease}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]:active{transform:scale(.98);box-shadow:0 2px 8px #0000001a}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px 0}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   .travel-icon[_ngcontent-%COMP%]{font-size:28px;margin-right:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px 16px 16px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]{display:flex;align-items:center}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:6px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:8px;font-weight:500}ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;padding:0 16px 16px}"]})}}return g})();function Ge(g,P){if(g&1&&(d(0,"p"),f(1),m()),g&2){let e=M();p(),se(" ",e.formatDistance(e.totalDistance)," \u2022 ",e.formatTime(e.totalDuration)," ")}}function Ue(g,P){if(g&1&&(d(0,"span"),f(1),m()),g&2){let e=M(2).$implicit,t=M();p(),O(" \u2022 ",t.formatTime(e.duration),"")}}function qe(g,P){if(g&1&&(d(0,"p"),f(1),k(2,Ue,2,1,"span",4),m()),g&2){let e=M().$implicit,t=M();p(),O(" ",t.formatDistance(e.distance)," "),p(),C("ngIf",e.duration>0)}}function We(g,P){if(g&1&&(d(0,"ion-item",9),b(1,"ion-icon",3),d(2,"ion-label"),b(3,"h3",10),k(4,qe,3,2,"p",4),m(),d(5,"ion-note",11),f(6),m()()),g&2){let e=P.$implicit,t=P.index,o=M();p(),C("name",o.getDirectionIcon(e.type))("color",o.getTravelModeColor()),p(2),C("innerHTML",e.instruction,ae),p(),C("ngIf",e.distance>0),p(2),S(t+1)}}var Le=(()=>{class g{constructor(){this.directions=[],this.travelMode="foot-walking",this.totalDistance=null,this.totalDuration=null,this.close=new ie}getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}getTravelModeIcon(){switch(this.travelMode){case"foot-walking":return"walk-outline";case"cycling-regular":return"bicycle-outline";case"driving-car":return"car-outline";default:return"navigate-outline"}}getTravelModeColor(){switch(this.travelMode){case"foot-walking":return"primary";case"cycling-regular":return"success";case"driving-car":return"danger";default:return"medium"}}formatTime(e){let t=Math.round(e/60);if(t<60)return`${t} min`;{let o=Math.floor(t/60),n=t%60;return`${o} hr ${n} min`}}formatDistance(e){return e<1e3?`${Math.round(e)} m`:`${(e/1e3).toFixed(2)} km`}closePanel(){this.close.emit()}getDirectionIcon(e){switch(e){case 0:return"arrow-forward-outline";case 1:return"arrow-forward-outline";case 2:return"arrow-forward-outline";case 3:return"arrow-forward-outline";case 4:return"arrow-back-outline";case 5:return"arrow-back-outline";case 6:return"arrow-back-outline";case 7:return"arrow-back-outline";case 8:return"arrow-down-outline";case 9:return"flag-outline";case 10:return"arrow-up-outline";case 11:return"arrow-forward-outline";case 12:return"arrow-forward-outline";case 13:return"arrow-forward-outline";case 14:return"arrow-forward-outline";case 15:return"flag-outline";default:return"navigate-outline"}}static{this.\u0275fac=function(t){return new(t||g)}}static{this.\u0275cmp=D({type:g,selectors:[["app-directions-panel"]],inputs:{directions:"directions",travelMode:"travelMode",totalDistance:"totalDistance",totalDuration:"totalDuration"},outputs:{close:"close"},standalone:!0,features:[A],decls:13,vars:5,consts:[[1,"directions-panel"],[1,"directions-header"],["lines","none"],["slot","start",3,"name","color"],[4,"ngIf"],["fill","clear","slot","end",3,"click"],["name","close-outline","slot","icon-only"],[1,"directions-list"],["lines","full",4,"ngFor","ngForOf"],["lines","full"],[3,"innerHTML"],["slot","end"]],template:function(t,o){t&1&&(d(0,"div",0)(1,"div",1)(2,"ion-item",2),b(3,"ion-icon",3),d(4,"ion-label")(5,"h2"),f(6),m(),k(7,Ge,2,2,"p",4),m(),d(8,"ion-button",5),_("click",function(){return o.closePanel()}),b(9,"ion-icon",6),m()()(),d(10,"div",7)(11,"ion-list"),k(12,We,7,5,"ion-item",8),m()()()),t&2&&(p(3),C("name",o.getTravelModeIcon())("color",o.getTravelModeColor()),p(3),O("",o.getTravelModeName()," Directions"),p(),C("ngIf",o.totalDistance&&o.totalDuration),p(5),C("ngForOf",o.directions))},dependencies:[q,z,G,Z,U,we,ye,F,Y,R],styles:[".directions-panel[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:15px;border-top-right-radius:15px;box-shadow:0 -2px 10px #0000001a;max-height:50vh;overflow-y:auto;z-index:1000}.directions-header[_ngcontent-%COMP%]{padding:10px 0;border-bottom:1px solid #eee;position:sticky;top:0;background-color:#fff;z-index:1001}.directions-list[_ngcontent-%COMP%]{max-height:calc(50vh - 60px);overflow-y:auto}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}ion-icon[_ngcontent-%COMP%]{font-size:24px}h2[_ngcontent-%COMP%]{font-weight:600;margin:0}h3[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0}p[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin:4px 0 0}ion-note[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:50%;background-color:var(--ion-color-light);color:var(--ion-color-dark);display:flex;align-items:center;justify-content:center;min-width:24px;min-height:24px}"]})}}return g})();var Ie=ne(Ne());function Ve(g,P){if(g&1){let e=N();d(0,"div",15)(1,"ion-button",16),_("click",function(){I(e);let o=M();return $(o.requestLocationExplicitly())}),b(2,"ion-icon",17),f(3," Enable Location Access "),m(),d(4,"p",18),f(5,"Tap the button above to enable location access"),m()()}}function He(g,P){if(g&1){let e=N();d(0,"div",19),_("click",function(){I(e);let o=M();return $(o.showDirectionsPanel=!0)}),b(1,"ion-icon",20),d(2,"div",21)(3,"strong"),f(4),m(),f(5),d(6,"div",22),f(7),m()(),b(8,"ion-icon",23),m()}if(g&2){let e=M();p(),C("name",e.travelMode==="foot-walking"?"walk-outline":e.travelMode==="cycling-regular"?"bicycle-outline":"car-outline")("color",e.travelMode==="foot-walking"?"primary":e.travelMode==="cycling-regular"?"success":"danger"),p(3),O("",(e.routeTime/60).toFixed(0)," min"),p(),O(" \u2022 ",(e.routeDistance/1e3).toFixed(2)," km "),p(2),S(e.getTravelModeName())}}function Be(g,P){if(g&1){let e=N();d(0,"app-directions-panel",24),_("close",function(){I(e);let o=M();return $(o.showDirectionsPanel=!1)}),m()}if(g&2){let e=M();C("directions",e.currentDirections)("travelMode",e.travelMode)("totalDistance",e.routeDistance)("totalDuration",e.routeTime)}}function Ye(g,P){if(g&1&&(d(0,"div",25),b(1,"ion-icon",10),d(2,"span"),f(3),m()()),g&2){let e=M();p(),C("name",e.currentDisasterType.toLowerCase().includes("earthquake")?"earth-outline":e.currentDisasterType.toLowerCase().includes("typhoon")?"thunderstorm-outline":e.currentDisasterType.toLowerCase().includes("flood")?"water-outline":"alert-circle-outline"),p(2),O("",e.currentDisasterType," Evacuation Centers")}}function Je(g,P){if(g&1){let e=N();d(0,"ion-fab",26)(1,"ion-fab-button",27),_("click",function(){I(e);let o=M();return $(o.routeToTwoNearestCenters())}),b(2,"ion-icon",28),m(),d(3,"ion-label",29),f(4,"Route to Nearest Centers"),m()()}}function je(g,P){if(g&1){let e=N();d(0,"ion-fab",30)(1,"ion-fab-button",31),_("click",function(){I(e);let o=M();return $(o.showDirectionsPanel=!0)}),b(2,"ion-icon",32),m(),d(3,"ion-label",29),f(4,"Show Directions"),m()()}}var xt=(()=>{class g{getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}findTwoNearestCenters(e,t,o){if(!o.length)return[];let n=o;if(this.currentDisasterType&&this.currentDisasterType!=="all"){console.log(`Filtering centers by disaster type: ${this.currentDisasterType}`);let a=this.currentDisasterType.toLowerCase();if(n=o.filter(s=>{if(!s.disaster_type)return!1;let i;return Array.isArray(s.disaster_type)?i=s.disaster_type.join(" ").toLowerCase():i=s.disaster_type.toLowerCase(),a==="earthquake"||a==="earthquakes"?i.includes("earthquake")||i.includes("quake"):a==="typhoon"||a==="typhoons"?i.includes("typhoon")||i.includes("storm")||i.includes("hurricane"):a==="flood"||a==="floods"?i.includes("flood")||i.includes("flash"):i===a}),console.log(`Filtered to ${n.length} centers for disaster type: ${this.currentDisasterType}`),n.length===0)return console.log(`No centers found for disaster type: ${this.currentDisasterType}`),[]}return[...n].sort((a,s)=>{let i=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude)),c=this.calculateDistance(e,t,Number(s.latitude),Number(s.longitude));return i-c}).slice(0,2)}updateRoute(){this.routeToTwoNearestCenters()}requestLocationExplicitly(){return v(this,null,function*(){console.log("User explicitly requested location access via button click"),this.showLocationRequestButton=!1,yield this.loadingService.showLoading("Getting your location...");try{try{let n=yield x.checkPermissions();if(console.log("Permission status:",n),n.location!=="granted"){console.log("Requesting permissions explicitly...");let r=yield x.requestPermissions();if(console.log("Permission request result:",r),r.location!=="granted")throw new Error("Location permission denied")}}catch(n){console.log("Permission check failed, might be in browser:",n)}let e=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:3e4,maximumAge:0});console.log("Successfully got position:",e);let t=e.coords.latitude,o=e.coords.longitude;yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Location access successful!",duration:2e3,color:"success"}).then(n=>n.present()),this.gpsEnabled=!0,this.map?(this.userMarker?(this.userMarker.setLatLng([t,o]),this.map.setView([t,o],15)):this.updateUserMarker(t,o),this.startWatchingPosition()):this.initializeMap(t,o)}catch(e){console.error("Error getting location:",e),yield this.loadingService.dismissLoading(),this.showLocationRequestButton=!0,yield(yield this.alertCtrl.create({header:"Location Access Failed",message:"We couldn't access your location. Would you like to see help on enabling location access?",buttons:[{text:"Show Help",handler:()=>{this.showLocationHelp()}},{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"Cancel",role:"cancel"}]})).present()}})}showLocationHelp(){return v(this,null,function*(){let e="To use location services:";navigator.userAgent.includes("Chrome")?e+='<br><br><b>Chrome:</b><br>1. Click the lock/info icon in the address bar<br>2. Select "Site settings"<br>3. Change Location permission to "Allow"<br>':navigator.userAgent.includes("Firefox")?e+='<br><br><b>Firefox:</b><br>1. Click the lock icon in the address bar<br>2. Select "Site Permissions"<br>3. Change "Access Your Location" to "Allow"<br>':navigator.userAgent.includes("Safari")?e+='<br><br><b>Safari:</b><br>1. Open Safari settings<br>2. Go to Websites > Location<br>3. Ensure this website is set to "Allow"<br>':e+="<br><br>Please enable location access for this website in your browser settings.",e+="<br><br>On mobile devices, also ensure that:<br>1. Your device location/GPS is turned on<br>2. The app has permission to access your location",yield(yield this.alertCtrl.create({header:"Location Services Help",message:e,buttons:[{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"OK",role:"cancel"}]})).present()})}routeToTwoNearestCenters(){return v(this,null,function*(){try{if(!this.gpsEnabled){console.log("GPS is disabled, not calculating routes"),(yield this.toastCtrl.create({message:"Please enable GPS to see evacuation routes",duration:3e3,color:"warning"})).present();return}console.log("Forcing fresh GPS position check for routing...");try{let e=yield this.getCurrentPositionWithFallback(),t=e.coords.latitude,o=e.coords.longitude;console.log(`Got fresh GPS position: [${t}, ${o}]`),this.userMarker?(this.userMarker.setLatLng([t,o]),this.map.setView([t,o],15)):this.userMarker=l.marker([t,o],{icon:l.icon({iconUrl:"assets/myLocation.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map);let n=t,r=o;this.toastCtrl.create({message:"Using your current real-time location",duration:2e3,color:"success"}).then(s=>s.present()),console.log(`Using FRESH GPS coordinates for routing: [${n}, ${r}]`),(!this.evacuationCenters||this.evacuationCenters.length===0)&&(yield this.loadEvacuationCenters(n,r));let a=this.findTwoNearestCenters(n,r,this.evacuationCenters);if(a.length===0){(yield this.toastCtrl.create({message:"No evacuation centers found.",duration:3e3,color:"danger"})).present();return}console.log("Aggressively clearing ALL existing routes"),this.map.eachLayer(s=>{s instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(s))});for(let s of a){if(s.routing_available===!1){console.log(`Skipping route to ${s.name} - routing not available (center is full)`);continue}let i=Number(s.latitude),c=Number(s.longitude);if(console.log(`Calculating route from [${n}, ${r}] to center: ${s.name} with disaster type: ${s.disaster_type}`),console.log(`Center coordinates: [${i}, ${c}], types: [${typeof i}, ${typeof c}]`),isNaN(i)||isNaN(c)){console.error("Invalid center coordinates:",{centerLat:i,centerLng:c,center:s});continue}yield this.getRealRoute(n,r,i,c,this.travelMode,s.disaster_type)}if(this.userMarker){let s="You are here!.";a.forEach((i,c)=>{let h=this.calculateDistance(n,r,Number(i.latitude),Number(i.longitude));s+=`<br> \u2022 <strong>${i.name}</strong> <br> Distance: ${(h/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(s).openPopup()}}catch(e){console.error("Failed to get fresh GPS position:",e),(yield this.toastCtrl.create({message:"Could not get your current location. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}}catch(e){(yield this.toastCtrl.create({message:"Failed to get your location or route.",duration:3e3,color:"danger"})).present(),console.error("Failed to route to two nearest centers",e)}})}constructor(){this.travelMode="foot-walking",this.routeTime=null,this.routeDistance=null,this.userMarker=null,this.evacuationCenters=[],this.gpsEnabled=!0,this.loadingService=E(xe),this.osmRouting=E(te),this.mapboxRouting=E(_e),this.toastController=E(H),this.alertCtrl=E(Me),this.toastCtrl=E(H),this.modalCtrl=E(ee),this.http=E(J),this.offlineStorage=E(Pe),this.watchId=null,this.currentDisasterType="all",this.isFilterMode=!1,this.currentDirections=[],this.showDirectionsPanel=!1,this.showLocationRequestButton=!1,this.ORS_API_KEY=T.orsApiKey,this.isLoadingCenters=!1,this.lastErrorToast=0,this.ERROR_TOAST_DEBOUNCE=5e3,this.route=E(ce),this.initializeOfflineMode()}initializeOfflineMode(){return v(this,null,function*(){try{yield this.offlineStorage.cleanExpiredCache(),this.offlineStorage.offlineDataAvailable$.subscribe(e=>{console.log(`\u{1F4F1} Offline data available: ${e}`)}),console.log("\u2705 Offline mode initialized")}catch(e){console.error("\u274C Failed to initialize offline mode:",e)}})}cacheMapData(e,t){return v(this,null,function*(){try{let o={userLocation:{lat:e,lng:t},zoomLevel:this.map?.getZoom()||15,mapBounds:this.map?.getBounds(),lastUpdated:new Date};yield this.offlineStorage.setItem("mapData",o),console.log("\u{1F5FA}\uFE0F Cached essential map data")}catch(o){console.error("\u274C Failed to cache map data:",o)}})}loadCachedMapData(){return v(this,null,function*(){try{let e=yield this.offlineStorage.getItem("mapData");return e?(console.log("\u{1F4E6} Loaded cached map data"),e):null}catch(e){return console.error("\u274C Failed to load cached map data:",e),null}})}setupNetworkStatusMonitoring(){window.addEventListener("online",()=>{console.log("\u{1F4F6} Network came back online - starting data sync"),this.syncDataWhenOnline()}),window.addEventListener("offline",()=>{console.log("\u{1F4F5} Network went offline - switching to cached data")}),navigator.onLine&&(console.log("\u{1F4F6} App started online - checking for data sync"),this.syncDataWhenOnline())}syncDataWhenOnline(){return v(this,null,function*(){try{console.log("\u{1F504} Starting data synchronization...");let e=yield this.offlineStorage.getCachedUserLocation();e&&(yield this.syncEvacuationCenters(e.lat,e.lng)),console.log("\u2705 Data synchronization completed")}catch(e){console.error("\u274C Data synchronization failed:",e)}})}syncEvacuationCenters(e,t){return v(this,null,function*(){try{console.log("\u{1F504} Syncing evacuation centers...");let o=yield W(this.http.get(`${T.apiUrl}/evacuation-centers`));o.data&&o.data.length>0&&(yield this.offlineStorage.cacheEvacuationCenters(o.data),console.log(`\u2705 Synced ${o.data.length} evacuation centers`),this.toastCtrl.create({message:`\u{1F504} Synced ${o.data.length} evacuation centers`,duration:2e3,color:"success"}).then(n=>n.present()))}catch(o){console.error("\u274C Failed to sync evacuation centers:",o)}})}getOfflineDataSummary(){return v(this,null,function*(){try{let e=yield this.offlineStorage.getOfflineDataSummary();console.log("\u{1F4CA} Offline Data Summary:",e),yield(yield this.toastCtrl.create({message:`\u{1F4F1} Offline: ${e.evacuationCenters} centers, ${e.cacheSize}KB cached`,duration:3e3,color:"medium"})).present()}catch(e){console.error("\u274C Failed to get offline data summary:",e)}})}getPrimaryDisasterType(e){return e?Array.isArray(e)?e[0]||"":e:""}getDisasterIcon(e){if(!e||typeof e=="string"&&e.startsWith("Others:"))return"assets/forOthers.png";switch(e){case"Earthquake":return"assets/forEarthquake.png";case"Flood":return"assets/forFlood.png";case"Typhoon":return"assets/forTyphoon.png";case"Fire":return"assets/forFire.png";case"Landslide":return"assets/forLandslide.png";case"Others":return"assets/forOthers.png";default:return console.warn(`Unknown disaster type: ${e}, using Others icon`),"assets/forOthers.png"}}getDisasterColor(e){if(!e||typeof e=="string"&&e.startsWith("Others:"))return"#9333ea";switch(e){case"Earthquake":return"#ffa500";case"Flood":return"#0000ff";case"Typhoon":return"#008000";case"Fire":return"#ef4444";case"Landslide":return"#8b5a2b";case"Others":return"#9333ea";default:return console.warn(`Unknown disaster type: ${e}, using Others color`),"#9333ea"}}clearPulseCircles(){this.map.eachLayer(e=>{e instanceof l.Circle&&e.options.className==="marker-pulse"&&this.map.removeLayer(e)})}addPulsingAnimationToNearest(e){if(!e)return;this.clearPulseCircles();let t=Number(e.latitude),o=Number(e.longitude);if(isNaN(t)||isNaN(o)){console.error("Invalid coordinates for nearest center:",e);return}let n=this.getDisasterColor(e.disaster_type);l.circle([t,o],{radius:100,fillColor:n,color:n,weight:2,opacity:.8,fillOpacity:.3,className:"marker-pulse"}).addTo(this.map),console.log(`Added pulsing animation to nearest center: ${e.name} with color: ${n}`)}hasRecentErrorToast(){return Date.now()-this.lastErrorToast<this.ERROR_TOAST_DEBOUNCE}setLastErrorToast(){this.lastErrorToast=Date.now()}toggleGps(e){return v(this,null,function*(){if(console.log("GPS toggle:",e.detail.checked),this.gpsEnabled=e.detail.checked,this.gpsEnabled){console.log("Enabling GPS tracking...");try{let t=yield this.getCurrentPositionWithFallback();console.log("Position on toggle:",t);let o=t.coords.latitude,n=t.coords.longitude;this.userMarker?(this.userMarker.setLatLng([o,n]),this.userMarker.addTo(this.map)):this.updateUserMarker(o,n),this.map.setView([o,n],15),this.startWatchingPosition()}catch(t){console.error("Error enabling GPS:",t),this.gpsEnabled=!1,(yield this.toastCtrl.create({message:"Failed to enable GPS. Please check your location settings.",duration:3e3,color:"danger"})).present()}}else if(console.log("Disabling GPS tracking..."),this.userMarker&&this.userMarker.remove(),this.watchId){if(typeof this.watchId=="string")try{let t=this.watchId;x.clearWatch({id:t})}catch(t){console.log("Error clearing Capacitor watch:",t)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(t){console.log("Error clearing browser watch:",t)}this.watchId=null}})}ngOnInit(){return v(this,null,function*(){console.log("\u{1F5FA}\uFE0F MAIN MAP: Initializing clean map (tabs/map)..."),this.setupNetworkStatusMonitoring(),this.route.queryParams.subscribe(e=>{if(e.centerId){let t=e.centerId;console.log(`\u{1F50D} SEARCH NAVIGATION: Loading specific center ID: ${t}`),this.loadSpecificCenter(t);return}if(e.lat&&e.lng){let t=parseFloat(e.lat),o=parseFloat(e.lng),n=e.name||"Search Result",r=e.directions==="true",a=e.viewOnly==="true";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${t}, ${o}] - ${n}, directions: ${r}, viewOnly: ${a}`),r?this.loadSearchLocationWithRouting(t,o,n):a&&this.loadSearchLocation(t,o,n);return}if(e.searchLat&&e.searchLng){let t=parseFloat(e.searchLat),o=parseFloat(e.searchLng),n=e.searchName||"Search Result";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${t}, ${o}] - ${n}`),this.loadSearchLocation(t,o,n);return}if(e.emergency==="true"&&e.notification==="true"){console.log("\u{1F6A8} EMERGENCY NOTIFICATION: Handling disaster notification routing");let t=e.category||"unknown",o=e.severity||"medium",n=e.title||"Emergency Alert",r=e.message||"Emergency notification received";console.log("\u{1F4F1} Emergency notification details:",{category:t,severity:o,title:n,autoRoute:e.autoRoute==="true"}),this.showDisasterEmergencyAlert({category:t,severity:o,title:n,message:r,autoRoute:e.autoRoute==="true"}),this.loadEmergencyMap();return}console.log("\u{1F5FA}\uFE0F MAIN MAP: Loading clean map with user location only"),this.loadCleanMap()})})}loadCleanMap(){return v(this,null,function*(){console.log("\u{1F5FA}\uFE0F CLEAN MAP: Loading map with user location only..."),yield this.loadingService.showLoading("Loading map...");try{let e=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),t=e.coords.latitude,o=e.coords.longitude;console.log(`\u{1F5FA}\uFE0F CLEAN MAP: User location [${t}, ${o}]`),this.initializeMap(t,o),this.evacuationCenters=[],this.isFilterMode=!1,this.currentDisasterType="all",this.map.eachLayer(r=>{r instanceof l.Marker&&r!==this.userMarker&&this.map.removeLayer(r),r instanceof l.GeoJSON&&this.map.removeLayer(r)}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:"\u{1F4CD} Map ready - Search for evacuation centers to view them here",duration:3e3,color:"primary",position:"top"})).present()}catch(e){yield this.loadingService.dismissLoading(),console.error("\u{1F5FA}\uFE0F CLEAN MAP: Error loading map",e),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadCleanMap()},{text:"Use Default Location",handler:()=>this.initializeMap(10.3157,123.8854)}]})).present()}})}loadSpecificCenter(e){return v(this,null,function*(){console.log(`\u{1F50D} SPECIFIC CENTER: Loading center ID ${e}...`),yield this.loadingService.showLoading("Loading evacuation center...");try{let o=(yield W(this.http.get(`${T.apiUrl}/evacuation-centers`))).find(y=>y.id.toString()===e);if(!o){yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Center Not Found",message:"The requested evacuation center could not be found.",buttons:["OK"]})).present(),this.loadCleanMap();return}let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude,s=Number(o.latitude),i=Number(o.longitude);this.initializeMap(r,a);let c=this.getDisasterIcon(this.getPrimaryDisasterType(o.disaster_type)),h=l.marker([s,i],{icon:l.icon({iconUrl:c,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});h.bindPopup(`
        <div class="evacuation-popup">
          <h3>${o.name}</h3>
          <p><strong>Type:</strong> ${o.disaster_type||"General"}</p>
          <p><strong>Address:</strong> ${o.address}</p>
          <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
        </div>
      `).openPopup(),h.addTo(this.map);let u=l.latLngBounds([[r,a],[s,i]]);this.map.fitBounds(u,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${o.name}`,duration:3e3,color:"success"})).present()}catch(t){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SPECIFIC CENTER: Error loading center",t),yield(yield this.toastCtrl.create({message:"Error loading evacuation center. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadSearchLocation(e,t,o){return v(this,null,function*(){console.log(`\u{1F50D} SEARCH LOCATION: Loading [${e}, ${t}] - ${o}...`),yield this.loadingService.showLoading("Loading location...");try{let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude;this.initializeMap(r,a);let s=l.marker([e,t],{icon:l.icon({iconUrl:"assets/myLocation.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});s.bindPopup(`
        <div class="search-popup">
          <h3>\u{1F4CD} ${o}</h3>
          <p>Search result location</p>
        </div>
      `).openPopup(),s.addTo(this.map);let i=l.latLngBounds([[r,a],[e,t]]);this.map.fitBounds(i,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${o}`,duration:3e3,color:"primary"})).present()}catch(n){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SEARCH LOCATION: Error loading location",n),yield(yield this.toastCtrl.create({message:"Error loading search location. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadMapWithSearchLocation(e,t,o,n=!1){return v(this,null,function*(){yield this.loadingService.showLoading("Loading selected location...");try{if(console.log(`Initializing map with search location: [${e}, ${t}], name: ${o}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(e,t),this.map.eachLayer(a=>{a instanceof l.Marker&&a!==this.userMarker&&this.map.removeLayer(a),a instanceof l.GeoJSON&&this.map.removeLayer(a)}),l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup(`<b>${o}</b><br>Selected evacuation center`).openPopup(),this.gpsEnabled)try{let a=yield this.getCurrentPositionWithFallback(),s=a.coords.latitude,i=a.coords.longitude;if(this.updateUserMarker(s,i),n){this.map.eachLayer(h=>{h instanceof l.GeoJSON&&this.map.removeLayer(h)}),yield this.getRealRoute(s,i,e,t,this.travelMode),this.toastCtrl.create({message:`Showing directions to ${o}`,duration:3e3,color:"success"}).then(h=>h.present());let c=l.latLngBounds([[s,i],[e,t]]);this.map.fitBounds(c,{padding:[50,50]})}else this.toastCtrl.create({message:`Showing ${o} on map`,duration:2e3,color:"primary"}).then(c=>c.present())}catch(a){console.error("Error getting user location for routing:",a),n&&this.toastCtrl.create({message:"Could not get your location to calculate directions. Please check your GPS settings.",duration:3e3,color:"warning"}).then(s=>s.present())}else n&&this.toastCtrl.create({message:"Please enable GPS to get directions",duration:3e3,color:"warning"}).then(a=>a.present());yield this.loadingService.dismissLoading()}catch(r){console.error("Error loading search location:",r),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load selected location. Please try again.",duration:3e3,color:"danger"}).then(a=>a.present()),this.loadMapWithUserLocation()}})}loadSearchLocationWithRouting(e,t,o){return v(this,null,function*(){console.log(`\u{1F50D} SEARCH ROUTING: Loading search location with routing [${e}, ${t}] - ${o}`);try{let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude;console.log(`\u{1F50D} SEARCH ROUTING: User location [${r}, ${a}]`),console.log(`\u{1F50D} SEARCH ROUTING: Target location [${e}, ${t}] - ${o}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(r,a),this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&this.map.removeLayer(i),i instanceof l.GeoJSON&&this.map.removeLayer(i)}),this.userMarker=l.marker([r,a],{icon:l.icon({iconUrl:"assets/myLocation.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!"),l.marker([e,t],{icon:l.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40]})}).addTo(this.map).bindPopup(`<b>${o}</b><br>Selected evacuation center`).openPopup(),yield this.showTransportationOptionsForSearch(e,t,o),console.log(`\u{1F50D} SEARCH ROUTING: Successfully loaded search location with routing: ${o}`)}catch(n){console.error("\u{1F50D} SEARCH ROUTING: Error loading search location with routing:",n),(yield this.toastCtrl.create({message:"Error getting your location for routing. Please enable GPS and try again.",duration:3e3,color:"danger"})).present()}})}showTransportationOptionsForSearch(e,t,o){return v(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${o}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToSearchLocation(e,t,o,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToSearchLocation(e,t,o,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToSearchLocation(e,t,o,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToSearchLocation(e,t,o,n){return v(this,null,function*(){if(this.userMarker)try{let r=this.userMarker.getLatLng().lat,a=this.userMarker.getLatLng().lng,s=this.mapboxRouting.convertTravelModeToProfile(n),i=yield this.mapboxRouting.getDirections(a,r,t,e,s);if(i&&i.routes&&i.routes.length>0){let c=i.routes[0],h="#3880ff";this.map.eachLayer(y=>{y instanceof l.GeoJSON&&this.map.removeLayer(y)});let u=l.polyline(c.geometry.coordinates.map(y=>[y[1],y[0]]),{color:h,weight:5,opacity:.8});u.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Route: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(u.getBounds(),{padding:[50,50]})}}catch(r){console.error("\u{1F50D} Error routing to search location:",r),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}ngOnDestroy(){console.log("Map page destroyed, cleaning up resources"),this.stopWatchingPosition(),this.map&&this.map.remove()}getCurrentPositionWithFallback(){return v(this,null,function*(){try{console.log("Trying Capacitor Geolocation...");try{let e=yield x.checkPermissions();if(console.log("Permission status:",e),e.location!=="granted"){console.log("Requesting permissions explicitly...");let t=yield x.requestPermissions();if(console.log("Permission request result:",t),t.location!=="granted")throw new Error("Location permission denied")}}catch(e){console.log("Permission check failed, might be in browser:",e)}try{return console.log("Getting current position via Capacitor..."),yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4})}catch(e){throw console.log("Capacitor Geolocation failed, trying browser fallback:",e),e}}catch{if(console.log("Trying browser geolocation fallback..."),navigator.geolocation)return new Promise((t,o)=>{navigator.geolocation.getCurrentPosition(n=>{console.log("Browser geolocation succeeded:",n),t({coords:{latitude:n.coords.latitude,longitude:n.coords.longitude,accuracy:n.coords.accuracy,altitude:n.coords.altitude,altitudeAccuracy:n.coords.altitudeAccuracy,heading:n.coords.heading,speed:n.coords.speed},timestamp:n.timestamp})},n=>{if(console.error("Browser geolocation failed:",n),n.code===1&&n.message.includes("secure origins")){let r=new Error("Geolocation requires HTTPS. Please use a secure connection, run on a real device, or enable insecure origins in Chrome flags.");r.code=n.code,o(r)}else o(n)},{enableHighAccuracy:!0,timeout:1e4})});throw console.error("Geolocation not available in this browser"),new Error("Geolocation not available in this browser")}})}loadMapWithDisasterFilter(e,t=!1,o=!1){return v(this,null,function*(){yield this.loadingService.showLoading(`Loading ${e==="all"?"all evacuation centers":e+" evacuation centers"}...`);try{console.log("Getting user location for disaster map...");try{let n=yield this.getCurrentPositionWithFallback();console.log("Position received:",n);let r=n.coords.latitude,a=n.coords.longitude;console.log(`Initializing disaster map with real GPS coordinates: [${r}, ${a}]`),this.initializeMap(r,a),this.startWatchingPosition(),yield this.loadEvacuationCentersFiltered(r,a,e),t?this.toastCtrl.create({message:`\u{1F6A8} EMERGENCY: Showing nearest ${e} evacuation centers with routes`,duration:5e3,color:"danger",position:"top"}).then(s=>s.present()):this.toastCtrl.create({message:`Showing ${e==="all"?"all evacuation centers":e+" evacuation centers"} near you`,duration:3e3,color:"primary"}).then(s=>s.present()),yield this.loadingService.dismissLoading();return}catch(n){console.error("Failed to get GPS position for disaster map:",n),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"GPS Required",message:"We need your location to show nearby evacuation centers. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithDisasterFilter(e)}},{text:"Cancel",role:"cancel"}]})).present()}}catch(n){console.error("Error loading disaster map:",n),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(r=>r.present())}})}loadMapWithOnlyUserLocation(){return v(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location for map tab...");try{let e=yield this.getCurrentPositionWithFallback();console.log("Position received:",e);let t=e.coords.latitude,o=e.coords.longitude;console.log(`Initializing map with only user location: [${t}, ${o}]`),this.isFilterMode=!1,this.currentDisasterType="all",this.evacuationCenters=[],this.initializeMap(t,o),this.map.eachLayer(n=>{n instanceof l.Marker&&n!==this.userMarker&&this.map.removeLayer(n),n instanceof l.GeoJSON&&this.map.removeLayer(n)}),this.startWatchingPosition(),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.toastCtrl.create({message:"Showing your current location",duration:2e3,color:"success"}).then(n=>n.present()),yield this.loadingService.dismissLoading();return}catch(e){console.error("Failed to get GPS position for map tab:",e),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Location Required",message:"We need your location to show the map. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithOnlyUserLocation()}},{text:"Cancel",role:"cancel"}]})).present();return}}catch(e){console.error("Error loading map:",e),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load map. Please try again.",duration:3e3,color:"danger"}).then(t=>t.present())}})}loadMapWithUserLocation(){return v(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location...");try{let e=yield this.getCurrentPositionWithFallback();console.log("Position received:",e);let t=e.coords.latitude,o=e.coords.longitude;console.log(`Initializing map with real GPS coordinates: [${t}, ${o}]`),this.isFilterMode||(this.currentDisasterType="all"),this.initializeMap(t,o),this.startWatchingPosition(),this.toastCtrl.create({message:"Using your real-time location",duration:2e3,color:"success"}).then(n=>n.present()),yield this.loadingService.dismissLoading();return}catch(e){throw console.error("Failed to get GPS position, showing alert:",e),e}}catch(e){console.error("Error getting location",e);let t="Unable to access your location. ";e.code===1?navigator.userAgent.includes("Chrome")?t+='Location permission denied. Please click the lock icon in the address bar, select "Site settings", and change Location permission to "Allow".':navigator.userAgent.includes("Firefox")?t+='Location permission denied. Please click the lock icon in the address bar, select "Site Permissions", and change "Access Your Location" to "Allow".':navigator.userAgent.includes("Safari")?t+='Location permission denied. Please check Safari settings > Websites > Location and ensure this website is set to "Allow".':t+="Location permission denied. Please enable location access for this website in your browser settings.":e.code===2?t+="Position unavailable. Your GPS signal might be weak or unavailable.":e.code===3?t+="Location request timed out. Please try again.":t+="Please enable GPS or try again. "+(e.message||""),yield(yield this.alertCtrl.create({header:"Location Error",message:t,buttons:[{text:"Retry",handler:()=>{this.loadMapWithUserLocation()}},{text:"Load Default Map",role:"cancel",handler:()=>{this.initializeMap(10.3157,123.8854)}}]})).present()}yield this.loadingService.dismissLoading()})}stopWatchingPosition(){if(this.watchId){if(console.log("Stopping position watch..."),typeof this.watchId=="string")try{let e=this.watchId;x.clearWatch({id:e})}catch(e){console.log("Error clearing Capacitor watch:",e)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(e){console.log("Error clearing browser watch:",e)}this.watchId=null}}startWatchingPosition(){this.stopWatchingPosition(),console.log("Starting position watch...");try{this.watchId=x.watchPosition({enableHighAccuracy:!0,timeout:1e4},(e,t)=>{e&&this.gpsEnabled&&(console.log("Capacitor watch position update:",e),this.updateUserMarker(e.coords.latitude,e.coords.longitude)),t&&(console.error("Error watching position:",t),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(o=>o.present()))}),console.log("Capacitor watch started with ID:",this.watchId)}catch(e){console.log("Capacitor watch failed, trying browser fallback:",e),navigator.geolocation?(this.watchId=navigator.geolocation.watchPosition(t=>{this.gpsEnabled&&(console.log("Browser watch position update:",t),this.updateUserMarker(t.coords.latitude,t.coords.longitude))},t=>{console.error("Browser watch error:",t),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(o=>o.present())},{enableHighAccuracy:!0,timeout:1e4}),console.log("Browser watch started with ID:",this.watchId)):console.error("Geolocation watching not available")}}initializeMap(e,t){console.log(`Initializing map with coordinates: [${e}, ${t}]`),(isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180)&&(console.error("Invalid coordinates for map initialization:",{lat:e,lng:t}),e=12.8797,t=121.774,console.log(`Using fallback coordinates for Philippines: [${e}, ${t}]`)),this.map&&(console.log("Removing existing map"),this.map.remove()),this.map=l.map("map").setView([e,t],15),console.log("Map initialized"),this.cacheMapData(e,t),console.log("\u{1F310} Loading online map tiles..."),l.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map),this.gpsEnabled?(console.log("GPS is enabled, adding user marker"),this.userMarker?(this.userMarker.setLatLng([e,t]),this.userMarker.addTo(this.map),console.log("Updated existing user marker")):(this.userMarker=l.marker([e,t],{icon:l.icon({iconUrl:"assets/myLocation.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker")),this.toastCtrl.create({message:"Using your real-time GPS location",duration:2e3,color:"success"}).then(o=>o.present())):console.log("GPS is disabled, not adding user marker"),this.isFilterMode&&this.currentDisasterType!=="all"?(console.log("Loading evacuation centers for filter mode"),this.loadEvacuationCenters(e,t)):(console.log("\u{1F5FA}\uFE0F CLEAN MAP: Skipping evacuation centers - showing only user location"),this.evacuationCenters=[])}updateUserMarker(e,t){if(console.log(`Updating user marker to: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid coordinates for user marker update:",{lat:e,lng:t});return}if(this.userMarker){let o=this.userMarker.getLatLng();this.userMarker.setLatLng([e,t]),this.map.setView([e,t]),console.log("Updated existing user marker position");let n=this.calculateDistance(o.lat,o.lng,e,t);console.log(`User moved ${n.toFixed(2)} meters from previous position`),n>20&&(console.log(`Significant movement detected (${n.toFixed(2)}m), recalculating routes`),this.map.eachLayer(r=>{r instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(r))}),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Recalculating routes to nearest evacuation centers"),this.routeToTwoNearestCenters()))}else this.userMarker=l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker"),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Calculating initial routes with real GPS data"),this.routeToTwoNearestCenters())}loadEvacuationCentersFiltered(e,t,o){return v(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers for disaster type: ${o}`),console.log(`User coordinates: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:e,userLng:t}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}this.currentDisasterType=o;let n=[];yield this.offlineStorage.cacheUserLocation({lat:e,lng:t}),console.log("\u{1F310} Fetching evacuation centers from:",`${T.apiUrl}/evacuation-centers`);try{let i=yield W(this.http.get(`${T.apiUrl}/evacuation-centers`));console.log("\u{1F4E1} RAW API RESPONSE:",i),n=i.data||[],console.log("\u{1F4CA} TOTAL CENTERS RECEIVED:",n?.length||0),n.length>0&&(yield this.offlineStorage.cacheEvacuationCenters(n),console.log("\u{1F4BE} Cached evacuation centers for offline use"))}catch(i){console.error("\u274C Failed to fetch evacuation centers from API:",i),console.log("\u{1F504} Attempting to load from offline cache...");let c=yield this.offlineStorage.getCachedEvacuationCenters();if(c&&c.length>0)n=c,console.log(`\u{1F4E6} Loaded ${n.length} evacuation centers from offline cache`),this.toastCtrl.create({message:`\u{1F4F1} Offline mode: Using cached data for ${o} centers`,duration:3e3,color:"warning"}).then(h=>h.present());else{console.log("\u274C No cached evacuation centers available"),this.toastCtrl.create({message:"No evacuation centers available. Please check your internet connection.",duration:3e3,color:"danger"}).then(h=>h.present());return}}if(n&&n.length>0){let i=[...new Set(n.map(c=>c.disaster_type))];console.log("\u{1F3F7}\uFE0F UNIQUE DISASTER TYPES IN DATABASE:",i),i.forEach(c=>{let h=n.filter(u=>u.disaster_type===c).length;console.log(`   \u{1F4C8} ${c}: ${h} centers`)}),console.log("\u{1F50D} SAMPLE CENTERS:"),n.slice(0,5).forEach((c,h)=>{console.log(`   ${h+1}. "${c.name}" - Type: "${c.disaster_type}" - Status: "${c.status}"`)})}this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&this.map.removeLayer(i)}),this.map.eachLayer(i=>{i instanceof l.GeoJSON&&this.map.removeLayer(i)});let r=n||[];if(o!=="all"){console.log(`\u{1F50D} FILTERING centers for disaster type: "${o}"`),console.log(`\u{1F4CA} Total centers before filtering: ${r.length}`),console.log("\u{1F4CB} All centers disaster types:",n.map(c=>`${c.name}: "${c.disaster_type}"`)),r=r.filter(c=>{if(!c.disaster_type)return console.log(`\u274C Center "${c.name}" has no disaster_type, excluding`),!1;let h=this.getPrimaryDisasterType(c.disaster_type).trim(),u=o.trim(),w=h===u;return console.log(`\u{1F3E2} Center "${c.name}"`),console.log(`   \u{1F4CD} Center Type: "${h}" (length: ${h.length})`),console.log(`   \u{1F3AF} Looking for: "${u}" (length: ${u.length})`),console.log(`   \u2705 Match: ${w}`),w}),console.log(`\u{1F3AF} FILTERED RESULT: ${r.length} centers for disaster type: "${o}"`),console.log("\u2705 INCLUDED CENTERS:"),r.forEach((c,h)=>{console.log(`   ${h+1}. ${c.name} (${c.disaster_type})`)});let i=n.filter(c=>c.disaster_type&&this.getPrimaryDisasterType(c.disaster_type).trim()!==o.trim());console.log("\u274C EXCLUDED CENTERS:"),i.forEach((c,h)=>{console.log(`   ${h+1}. ${c.name} (${c.disaster_type})`)}),r.length===0&&(console.error("\u{1F6A8} NO CENTERS FOUND FOR DISASTER TYPE!"),console.error("\u{1F50D} Debug Info:"),console.error(`   Target disaster type: "${o}"`),console.error("   Available disaster types:",[...new Set(n.map(c=>c.disaster_type))]),console.error(`   Total centers in database: ${n.length}`))}console.log("\u{1F9F9} AGGRESSIVE CLEARING: Removing ALL existing markers");let a=[];if(this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&(console.log("\u{1F5D1}\uFE0F Marking marker for removal:",i),a.push(i)),i instanceof l.GeoJSON&&(console.log("\u{1F5D1}\uFE0F Marking route for removal:",i),a.push(i))}),a.forEach(i=>{console.log("\u{1F5D1}\uFE0F Removing layer from map"),this.map.removeLayer(i)}),this.evacuationCenters=[],console.log(`\u{1F9F9} CLEARED: Removed ${a.length} layers from map`),this.evacuationCenters=r,this.evacuationCenters.length===0){console.log(`\u{1F6A8} NO EVACUATION CENTERS FOUND for disaster type: "${o}"`),this.alertCtrl.create({header:"No Evacuation Centers Found",message:`There are no evacuation centers stored for ${o==="all"?"any disaster type":o}. Please contact your administrator to add evacuation centers.`,buttons:["OK"]}).then(i=>i.present()),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.map.setView([e,t],15);return}console.log(`\u{1F3AF} ADDING ${this.evacuationCenters.length} FILTERED MARKERS to map`),console.log(`\u{1F4CD} Disaster type filter: "${o}"`),console.log("\u{1F3E2} Centers to display:",this.evacuationCenters.map(i=>`${i.name} (${i.disaster_type})`)),this.evacuationCenters.forEach((i,c)=>{let h=Number(i.latitude),u=Number(i.longitude);if(console.log(`\u{1F3E2} Processing center ${c+1}/${this.evacuationCenters.length}: ${i.name}`),console.log(`   \u{1F4CD} Coordinates: [${h}, ${u}]`),console.log(`   \u{1F3F7}\uFE0F Disaster Type: "${i.disaster_type}"`),console.log(`   \u{1F3AF} Filter Type: "${o}"`),!isNaN(h)&&!isNaN(u)){let w=this.getDisasterIcon(this.getPrimaryDisasterType(i.disaster_type));console.log(`   \u{1F3A8} Icon URL: ${w}`);let y=l.marker([h,u],{icon:l.icon({iconUrl:w,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),B=`
            <div class="evacuation-popup">
              <h3>${i.name||"Evacuation Center"}</h3>
              <p><strong>Type:</strong> ${i.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(this.calculateDistance(e,t,h,u)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;y.bindPopup(B),y.on("click",()=>{setTimeout(()=>{y.closePopup(),this.showEvacuationCenterDetails(i,e,t)},300)}),y.addTo(this.map),console.log(`   \u2705 MARKER ADDED to map for: ${i.name} (${i.disaster_type})`)}else console.error(`   \u274C Invalid coordinates for center: ${i.name}`)}),console.log(`\u{1F389} COMPLETED: Added ${this.evacuationCenters.length} markers for disaster type "${o}"`);let s=0;if(this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&s++}),console.log(`\u{1F50D} VERIFICATION: ${s} evacuation center markers currently on map`),this.gpsEnabled&&this.userMarker&&this.evacuationCenters.length>0){console.log("GPS enabled and user marker exists, finding nearest centers");let i=this.findTwoNearestCenters(e,t,this.evacuationCenters);if(i.length>0){this.addPulsingAnimationToNearest(i[0]);for(let u of i){if(u.routing_available===!1){console.log(`Skipping route to ${u.name} - routing not available (center is full)`);continue}let w=Number(u.latitude),y=Number(u.longitude);if(console.log(`Calculating route to center: ${u.name}`),console.log(`Center coordinates: [${w}, ${y}], types: [${typeof w}, ${typeof y}]`),isNaN(w)||isNaN(y)){console.error("Invalid center coordinates:",{centerLat:w,centerLng:y,center:u});continue}yield this.getRealRoute(e,t,w,y,this.travelMode,u.disaster_type)}let c="You are here!.";i.forEach((u,w)=>{let y=this.calculateDistance(e,t,Number(u.latitude),Number(u.longitude));c+=`<br> \u2022 <strong>${w+1}: ${u.name} </strong> <br> Distance: ${(y/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(c).openPopup();let h=l.latLngBounds([]);h.extend([e,t]),i.forEach(u=>{h.extend([Number(u.latitude),Number(u.longitude)])}),this.map.fitBounds(h,{padding:[50,50]})}else console.log("No nearest centers found"),this.map.setView([e,t],15)}else console.log("GPS disabled, no user marker, or no centers found, skipping route calculation"),this.map.setView([e,t],15)}catch(n){console.error("Error loading filtered evacuation centers:",n),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}loadEvacuationCenters(e,t){return v(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers with user coordinates: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:e,userLng:t}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}let o=[];yield this.offlineStorage.cacheUserLocation({lat:e,lng:t}),console.log("\u{1F310} Fetching evacuation centers from:",`${T.apiUrl}/evacuation-centers`);try{o=(yield W(this.http.get(`${T.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F4E1} Received centers from API:",o),o.length>0&&(yield this.offlineStorage.cacheEvacuationCenters(o),console.log("\u{1F4BE} Cached evacuation centers for offline use"))}catch(n){console.error("\u274C Failed to fetch evacuation centers from API:",n),console.log("\u{1F504} Attempting to load from offline cache...");let r=yield this.offlineStorage.getCachedEvacuationCenters();if(r&&r.length>0)o=r,console.log(`\u{1F4E6} Loaded ${o.length} evacuation centers from offline cache`),this.toastCtrl.create({message:`\u{1F4F1} Offline mode: Loaded ${o.length} cached evacuation centers`,duration:3e3,color:"warning"}).then(a=>a.present());else{console.log("\u274C No cached evacuation centers available"),this.toastCtrl.create({message:"No evacuation centers available. Please check your internet connection.",duration:3e3,color:"danger"}).then(a=>a.present());return}}if(this.evacuationCenters=o||[],this.map.eachLayer(n=>{n instanceof l.Marker&&n!==this.userMarker&&this.map.removeLayer(n)}),this.evacuationCenters.forEach(n=>{let r=Number(n.latitude),a=Number(n.longitude);if(console.log(`Processing center: ${n.name}, coordinates: [${r}, ${a}]`),!isNaN(r)&&!isNaN(a)){let s=this.getDisasterIcon(this.getPrimaryDisasterType(n.disaster_type)),i=l.marker([r,a],{icon:l.icon({iconUrl:s,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),c=`
            <div class="evacuation-popup">
              <h3>${n.name||"Evacuation Center"}</h3>
              <p><strong>Distance:</strong> ${(this.calculateDistance(e,t,r,a)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;i.bindPopup(c),i.on("click",()=>{setTimeout(()=>{i.closePopup(),this.showEvacuationCenterDetails(n,e,t)},300)}),i.addTo(this.map),console.log(`Added marker for center: ${n.name}`)}else console.error(`Invalid coordinates for center: ${n.name}`)}),this.gpsEnabled&&this.userMarker){console.log("GPS enabled and user marker exists, finding nearest centers");let n=this.findTwoNearestCenters(e,t,this.evacuationCenters);if(n.length>0){this.addPulsingAnimationToNearest(n[0]),this.map.eachLayer(a=>{a instanceof l.GeoJSON&&this.map.removeLayer(a)});for(let a of n){if(a.routing_available===!1){console.log(`Skipping route to ${a.name} - routing not available (center is full)`);continue}let s=Number(a.latitude),i=Number(a.longitude);if(console.log(`Calculating route to center: ${a.name}`),console.log(`Center coordinates: [${s}, ${i}], types: [${typeof s}, ${typeof i}]`),isNaN(s)||isNaN(i)){console.error("Invalid center coordinates:",{centerLat:s,centerLng:i,center:a});continue}yield this.getRealRoute(e,t,s,i,this.travelMode,a.disaster_type)}let r="You are here!.";n.forEach((a,s)=>{let i=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude));r+=`<br> \u2022${s+1}: ${a.name} <br> Distance: ${(i/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(r).openPopup()}else console.log("No nearest centers found"),this.map.setView([e,t],15)}else console.log("GPS disabled or no user marker, skipping route calculation"),this.map.setView([e,t],15)}catch(o){console.error("Failed to load evacuation centers",o),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}getRealRoute(s,i,c,h){return v(this,arguments,function*(e,t,o,n,r=this.travelMode,a){if(console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(u=>{u instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(u))}),console.log("Requesting Mapbox route with coordinates:",{startLat:e,startLng:t,endLat:o,endLng:n,travelMode:r}),[e,t,o,n].some(u=>typeof u!="number"||isNaN(u))){(yield this.toastCtrl.create({message:"Invalid route coordinates. Cannot request directions.",duration:3e3,color:"danger"})).present();return}if(Math.abs(e)>90||Math.abs(o)>90||Math.abs(t)>180||Math.abs(n)>180){(yield this.toastCtrl.create({message:"Route coordinates out of range. Cannot request directions.",duration:3e3,color:"danger"})).present();return}try{console.log("Sending route request to OpenStreetMap");let u=this.mapboxRouting.convertTravelModeToProfile(r),w=yield this.mapboxRouting.getDirections(t,e,n,o,u);if(!w.routes||w.routes.length===0)throw new Error("No routes found");let y=w.routes[0],B=this.osmRouting.convertToGeoJSON(y),L="#3388ff";if(a)switch(a){case"Earthquake":L="#ffa500";break;case"Flood":L="#0000ff";break;case"Typhoon":L="#008000";break;default:console.warn(`Unknown disaster type for route color: ${a}`);break}console.log(`Route calculation - Disaster type: "${a}", Normalized type: "${a?a.toLowerCase():"none"}", Selected color: ${L}`),this.currentDisasterType&&this.currentDisasterType!=="all"&&(this.currentDisasterType==="Earthquake"?L="#ffa500":this.currentDisasterType==="Typhoon"?L="#008000":this.currentDisasterType==="Flood"&&(L="#0000ff"),console.log(`Filter mode active: ${this.currentDisasterType}, forcing route color to: ${L}`)),console.log(`Using route color: ${L} for disaster type: ${a||"unknown"}`);let $e=l.geoJSON(B,{style:{color:L,weight:5,opacity:.8}}).addTo(this.map);this.routeTime=y.duration,this.routeDistance=y.distance;let oe=this.osmRouting.getRouteSummary(y);console.log(`OpenStreetMap route summary: ${oe.duration}, ${oe.distance}`),this.map.fitBounds($e.getBounds(),{padding:[50,50]})}catch(u){console.error("Failed to fetch route from Mapbox",u);let w="Failed to fetch route. Please check your internet connection or try again later.";u.message?u.message.includes("Invalid Mapbox access token")?w="Invalid Mapbox access token. Please check your token configuration.":u.message.includes("Rate limit exceeded")?w="Too many requests to Mapbox. Please wait a moment and try again.":u.message.includes("Network error")?w="Network error. Please check your internet connection.":u.message.includes("No routes found")?w="No route could be calculated between these points.":w=`Mapbox routing error: ${u.message}`:u.status===401?w="Invalid Mapbox access token. Please check your token.":u.status===422?w="Invalid coordinates or routing parameters.":u.status===429?w="Rate limit exceeded. Please try again later.":u.status===0&&(w="Network error. Please check your internet connection.");let y=r==="foot-walking"?"walking":r==="cycling-regular"?"cycling":r==="driving-car"?"driving":r;this.hasRecentErrorToast()||((yield this.toastCtrl.create({message:`Failed to fetch ${y} route: ${w}`,duration:5e3,color:"danger"})).present(),this.setLastErrorToast())}})}findNearestCenter(e,t,o){if(!o.length)return null;let n=o[0],r=this.calculateDistance(e,t,Number(n.latitude),Number(n.longitude));for(let a of o){let s=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude));s<r&&(r=s,n=a)}return n}calculateDistance(e,t,o,n){let a=e*Math.PI/180,s=o*Math.PI/180,i=(o-e)*Math.PI/180,c=(n-t)*Math.PI/180,h=Math.sin(i/2)*Math.sin(i/2)+Math.cos(a)*Math.cos(s)*Math.sin(c/2)*Math.sin(c/2);return 6371e3*(2*Math.atan2(Math.sqrt(h),Math.sqrt(1-h)))}downloadMap(){return v(this,null,function*(){try{yield this.loadingService.showLoading("Capturing map...");let e=document.getElementById("map");if(!e)throw new Error("Map element not found");console.log("Capturing map as image...");let t=yield(0,Ie.default)(e,{useCORS:!0,allowTaint:!0,scrollX:0,scrollY:0,windowWidth:document.documentElement.offsetWidth,windowHeight:document.documentElement.offsetHeight,scale:1});yield this.loadingService.dismissLoading();let o=t.toDataURL("image/png"),a=`evacuation-map-${new Date().toISOString().replace(/[:.]/g,"-").substring(0,19)}.png`;yield(yield this.alertCtrl.create({header:"Map Captured",message:"Your map has been captured. What would you like to do with it?",buttons:[{text:"Download",handler:()=>{this.downloadImage(o,a)}},{text:"Share",handler:()=>{this.shareImage(o,a)}},{text:"Cancel",role:"cancel"}]})).present()}catch(e){console.error("Error capturing map:",e),yield this.loadingService.dismissLoading(),(yield this.toastCtrl.create({message:"Failed to capture map. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadImage(e,t){let o=document.createElement("a");o.href=e,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),this.toastCtrl.create({message:"Map downloaded successfully",duration:2e3,color:"success"}).then(n=>n.present())}shareImage(e,t){return v(this,null,function*(){try{if(navigator.share){let o=yield(yield fetch(e)).blob(),n=new File([o],t,{type:"image/png"});yield navigator.share({title:"Evacuation Map",text:"Here is my evacuation map with routes to the nearest evacuation centers",files:[n]}),console.log("Map shared successfully")}else console.log("Web Share API not supported"),(yield this.toastCtrl.create({message:"Sharing not supported on this device. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(e,t)}catch(o){console.error("Error sharing map:",o),(yield this.toastCtrl.create({message:"Failed to share map. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(e,t)}})}showEvacuationCenterDetails(e,t,o){return v(this,null,function*(){console.log("Showing evacuation center details for:",e.name);let n=yield this.modalCtrl.create({component:Ee,componentProps:{center:e,userLat:t,userLng:o},cssClass:"evacuation-details-modal",breakpoints:[0,.5,.75,1],initialBreakpoint:.75});yield n.present();let{data:r}=yield n.onDidDismiss();if(r&&r.selectedMode&&(console.log("Selected travel mode:",r.selectedMode),this.travelMode=r.selectedMode,console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(a=>{a instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(a))}),this.userMarker)){let a=this.userMarker.getLatLng(),s=Number(e.latitude),i=Number(e.longitude);if(console.log("Recalculating route with new travel mode:",{userLat:a.lat,userLng:a.lng,centerLat:s,centerLng:i,travelMode:this.travelMode}),isNaN(s)||isNaN(i)){console.error("Invalid center coordinates:",{centerLat:s,centerLng:i}),(yield this.toastCtrl.create({message:"Invalid evacuation center coordinates. Cannot calculate route.",duration:3e3,color:"danger"})).present();return}this.toastCtrl.create({message:`Showing ${this.getTravelModeName().toLowerCase()} route to ${e.name}`,duration:2e3,color:"primary"}).then(c=>c.present()),yield this.getRealRoute(a.lat,a.lng,s,i,this.travelMode,this.getPrimaryDisasterType(e.disaster_type))}})}showDisasterEmergencyAlert(e){return v(this,null,function*(){let t={flood:"\u{1F30A}",typhoon:"\u{1F32A}\uFE0F",fire:"\u{1F525}",landslide:"\u26F0\uFE0F",default:"\u26A0\uFE0F"},o=t[e.category]||t.default,n=e.category.toUpperCase();yield(yield this.alertCtrl.create({header:`${o} ${n} EMERGENCY`,subHeader:e.title||"Emergency Notification",message:`
        <div style="text-align: left;">
          <p><strong>Alert:</strong> ${e.message||`${n} emergency detected`}</p>
          <p><strong>Severity:</strong> ${(e.severity||"medium").toUpperCase()}</p>
          <p><strong>Action:</strong> Showing all evacuation centers for ${e.category} emergency</p>
          <p><strong>Map View:</strong> General evacuation centers map</p>
        </div>
      `,buttons:[{text:"Show All Centers",role:"confirm",cssClass:"alert-button-confirm",handler:()=>{console.log(`\u{1F6A8} User confirmed viewing all centers for ${e.category} emergency`)}},{text:"View Map Only",role:"cancel",cssClass:"alert-button-cancel",handler:()=>{console.log("\u{1F4CD} User chose to view map without emergency centers"),this.loadCleanMap()}}],cssClass:"emergency-alert"})).present()})}loadEmergencyMap(){return v(this,null,function*(){console.log("\u{1F6A8} EMERGENCY MAP: Loading all evacuation centers for emergency..."),yield this.loadingService.showLoading("Loading emergency evacuation centers...");try{let e=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),t=e.coords.latitude,o=e.coords.longitude;console.log(`\u{1F4CD} User location: [${t}, ${o}]`),this.initializeMap(t,o),yield this.loadEvacuationCenters(t,o),yield(yield this.toastController.create({message:"\u{1F6A8} Emergency mode: Showing all evacuation centers",duration:3e3,color:"danger",position:"top",cssClass:"emergency-toast"})).present(),yield this.loadingService.dismissLoading()}catch(e){console.error("\u274C Error loading emergency map:",e),yield this.loadingService.dismissLoading(),this.loadCleanMap(),yield(yield this.toastController.create({message:"\u26A0\uFE0F Could not load emergency centers. Showing basic map.",duration:3e3,color:"warning",position:"top"})).present()}})}static{this.\u0275fac=function(t){return new(t||g)}}static{this.\u0275cmp=D({type:g,selectors:[["app-map"]],standalone:!0,features:[A],decls:23,vars:14,consts:[[3,"translucent"],[3,"fullscreen"],["collapse","condense"],["size","large"],["id","map"],["class","location-request-container",4,"ngIf"],["class","route-summary-card",3,"click",4,"ngIf"],[3,"directions","travelMode","totalDistance","totalDuration","close",4,"ngIf"],["vertical","top","horizontal","end","slot","fixed"],["size","small",3,"click","color"],[3,"name"],[1,"gps-status",3,"click"],["class","disaster-type-indicator",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","start","slot","fixed",4,"ngIf"],[1,"location-request-container"],["expand","block","color","primary",3,"click"],["name","locate","slot","start"],[1,"location-help-text"],[1,"route-summary-card",3,"click"],[3,"name","color"],[1,"summary-text"],[1,"travel-mode"],["name","chevron-up",1,"expand-icon"],[3,"close","directions","travelMode","totalDistance","totalDuration"],[1,"disaster-type-indicator"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],[1,"fab-label"],["vertical","bottom","horizontal","start","slot","fixed"],["color","tertiary",3,"click"],["name","list-outline"]],template:function(t,o){t&1&&(d(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),f(3,"Map"),m()()(),d(4,"ion-content",1)(5,"ion-header",2)(6,"ion-toolbar")(7,"ion-title",3),f(8,"Map"),m()()(),b(9,"div",4),k(10,Ve,6,0,"div",5)(11,He,9,5,"div",6)(12,Be,1,4,"app-directions-panel",7),d(13,"ion-fab",8)(14,"ion-fab-button",9),_("click",function(){return o.toggleGps({detail:{checked:!o.gpsEnabled}})}),b(15,"ion-icon",10),m()(),d(16,"div",11),_("click",function(){return o.showLocationHelp()}),b(17,"ion-icon",10),d(18,"span"),f(19),m()(),k(20,Ye,4,2,"div",12)(21,Je,5,0,"ion-fab",13)(22,je,5,0,"ion-fab",14),m()),t&2&&(C("translucent",!0),p(4),C("fullscreen",!0),p(6),C("ngIf",o.showLocationRequestButton),p(),C("ngIf",o.routeTime&&o.routeDistance),p(),C("ngIf",o.showDirectionsPanel&&o.currentDirections.length>0),p(2),C("color",o.gpsEnabled?"success":"medium"),p(),C("name",o.gpsEnabled?"locate":"locate-outline"),p(),re("active",o.gpsEnabled),p(),C("name",o.gpsEnabled?"location":"location-outline"),p(2),O("GPS ",o.gpsEnabled?"Active":"Inactive",""),p(),C("ngIf",o.isFilterMode&&o.currentDisasterType!=="all"),p(),C("ngIf",o.isFilterMode||o.evacuationCenters.length>0),p(),C("ngIf",o.currentDirections.length>0&&!o.showDirectionsPanel))},dependencies:[q,z,j,fe,ve,K,G,U,X,Q,F,R,le,Le],styles:['ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#fff;font-weight:600}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{--color: white}ion-content[_ngcontent-%COMP%]{--background: #f5f5f5}.offline-status-banner[_ngcontent-%COMP%]{position:absolute;top:10px;left:50%;transform:translate(-50%);background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border-radius:20px;padding:8px 16px;display:flex;align-items:center;gap:8px;z-index:1001;box-shadow:0 2px 8px #0003;font-size:14px;font-weight:500;animation:_ngcontent-%COMP%_slideDown .3s ease-out}.offline-status-banner[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}#map[_ngcontent-%COMP%]{width:100%;height:100%}.mode-segment[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:10px;z-index:1000;background:#ffffffe6;border-radius:20px;padding:4px;width:90%;max-width:400px;box-shadow:0 2px 8px #0000001a}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--background: transparent;--background-checked: var(--ion-color-light);--indicator-color: transparent;--border-radius: 16px;min-height:40px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:block;margin:0 auto 4px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-summary-card[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:70px;background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;display:flex;align-items:center;gap:12px;z-index:1000;cursor:pointer;transition:all .2s ease}.route-summary-card[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translate(-50%) translateY(-2px)}.route-summary-card[_ngcontent-%COMP%]:active{transform:translate(-50%) translateY(0)}.route-summary-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{line-height:1.3}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:16px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   .travel-mode[_ngcontent-%COMP%]{font-size:12px;opacity:.8;margin-top:2px}.route-summary-card[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:18px;margin-left:8px;color:var(--ion-color-medium)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:80px;bottom:30px;background:#fffffff2;padding:8px 16px;border-radius:20px;font-size:14px;color:var(--ion-color-primary);z-index:1000;box-shadow:0 2px 8px #0000001a;font-weight:500}ion-fab-button[activated][_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}.gps-status[_ngcontent-%COMP%]{position:absolute;top:10px;left:70px;background:var(--ion-color-medium);border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;color:#fff;cursor:pointer;transition:all .2s ease}.gps-status[_ngcontent-%COMP%]:hover{background:var(--ion-color-medium-shade);box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.gps-status[_ngcontent-%COMP%]:active{transform:translateY(0)}.gps-status.active[_ngcontent-%COMP%]{color:#fff;background:var(--ion-color-success)}.gps-status.active[_ngcontent-%COMP%]:hover{background:var(--ion-color-success-shade)}.gps-status.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite;color:#fff}.gps-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.gps-status[_ngcontent-%COMP%]:after{content:"?";display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background:var(--ion-color-medium);color:#fff;border-radius:50%;font-size:12px;margin-left:6px;opacity:.7}.disaster-type-indicator[_ngcontent-%COMP%]{position:absolute;top:10px;right:80px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;font-weight:500;color:var(--ion-color-dark)}.disaster-type-indicator[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-primary)}.location-request-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fffffff2;border-radius:16px;box-shadow:0 4px 16px #00000026;padding:20px;text-align:center;max-width:300px;width:90%;z-index:1001;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:10px 0;--border-radius: 10px;--box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), .3);font-weight:600;height:48px}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:active{--box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), .2);transform:translateY(2px)}.location-request-container[_ngcontent-%COMP%]   .location-help-text[_ngcontent-%COMP%]{margin:10px 0 0;font-size:14px;color:var(--ion-color-medium);line-height:1.4}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%,-40%)}to{opacity:1;transform:translate(-50%,-50%)}}.map-default-message[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;text-align:center;max-width:300px;z-index:1000}.map-default-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:var(--ion-color-primary);margin-bottom:8px}.map-default-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-weight:500;font-size:16px;color:var(--ion-color-dark)}.map-default-message[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:13px;display:block;line-height:1.4}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}@keyframes _ngcontent-%COMP%_pulsate{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.5);opacity:.4}to{transform:scale(.8);opacity:.8}}.marker-pulse-container[_ngcontent-%COMP%]{position:relative}.marker-pulse[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:50px;height:50px;margin-top:-25px;margin-left:-25px;border-radius:50%;z-index:100;pointer-events:none;animation:_ngcontent-%COMP%_pulsate 1.5s ease-out infinite;box-shadow:0 0 10px #00000080}[_nghost-%COMP%]     .popup-button{background-color:var(--ion-color-primary);color:#fff;border:none;border-radius:4px;padding:6px 12px;font-size:14px;cursor:pointer;margin-top:8px;transition:background-color .2s}[_nghost-%COMP%]     .popup-button:hover{background-color:var(--ion-color-primary-shade)}[_nghost-%COMP%]     .evacuation-popup h3{margin:0 0 8px;font-size:16px;font-weight:600}[_nghost-%COMP%]     .evacuation-popup p{margin:4px 0;font-size:14px}.evacuation-details-modal[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0;--backdrop-opacity: .4}']})}}return g})();export{xt as MapPage};
