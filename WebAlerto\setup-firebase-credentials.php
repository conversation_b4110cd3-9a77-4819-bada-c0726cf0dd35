<?php

echo "=== Firebase Credentials Setup Helper ===\n\n";

$credentialsPath = __DIR__ . '/storage/app/firebase-service-account.json';

echo "This script will help you set up Firebase credentials for push notifications.\n\n";

echo "STEP 1: Get your Firebase Service Account credentials\n";
echo "-----------------------------------------------\n";
echo "1. Go to: https://console.firebase.google.com/\n";
echo "2. Select your project: last-5acaf\n";
echo "3. Click the gear icon (⚙️) > Project Settings\n";
echo "4. Go to the 'Service accounts' tab\n";
echo "5. Click 'Generate new private key'\n";
echo "6. Download the JSON file\n\n";

echo "STEP 2: Install the credentials file\n";
echo "-----------------------------------\n";
echo "Save the downloaded JSON file as:\n";
echo "📁 $credentialsPath\n\n";

echo "STEP 3: Verify the file format\n";
echo "-----------------------------\n";
echo "The JSON file should contain these fields:\n";
echo "- type: \"service_account\"\n";
echo "- project_id: \"last-5acaf\"\n";
echo "- private_key_id: \"...\"\n";
echo "- private_key: \"-----BEGIN PRIVATE KEY-----...\"\n";
echo "- client_email: \"<EMAIL>\"\n";
echo "- client_id: \"...\"\n\n";

// Check if file already exists
if (file_exists($credentialsPath)) {
    echo "✅ CREDENTIALS FILE FOUND!\n";
    echo "File: $credentialsPath\n";
    
    try {
        $credentials = json_decode(file_get_contents($credentialsPath), true);
        
        if ($credentials) {
            echo "✅ File is valid JSON\n";
            echo "Project ID: " . ($credentials['project_id'] ?? 'NOT FOUND') . "\n";
            echo "Type: " . ($credentials['type'] ?? 'NOT FOUND') . "\n";
            echo "Client Email: " . ($credentials['client_email'] ?? 'NOT FOUND') . "\n";
            
            if ($credentials['project_id'] === 'last-5acaf') {
                echo "✅ Project ID matches!\n";
            } else {
                echo "❌ Project ID mismatch! Expected: last-5acaf, Found: " . ($credentials['project_id'] ?? 'NONE') . "\n";
            }
            
            if (isset($credentials['private_key']) && strpos($credentials['private_key'], 'BEGIN PRIVATE KEY') !== false) {
                echo "✅ Private key format looks correct\n";
            } else {
                echo "❌ Private key missing or invalid format\n";
            }
            
        } else {
            echo "❌ File is not valid JSON\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error reading file: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ CREDENTIALS FILE NOT FOUND\n";
    echo "Expected location: $credentialsPath\n";
    echo "Please download and save your Firebase service account JSON file to this location.\n";
}

echo "\nSTEP 4: Test the setup\n";
echo "---------------------\n";
echo "After saving the credentials file, run:\n";
echo "📝 php diagnose-firebase.php\n";
echo "📝 php test-fcm-notifications.php\n\n";

echo "STEP 5: Test with your mobile device\n";
echo "-----------------------------------\n";
echo "1. Make sure your mobile app is running\n";
echo "2. Ensure the app is connected to the internet\n";
echo "3. Check that FCM is enabled in your mobile app\n";
echo "4. Test by marking an evacuation center as 'Full' in the web dashboard\n\n";

echo "TROUBLESHOOTING:\n";
echo "---------------\n";
echo "If you still don't receive notifications after setup:\n";
echo "1. Check if your mobile app is using the same Firebase project (last-5acaf)\n";
echo "2. Verify FCM tokens are being registered correctly\n";
echo "3. Make sure your device has internet connection\n";
echo "4. Check if notifications are enabled for the app in device settings\n";
echo "5. Try force-closing and reopening the mobile app\n\n";

echo "=== END SETUP HELPER ===\n";
