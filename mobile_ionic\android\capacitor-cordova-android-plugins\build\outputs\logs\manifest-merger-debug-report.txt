-- Merging decision tree log ---
manifest
ADDED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:1-8:12
INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:1-8:12
	package
		INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	xmlns:amazon
		ADDED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:3:1-57
	xmlns:android
		ADDED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:1-6:15
	android:usesCleartextTraffic
		ADDED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:15-50
uses-sdk
INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\laravel\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
