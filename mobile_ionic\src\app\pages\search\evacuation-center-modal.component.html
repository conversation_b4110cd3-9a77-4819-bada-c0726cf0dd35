<div class="modal-container">
  <!-- Close button -->
  <div class="close-button" (click)="dismiss()">
    <ion-icon name="close-circle" color="danger"></ion-icon>
  </div>

  <!-- Center name -->
  <h2 class="center-name">{{ center.name }}</h2>

  <!-- Contact info -->
  <div class="info-section">
    <div class="info-label">Contact Number</div>
    <div class="info-value contact">
      <ion-icon name="call-outline"></ion-icon>
      <span>{{ center.contact || 'No contact available' }}</span>
    </div>
  </div>

  <!-- Address info -->
  <div class="info-section">
    <div class="info-label">Address</div>
    <div class="info-value address">
      <ion-icon name="location-outline"></ion-icon>
      <span>{{ center.address }}</span>
    </div>
  </div>

  <!-- Status info -->
  <div class="info-section" *ngIf="center.status">
    <div class="info-label">Status</div>
    <div class="info-value status" [ngClass]="{'full-status': center.status === 'Full'}">
      <ion-icon [name]="center.status === 'Full' ? 'warning-outline' : 'checkmark-circle-outline'"
                [color]="center.status === 'Full' ? 'warning' : 'success'"></ion-icon>
      <span>{{ center.status }}</span>
      <span *ngIf="center.status === 'Full'" class="full-notice"> - No routing available</span>
    </div>
  </div>

  <!-- Get Directions button -->
  <div class="directions-button">
    <ion-button expand="block"
                [color]="center.routing_available === false ? 'medium' : 'primary'"
                [disabled]="center.routing_available === false"
                (click)="getDirections()">
      <ion-icon [name]="center.routing_available === false ? 'warning-outline' : 'navigate'" slot="start"></ion-icon>
      {{ center.routing_available === false ? 'Center Full - No Directions' : 'Get Directions' }}
    </ion-button>
  </div>
</div>
