import{a as ct}from"./chunk-NNX4H53H.js";import{a as st,c as dt}from"./chunk-2IVG2Z4K.js";import{a as lt}from"./chunk-AYSFXGM6.js";import{b as at}from"./chunk-DUSKIWGF.js";import{a as rt}from"./chunk-LT5RLULD.js";import"./chunk-3J7GGTVR.js";import{a as it}from"./chunk-G7IUXWVS.js";import"./chunk-ICWJVXBH.js";import{$ as C,Aa as U,Ab as W,Bb as K,Ca as G,D as s,H as P,Hb as q,I as m,K as b,Lb as Q,M as i,Mb as X,N as a,O as f,R as k,S as _,T as h,_ as p,_b as Z,aa as N,cc as tt,d as S,ec as et,fa as $,ga as D,gc as nt,hc as ot,m as x,mb as J,n as A,oa as z,pa as F,qa as Y,s as y,sa as H,t as w,va as V,yb as B,zb as j}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as I,b as R,g as ut,h as d}from"./chunk-B7O3QC5Z.js";var g=ut(dt());var ht=c=>({"full-status":c});function mt(c,v){if(c&1&&(i(0,"div",36)(1,"ion-card")(2,"ion-card-content")(3,"div",37),f(4,"ion-icon",38),i(5,"span"),p(6,"Route to Nearest Center"),a()(),i(7,"div",32)(8,"div",39),f(9,"ion-icon",31),i(10,"span"),p(11),a()(),i(12,"div",39),f(13,"ion-icon",40),i(14,"span"),p(15),a()()()()()()),c&2){let t=h();s(9),m("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),s(2),N("",(t.routeTime/60).toFixed(0)," min"),s(4),N("",(t.routeDistance/1e3).toFixed(2)," km")}}function ft(c,v){if(c&1&&(i(0,"span",50),p(1),a()),c&2){let t=h().$implicit,o=h();s(),N(" \u{1F4CD} ",o.calculateDistanceInKm(t)," km away ")}}function _t(c,v){if(c&1){let t=k();i(0,"div",41),_("click",function(){let e=y(t).$implicit,n=h();return w(n.selectCenterFromList(e))}),i(1,"div",42)(2,"h4"),p(3),a(),i(4,"p",43),p(5),a(),i(6,"div",44),P(7,ft,2,1,"span",45),i(8,"span",46),p(9),a(),i(10,"span",47),p(11),a()()(),i(12,"div",48),f(13,"ion-icon",49),a()()}if(c&2){let t=v.$implicit,o=h();s(3),C(t.name),s(2),C(t.address),s(2),m("ngIf",o.userLocation),s(2),N("\u{1F465} ",t.capacity||"N/A"," capacity"),s(),m("ngClass",D(6,ht,t.routing_available===!1)),s(),N(" ",t.routing_available===!1?"\u{1F534} Full":"\u{1F7E2} Available"," ")}}function Ct(c,v){if(c&1){let t=k();i(0,"app-real-time-navigation",51),_("routeUpdated",function(e){y(t);let n=h();return w(n.onNavigationRouteUpdated(e))})("navigationStopped",function(){y(t);let e=h();return w(e.onNavigationStopped())}),a()}if(c&2){let t=h();m("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function Mt(c,v){if(c&1&&(i(0,"div",42)(1,"h3"),p(2),a(),i(3,"p"),p(4),a()()),c&2){let t=h();s(2),C(t.selectedCenter.name),s(2),C(t.selectedCenter.address)}}function Pt(c,v){if(c&1&&(i(0,"div",36)(1,"span",61),p(2),a(),i(3,"span",50),p(4),a()()),c&2){let t=h(2);s(2),C(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),C(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function vt(c,v){if(c&1&&(i(0,"div",36)(1,"span",61),p(2),a(),i(3,"span",50),p(4),a()()),c&2){let t=h(2);s(2),C(t.formatTime(t.routeInfo.cycling.duration)),s(2),C(t.formatDistance(t.routeInfo.cycling.distance))}}function Ot(c,v){if(c&1&&(i(0,"div",36)(1,"span",61),p(2),a(),i(3,"span",50),p(4),a()()),c&2){let t=h(2);s(2),C(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),C(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function xt(c,v){if(c&1){let t=k();i(0,"button",62),_("click",function(){y(t);let e=h(2);return w(e.startRealTimeNavigation(e.selectedCenter))}),f(1,"ion-icon",54),p(2," Start Navigation "),a()}}function yt(c,v){if(c&1){let t=k();i(0,"div",52)(1,"div",53),f(2,"ion-icon",54),i(3,"span"),p(4,"Choose Transportation"),a()(),i(5,"div",55)(6,"button",56),_("click",function(){y(t);let e=h();return w(e.selectTransportMode("walking"))}),f(7,"ion-icon",57),i(8,"span"),p(9,"Walk"),a(),P(10,Pt,5,2,"div",12),a(),i(11,"button",56),_("click",function(){y(t);let e=h();return w(e.selectTransportMode("cycling"))}),f(12,"ion-icon",58),i(13,"span"),p(14,"Cycle"),a(),P(15,vt,5,2,"div",12),a(),i(16,"button",56),_("click",function(){y(t);let e=h();return w(e.selectTransportMode("driving"))}),f(17,"ion-icon",59),i(18,"span"),p(19,"Drive"),a(),P(20,Ot,5,2,"div",12),a()(),P(21,xt,3,0,"button",60),a()}if(c&2){let t=h();s(6),b("active",t.selectedTransportMode==="walking"),s(4),m("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),b("active",t.selectedTransportMode==="cycling"),s(4),m("ngIf",t.routeInfo.cycling),s(),b("active",t.selectedTransportMode==="driving"),s(4),m("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),m("ngIf",t.selectedTransportMode)}}function wt(c,v){if(c&1&&(i(0,"div",63)(1,"span",61),p(2),a(),i(3,"span",50),p(4),a()()),c&2){let t=h();s(2),C(t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)),s(2),C(t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance))}}var Ut=(()=>{class c{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.shouldAutoRouteEmergency=!1,this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.selectedCenter=null,this.selectedTransportMode="walking",this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(et),this.toastCtrl=x(nt),this.alertCtrl=x(tt),this.http=x(V),this.router=x(G),this.route=x(U),this.enhancedDownload=x(ct),this.osmRouting=x(rt),this.mapboxRouting=x(st)}ngOnInit(){console.log("\u{1F7E2} TYPHOON MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F7E2} TYPHOON MAP: New center to highlight:",this.newCenterId)),t.emergency==="true"&&t.autoRoute==="true"&&(console.log("\u{1F6A8} Emergency navigation triggered for typhoon map"),t.notification==="true"&&(console.log("\u{1F4F1} Emergency triggered by notification:",{category:t.category,severity:t.severity,title:t.title,message:t.message}),this.showNotificationEmergencyAlert(t)),this.shouldAutoRouteEmergency=!0)})}ngAfterViewInit(){return d(this,null,function*(){console.log("\u{1F7E2} TYPHOON MAP: View initialized, loading map..."),setTimeout(()=>d(this,null,function*(){yield this.loadTyphoonMap()}),100)})}loadTyphoonMap(){return d(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading typhoon evacuation centers...",spinner:"crescent"});yield t.present();try{let o=yield at.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=o.coords.latitude,n=o.coords.longitude;console.log(`\u{1F7E2} TYPHOON MAP: User location [${e}, ${n}]`),this.userLocation={lat:e,lng:n},this.initializeMap(e,n),yield this.loadTyphoonCenters(e,n),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Showing ${this.evacuationCenters.length} typhoon evacuation centers`,duration:3e3,color:"success",position:"top"})).present()}catch(o){yield t.dismiss(),console.error("\u{1F7E2} TYPHOON MAP: Error loading map",o),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadTyphoonMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,o){if(console.log(`\u{1F7E2} TYPHOON MAP: Initializing map at [${t}, ${o}]`),!document.getElementById("typhoon-map"))throw console.error("\u{1F7E2} TYPHOON MAP: Container #typhoon-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=g.map("typhoon-map").setView([t,o],13),g.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=g.marker([t,o],{icon:g.icon({iconUrl:"assets/myLocation.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadTyphoonCenters(t,o){return d(this,null,function*(){try{console.log("\u{1F7E2} TYPHOON MAP: Fetching typhoon centers...");let e=[];try{e=(yield S(this.http.get(`${it.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F7E2} TYPHOON MAP: Total centers received from API:",e?.length||0)}catch(n){console.error("\u274C API failed:",n),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(n=>Array.isArray(n.disaster_type)?n.disaster_type.some(r=>r==="Typhoon"):n.disaster_type==="Typhoon"),console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`),console.log("\u{1F7E2} TYPHOON MAP: Filtered centers:",this.evacuationCenters.map(n=>`${n.name} (${JSON.stringify(n.disaster_type)})`)),console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Typhoon Centers",message:"No typhoon evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(n=>{let r=Number(n.latitude),l=Number(n.longitude);if(!isNaN(r)&&!isNaN(l)){let u=g.marker([r,l],{icon:g.icon({iconUrl:"assets/forTyphoon.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),M=this.calculateDistance(t,o,r,l);u.on("click",()=>{console.log("\u{1F300} TYPHOON: Marker clicked for center:",n.name),n.routing_available===!1?this.alertCtrl.create({header:"Center Full",message:`${n.name} is currently full. Routing is not available.`,buttons:["OK"]}).then(L=>L.present()):this.showNavigationPanel(n)});let O=this.newCenterId&&n.id.toString()===this.newCenterId,T=n.status||"Active",E=n.routing_available===!1,gt=E?"\u{1F534}":"\u{1F7E2}",pt=E?"<p><em>\u26A0\uFE0F Center is Full - No routing available</em></p>":"<p><em>Click marker for route options</em></p>";u.bindPopup(`
            <div class="evacuation-popup">
              <h3>${gt} ${n.name} ${O?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Typhoon Center</p>
              <p><strong>Status:</strong> ${T}</p>
              <p><strong>Distance:</strong> ${(M/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
              ${pt}
              ${O?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),O&&(u.openPopup(),this.map.setView([r,l],15),this.toastCtrl.create({message:`\u{1F195} New typhoon evacuation center: ${n.name}`,duration:5e3,color:"success",position:"top"}).then(L=>L.present())),u.addTo(this.map),console.log(`\u{1F7E2} Added typhoon marker: ${n.name}`)}}),console.log("\u{1F7E2} Showing simple markers without auto-routing..."),this.shouldAutoRouteEmergency&&(console.log("\u{1F6A8} Performing emergency auto-routing to nearest typhoon evacuation centers"),yield this.performEmergencyRouting(),this.shouldAutoRouteEmergency=!1),this.evacuationCenters.length>0){let n=g.latLngBounds([]);n.extend([t,o]),this.evacuationCenters.forEach(r=>{n.extend([Number(r.latitude),Number(r.longitude)])}),this.map.fitBounds(n,{padding:[50,50]})}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading typhoon centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return d(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E2} TYPHOON MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E2} TYPHOON MAP: Finding 2 nearest typhoon centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No typhoon evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Showing routes to ${t.length} nearest typhoon centers`,duration:4e3,color:"success"})).present()}catch(t){console.error("\u{1F7E2} TYPHOON MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}performEmergencyRouting(){return d(this,null,function*(){try{console.log("\u{1F6A8} Starting emergency routing to nearest typhoon evacuation centers"),yield(yield this.toastCtrl.create({message:"\u{1F6A8} EMERGENCY: Routing to nearest typhoon evacuation centers",duration:5e3,color:"danger",position:"top",cssClass:"emergency-toast"})).present(),yield this.routeToTwoNearestCenters(),yield(yield this.toastCtrl.create({message:"\u2705 Emergency routes calculated. Follow the highlighted paths to safety.",duration:7e3,color:"success",position:"bottom"})).present()}catch(t){console.error("Error in emergency routing:",t),yield(yield this.toastCtrl.create({message:"\u26A0\uFE0F Emergency routing failed. Please manually navigate to nearest evacuation center.",duration:5e3,color:"warning",position:"top"})).present()}})}getTwoNearestCenters(t,o){return this.evacuationCenters.map(n=>R(I({},n),{distance:this.calculateDistance(t,o,Number(n.latitude),Number(n.longitude))})).sort((n,r)=>n.distance-r.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(o=>this.map.removeLayer(o)),this.nearestMarkers=[],t.forEach((o,e)=>{let n=Number(o.latitude),r=Number(o.longitude);if(!isNaN(n)&&!isNaN(r)){let l=g.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <img src="assets/forTyphoon.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=g.marker([n,r],{icon:l});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${o.name}</h4>
            <p><strong>Type:</strong> Typhoon</p>
            <p><strong>Distance:</strong> ${(o.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers&&(this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[]),this.map.eachLayer(t=>{(t instanceof g.GeoJSON||t instanceof g.Polyline||t.options&&(t.options.color==="#2dd36f"||t.options.color==="#008000"||t.options.color==="#007bff"||t.isRouteLayer||t.isNavigationRoute))&&this.map.removeLayer(t)})}calculateRoutes(t){return d(this,null,function*(){if(this.userLocation){this.routeLayer=g.layerGroup().addTo(this.map);for(let o=0;o<t.length;o++){let e=t[o];if(e.routing_available===!1){console.log(`\u{1F7E2} TYPHOON MAP: Skipping route to ${e.name} - routing not available`);continue}let n=Number(e.latitude),r=Number(e.longitude);if(!isNaN(n)&&!isNaN(r))try{console.log(`\u{1F7E2} TYPHOON MAP: Creating Mapbox route to center ${o+1}: ${e.name}`);let l=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),u=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,r,n,l);if(u&&u.routes&&u.routes.length>0){let M=u.routes[0];g.polyline(M.geometry.coordinates.map(T=>[T[1],T[0]]),{color:"#2dd36f",weight:4,opacity:.8,dashArray:o===0?void 0:"10, 10"}).addTo(this.routeLayer),o===0&&(this.routeTime=M.duration,this.routeDistance=M.distance),console.log(`\u2705 TYPHOON MAP: Added Mapbox route to ${e.name} (${(M.distance/1e3).toFixed(2)}km, ${(M.duration/60).toFixed(0)}min)`)}}catch(l){console.error(`\u{1F7E2} Error calculating Mapbox route to center ${o+1}:`,l)}}}})}calculateRoute(t,o){return d(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F7E2} TYPHOON MAP: No user location available for routing");return}let e=this.osmRouting.convertTravelModeToProfile(o),n=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n.routes&&n.routes.length>0){let r=n.routes[0],l=this.osmRouting.convertToGeoJSON(r);g.geoJSON(l,{style:{color:"#008000",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F7E2} TYPHOON MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error calculating route:",e)}})}openInExternalMaps(t,o){return d(this,null,function*(){let e=Number(t.latitude),n=Number(t.longitude),r="walking";o==="driving"?r="driving":o==="cycling"&&(r="bicycling");let l=`https://www.google.com/maps/dir/?api=1&destination=${e},${n}&travelmode=${r}`;try{window.open(l,"_system")}catch(u){console.error("Error opening external maps:",u),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}calculateAllRoutes(t){return d(this,null,function*(){if(!this.userLocation)return;let o=["walking","cycling","driving"];for(let e of o)try{let n=this.mapboxRouting.convertTravelModeToProfile(e),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),n);if(r.routes&&r.routes.length>0){let l=r.routes[0];this.routeInfo[e]={duration:l.duration,distance:l.distance},console.log(`\u{1F300} TYPHOON: ${e} route calculated - ${(l.distance/1e3).toFixed(2)}km, ${Math.round(l.duration/60)}min`)}}catch(n){console.error(`\u{1F300} TYPHOON: Error calculating ${e} route:`,n)}})}startNavigation(){return d(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"success",position:"top"})).present()})}routeToCenter(t,o){return d(this,null,function*(){if(this.userLocation)try{this.clearRoutes(),console.log(`\u{1F300} TYPHOON: Creating Mapbox route to ${t.name} via ${o}`);let e=this.mapboxRouting.convertTravelModeToProfile(o),n=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n&&n.routes&&n.routes.length>0){let r=n.routes[0],l="#008000";this.routeLayer=g.layerGroup().addTo(this.map);let u=g.polyline(r.geometry.coordinates.map(O=>[O[1],O[0]]),{color:l,weight:5,opacity:.8});u.isRouteLayer=!0,u.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Route: ${(r.distance/1e3).toFixed(2)}km, ${(r.duration/60).toFixed(0)}min via ${o}`,duration:4e3,color:"success"})).present(),this.map.fitBounds(u.getBounds(),{padding:[50,50]}),console.log(`\u2705 TYPHOON: Successfully created route with ${r.geometry.coordinates.length} points`)}}catch(e){console.error("\u{1F300} Error routing to typhoon center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,o,e,n){let l=t*Math.PI/180,u=e*Math.PI/180,M=(e-t)*Math.PI/180,O=(n-o)*Math.PI/180,T=Math.sin(M/2)*Math.sin(M/2)+Math.cos(l)*Math.cos(u)*Math.sin(O/2)*Math.sin(O/2);return 6371e3*(2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T)))}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let o=t.detail.value;this.changeTravelMode(o)}changeTravelMode(t){return d(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F7E2} Travel mode changed to ${t}`,duration:2e3,color:"success"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return d(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("typhoon-map",this.map,"Typhoon",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return d(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{this.clearRoutes(),yield this.routeToTwoNearestCenters(),yield(yield this.toastCtrl.create({message:"\u{1F7E2} Routes calculated to 2 nearest typhoon evacuation centers",duration:4e3,color:"success"})).present()}catch(t){console.error("Error calculating routes:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t)}calculateDistanceInKm(t){return this.userLocation?(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude))/1e3).toFixed(1):"N/A"}showNavigationPanel(t){console.log("\u{1F300} TYPHOON: showNavigationPanel called for:",t.name),console.log("\u{1F300} TYPHOON: Setting selectedCenter to:",t),this.selectedCenter=t,this.showRouteFooter=!0,console.log("\u{1F300} TYPHOON: selectedCenter is now:",this.selectedCenter),console.log("\u{1F300} TYPHOON: showRouteFooter is now:",this.showRouteFooter),this.calculateRouteInfo(t)}closeNavigationPanel(){this.selectedCenter=null,this.showRouteFooter=!1,this.routeInfo={}}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}calculateRouteInfo(t){return d(this,null,function*(){if(!this.userLocation)return;let o=["walking","cycling","driving"];for(let e of o)try{let n=this.mapboxRouting.convertTravelModeToProfile(e),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),n);if(r.routes&&r.routes.length>0){let l=r.routes[0];this.routeInfo[e]={duration:l.duration,distance:l.distance},console.log(`\u{1F300} TYPHOON: ${e} route info calculated - ${(l.distance/1e3).toFixed(2)}km, ${Math.round(l.duration/60)}min`)}}catch(n){console.error(`\u{1F300} TYPHOON: Error calculating ${e} route info:`,n);let r=this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude));this.routeInfo[e]={duration:r/(e==="walking"?5e3:e==="cycling"?15e3:5e4)*3600,distance:r}}})}showRouteOnMap(t,o){return d(this,null,function*(){if(this.userLocation)try{let e=this.mapboxRouting.convertTravelModeToProfile(o),n=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n.routes&&n.routes.length>0){let r=n.routes[0],l=this.mapboxRouting.convertToGeoJSON(r);this.clearRoutes(),this.routeLayer||(this.routeLayer=g.layerGroup().addTo(this.map));let u=g.geoJSON(l,{style:{color:"#2dd36f",weight:4,opacity:.8}});u.isRouteLayer=!0,u.addTo(this.routeLayer)}}catch(e){console.error("Error showing route on map:",e)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let o=t/1e3;return o<1?`${Math.round(t)} m`:`${o.toFixed(1)} km`}ionViewWillEnter(){console.log("\u{1F300} TYPHOON MAP: View will enter - refreshing data..."),this.map&&this.userLocation&&this.loadTyphoonCenters(this.userLocation.lat,this.userLocation.lng)}ionViewWillLeave(){this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){return d(this,null,function*(){if(console.log("\u{1F9ED} Starting real-time navigation to typhoon center:",t.name),!this.selectedTransportMode){console.error("\u274C No transport mode selected");return}yield this.routeToCenter(t,this.selectedTransportMode),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.closeNavigationPanel(),this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name} via ${this.selectedTransportMode}`,duration:3e3,color:"primary"}).then(o=>o.present()),console.log("\u2705 Typhoon map real-time navigation setup complete")})}onNavigationRouteUpdated(t){console.log("\u{1F504} Typhoon map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Typhoon map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let o=this.osmRouting.convertToGeoJSON(t),e=g.geoJSON(o,{style:{color:"#2dd36f",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}showNotificationEmergencyAlert(t){return d(this,null,function*(){yield(yield this.alertCtrl.create({header:"\u{1F32A}\uFE0F TYPHOON EMERGENCY",subHeader:t.title||"Emergency Notification",message:`
        <div style="text-align: left;">
          <p><strong>Alert:</strong> ${t.message||"Typhoon emergency detected"}</p>
          <p><strong>Severity:</strong> ${(t.severity||"medium").toUpperCase()}</p>
          <p><strong>Action:</strong> Routing to nearest typhoon evacuation centers</p>
        </div>
      `,buttons:[{text:"Navigate Now",role:"confirm",cssClass:"alert-button-confirm",handler:()=>{console.log("\u{1F6A8} User confirmed emergency navigation for typhoon")}},{text:"View Map Only",role:"cancel",cssClass:"alert-button-cancel",handler:()=>{console.log("\u{1F4CD} User chose to view typhoon map without auto-routing"),this.shouldAutoRouteEmergency=!1}}],cssClass:"emergency-alert"})).present()})}static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275cmp=A({type:c,selectors:[["app-typhoon-map"]],standalone:!0,features:[$],decls:47,vars:19,consts:[[3,"translucent"],["color","success"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","typhoon-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForTyphoon.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForTyphoon.png","alt","Download",1,"control-icon"],["src","assets/compassForTyphoon.png","alt","Route to Nearest",1,"control-icon"],["class","route-info",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["name","close"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["fill","clear","size","small",3,"click"],["name","close-outline"],["class","transport-options",4,"ngIf"],[1,"route-footer"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],["class","route-info-footer",4,"ngIf"],["fill","solid","color","success",3,"click"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","success"],[1,"route-item"],["name","location-outline"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"status",3,"ngClass"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"time"],[1,"start-navigation-btn",3,"click"],[1,"route-info-footer"]],template:function(o,e){o&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),_("click",function(){return e.goBack()}),f(4,"ion-icon",4),a()()()(),i(5,"ion-content",5),f(6,"div",6),i(7,"div",7)(8,"ion-button",8),_("click",function(){return e.showAllCenters()}),f(9,"img",9),a(),i(10,"ion-button",8),_("click",function(){return e.downloadMap()}),f(11,"img",10),a(),i(12,"ion-button",8),_("click",function(){return e.routeToNearestCenters()}),f(13,"img",11),a()(),P(14,mt,16,3,"div",12),i(15,"div",13)(16,"div",14)(17,"div",15)(18,"div",16)(19,"h3"),p(20,"\u{1F7E2} Typhoon Evacuation Centers"),a(),i(21,"p"),p(22),a()(),i(23,"ion-button",3),_("click",function(){return e.closeAllCentersPanel()}),f(24,"ion-icon",17),a()(),i(25,"div",18),P(26,_t,14,8,"div",19),a()()(),i(27,"div",20),_("click",function(){return e.closeAllCentersPanel()}),a(),P(28,Ct,1,3,"app-real-time-navigation",21),i(29,"div",22)(30,"div",14)(31,"div",15),P(32,Mt,5,2,"div",23),i(33,"ion-button",24),_("click",function(){return e.closeNavigationPanel()}),f(34,"ion-icon",25),a()(),P(35,yt,22,10,"div",26),a()(),i(36,"div",27)(37,"div",28)(38,"div",29)(39,"div",30),f(40,"ion-icon",31),a(),i(41,"div",32)(42,"div",33),p(43),a(),P(44,wt,5,2,"div",34),a()(),i(45,"ion-button",35),_("click",function(){return e.startRealTimeNavigation(e.selectedCenter)}),p(46," Start "),a()()()()),o&2&&(m("translucent",!0),s(5),m("fullscreen",!0),s(9),m("ngIf",e.routeTime>0&&e.routeDistance>0),s(),b("show",e.showAllCentersPanel),s(7),N("",e.evacuationCenters.length," centers available"),s(4),m("ngForOf",e.evacuationCenters),s(),b("show",e.showAllCentersPanel),s(),m("ngIf",e.navigationDestination),s(),b("show",e.selectedCenter),s(3),m("ngIf",e.selectedCenter),s(3),m("ngIf",e.selectedCenter),s(),b("show",e.showRouteFooter&&e.selectedCenter),s(4),m("name",e.selectedTransportMode==="walking"?"walk-outline":e.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),C(e.selectedCenter==null?null:e.selectedCenter.name),s(),m("ngIf",e.routeInfo[e.selectedTransportMode]))},dependencies:[ot,B,j,W,K,q,Q,X,Z,H,z,F,Y,J,lt],styles:["#typhoon-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #2dd36f;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(45,211,111,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(45, 211, 111, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:400px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:2000;transition:right .3s ease-in-out;overflow:hidden}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-success-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-success);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-success-tint);background:var(--ion-color-success-tint);transform:translateY(-2px);box-shadow:0 4px 12px #2dd36f26}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;gap:12px;flex-wrap:wrap}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-success);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{font-size:12px;font-weight:600;padding:2px 6px;border-radius:8px;background:#00ff001a;color:var(--ion-color-success)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .status.full-status[_ngcontent-%COMP%]{background:#ff00001a;color:var(--ion-color-danger)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:18px}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #2dd36f;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-success);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between;gap:16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-success-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-success)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:var(--ion-color-success)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;--padding-start: 20px;--padding-end: 20px;font-weight:600}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-success-tint);background:var(--ion-color-success-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-success);background:var(--ion-color-success-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-success);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-success-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-success);--indicator-color: var(--ion-color-success);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;background:#fffffff2}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-success)}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#2dd36f;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-success);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2dd36f;font-size:18px}"]})}}return c})();export{Ut as TyphoonMapPage};
