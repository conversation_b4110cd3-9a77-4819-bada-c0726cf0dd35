import{a as Y}from"./chunk-WB4DBDSO.js";import{b as X}from"./chunk-TTLEF7YW.js";import"./chunk-G7IUXWVS.js";import{$ as m,Ab as T,Bb as F,Ca as I,Cb as z,D as i,Db as L,Eb as A,Gb as $,H as h,Hb as N,I as s,Lb as B,M as t,Mb as R,N as n,O as c,Ob as U,Pb as V,Qb as j,R as b,S as _,T as g,Zb as H,_ as a,_b as q,aa as d,cc as G,ec as J,fa as S,gc as Q,hc as W,m as u,mb as M,n as x,pa as v,qa as w,s as y,sa as P,t as O,xb as E,yb as D,zb as k}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{h as p}from"./chunk-B7O3QC5Z.js";function Z(r,f){if(r&1&&(t(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),c(3,"ion-icon",21),a(4," Cache Summary "),n()(),t(5,"ion-card-content")(6,"ion-list")(7,"ion-item"),c(8,"ion-icon",22),t(9,"ion-label")(10,"h3"),a(11,"Evacuation Centers"),n(),t(12,"p"),a(13),n()()(),t(14,"ion-item"),c(15,"ion-icon",23),t(16,"ion-label")(17,"h3"),a(18,"User Location"),n(),t(19,"p"),a(20),n()()(),t(21,"ion-item"),c(22,"ion-icon",24),t(23,"ion-label")(24,"h3"),a(25,"Emergency Contacts"),n(),t(26,"p"),a(27),n()()(),t(28,"ion-item"),c(29,"ion-icon",25),t(30,"ion-label")(31,"h3"),a(32,"Disaster Information"),n(),t(33,"p"),a(34),n()()(),t(35,"ion-item"),c(36,"ion-icon",26),t(37,"ion-label")(38,"h3"),a(39,"Total Cache Size"),n(),t(40,"p"),a(41),n()()()()()()),r&2){let e=g();i(13),d("",e.offlineDataSummary.evacuationCenters," centers cached"),i(7),m(e.offlineDataSummary.userLocation?"Cached":"Not cached"),i(7),d("",e.offlineDataSummary.emergencyContacts," contacts cached"),i(7),d("",e.offlineDataSummary.disasterInfo," guides cached"),i(7),m(e.formatCacheSize(e.offlineDataSummary.cacheSize))}}function K(r,f){r&1&&(t(0,"ion-badge",34),a(1,"24/7"),n())}function ee(r,f){if(r&1){let e=b();t(0,"ion-item",29),_("click",function(){let l=y(e).$implicit,C=g(2);return O(C.callEmergencyNumber(l))}),c(1,"ion-icon",30),t(2,"ion-label")(3,"h3"),a(4),n(),t(5,"p"),a(6),n(),t(7,"p",31),a(8),n()(),t(9,"ion-chip",32)(10,"ion-label"),a(11),n()(),h(12,K,2,0,"ion-badge",33),n()}if(r&2){let e=f.$implicit,o=g(2);i(),s("name",o.getContactTypeIcon(e.type))("color",o.getContactTypeColor(e.type)),i(3),m(e.name),i(2),m(e.number),i(2),m(e.description),i(),s("color",o.getContactTypeColor(e.type)),i(2),m(e.type),i(),s("ngIf",e.available24h)}}function te(r,f){if(r&1&&(t(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),c(3,"ion-icon",27),a(4," Emergency Contacts "),n(),t(5,"ion-card-subtitle"),a(6),n()(),t(7,"ion-card-content")(8,"ion-list"),h(9,ee,13,8,"ion-item",28),n()()()),r&2){let e=g();i(6),d("",e.emergencyContacts.length," contacts available offline"),i(3),s("ngForOf",e.emergencyContacts)}}function ne(r,f){if(r&1&&(t(0,"ion-item"),c(1,"ion-icon",36),t(2,"ion-label")(3,"h3"),a(4),n(),t(5,"p"),a(6),n(),t(7,"p"),a(8),n()(),t(9,"ion-chip",37)(10,"ion-label"),a(11),n()()()),r&2){let e=f.$implicit,o=g(2);i(),s("name",o.getDisasterIcon(e.disasterType)),i(3),m(e.title),i(2),d("",e.instructions.length," emergency instructions"),i(2),d("",e.preparationSteps.length," preparation steps"),i(3),m(e.disasterType)}}function ie(r,f){if(r&1&&(t(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),c(3,"ion-icon",35),a(4," Disaster Preparedness "),n(),t(5,"ion-card-subtitle"),a(6),n()(),t(7,"ion-card-content")(8,"ion-list"),h(9,ne,12,5,"ion-item",15),n()()()),r&2){let e=g();i(6),d("",e.disasterInfo.length," guides available offline"),i(3),s("ngForOf",e.disasterInfo)}}function ae(r,f){if(r&1&&(t(0,"ion-item"),c(1,"ion-icon",38),t(2,"ion-label")(3,"h3"),a(4),n(),t(5,"p"),a(6),n(),t(7,"p"),a(8),n(),t(9,"p"),a(10),n()()()),r&2){let e=f.$implicit,o=g();i(4),m(e.key),i(2),d("Size: ",o.formatCacheSize(e.size/1024),""),i(2),d("Updated: ",o.formatDate(e.lastUpdated),""),i(2),d("Expires: ",o.formatDate(e.expiresAt),"")}}var he=(()=>{class r{constructor(){this.offlineDataSummary=null,this.emergencyContacts=[],this.disasterInfo=[],this.cacheMetadata=[],this.isOnline=navigator.onLine,this.offlineStorage=u(X),this.emergencyContactsService=u(Y),this.alertCtrl=u(G),this.toastCtrl=u(Q),this.loadingCtrl=u(J),this.router=u(I)}ngOnInit(){return p(this,null,function*(){yield this.loadOfflineData(),this.setupNetworkListener()})}setupNetworkListener(){window.addEventListener("online",()=>{this.isOnline=!0}),window.addEventListener("offline",()=>{this.isOnline=!1})}loadOfflineData(){return p(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Loading offline data...",spinner:"crescent"});yield e.present();try{this.offlineDataSummary=yield this.offlineStorage.getOfflineDataSummary(),this.emergencyContacts=yield this.emergencyContactsService.getEmergencyContacts(),this.disasterInfo=yield this.emergencyContactsService.getDisasterInfo(),this.cacheMetadata=yield this.offlineStorage.getCacheMetadata(),console.log("Offline data loaded:",{summary:this.offlineDataSummary,contacts:this.emergencyContacts.length,disasterInfo:this.disasterInfo.length,metadata:this.cacheMetadata.length})}catch(o){console.error("Failed to load offline data:",o),yield(yield this.toastCtrl.create({message:"Failed to load offline data",duration:3e3,color:"danger"})).present()}finally{yield e.dismiss()}})}clearAllCache(){return p(this,null,function*(){yield(yield this.alertCtrl.create({header:"Clear All Cache",message:"This will remove all offline data including evacuation centers, emergency contacts, and user location. Are you sure?",buttons:[{text:"Cancel",role:"cancel"},{text:"Clear All",role:"destructive",handler:()=>p(this,null,function*(){yield this.performClearCache()})}]})).present()})}performClearCache(){return p(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Clearing cache...",spinner:"crescent"});yield e.present();try{yield this.offlineStorage.clearAll(),yield this.loadOfflineData(),yield(yield this.toastCtrl.create({message:"All offline data cleared successfully",duration:3e3,color:"success"})).present()}catch(o){console.error("Failed to clear cache:",o),yield(yield this.toastCtrl.create({message:"Failed to clear cache",duration:3e3,color:"danger"})).present()}finally{yield e.dismiss()}})}refreshOfflineData(){return p(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Refreshing offline data...",spinner:"crescent"});yield e.present();try{yield this.emergencyContactsService.initializeEmergencyContacts(),yield this.loadOfflineData(),yield(yield this.toastCtrl.create({message:"Offline data refreshed successfully",duration:3e3,color:"success"})).present()}catch(o){console.error("Failed to refresh offline data:",o),yield(yield this.toastCtrl.create({message:"Failed to refresh offline data",duration:3e3,color:"danger"})).present()}finally{yield e.dismiss()}})}formatCacheSize(e){return e<1024?`${e} KB`:`${(e/1024).toFixed(1)} MB`}formatDate(e){return new Date(e).toLocaleString()}getContactTypeIcon(e){return{police:"shield-checkmark",fire:"flame",medical:"medical",rescue:"boat",government:"business",utility:"flash",general:"call"}[e]||"call"}getContactTypeColor(e){return{police:"primary",fire:"danger",medical:"success",rescue:"warning",government:"secondary",utility:"tertiary",general:"medium"}[e]||"medium"}getDisasterIcon(e){return{Earthquake:"pulse",Flood:"water",Typhoon:"cloudy",Fire:"flame",Landslide:"triangle",General:"warning"}[e]||"warning"}callEmergencyNumber(e){return p(this,null,function*(){yield(yield this.alertCtrl.create({header:`Call ${e.name}`,message:`Do you want to call ${e.number}?`,buttons:[{text:"Cancel",role:"cancel"},{text:"Call",handler:()=>{window.open(`tel:${e.number}`,"_system")}}]})).present()})}goBack(){this.router.navigate(["/tabs/home"])}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275cmp=x({type:r,selectors:[["app-offline-data"]],standalone:!0,features:[S],decls:43,vars:13,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","arrow-back"],["slot","end"],[3,"click","disabled"],["name","refresh"],[3,"fullscreen"],["collapse","condense"],["size","large"],[3,"name","color"],[3,"color"],[3,"name"],[4,"ngIf"],["name","settings","color","medium"],[4,"ngFor","ngForOf"],[1,"cache-actions"],["expand","block","fill","outline","color","primary",3,"click","disabled"],["name","refresh","slot","start"],["expand","block","fill","outline","color","danger",3,"click"],["name","trash","slot","start"],["name","archive","color","primary"],["name","business","slot","start","color","primary"],["name","location","slot","start","color","success"],["name","call","slot","start","color","warning"],["name","information-circle","slot","start","color","secondary"],["name","folder","slot","start","color","medium"],["name","call","color","danger"],["button","",3,"click",4,"ngFor","ngForOf"],["button","",3,"click"],["slot","start",3,"name","color"],[1,"contact-description"],["slot","end","outline","",3,"color"],["slot","end","color","success",4,"ngIf"],["slot","end","color","success"],["name","shield-checkmark","color","success"],["slot","start","color","warning",3,"name"],["slot","end","color","warning","outline",""],["name","document","slot","start","color","medium"]],template:function(o,l){o&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),_("click",function(){return l.goBack()}),c(4,"ion-icon",3),n()(),t(5,"ion-title"),a(6,"Offline Data"),n(),t(7,"ion-buttons",4)(8,"ion-button",5),_("click",function(){return l.refreshOfflineData()}),c(9,"ion-icon",6),n()()()(),t(10,"ion-content",7)(11,"ion-header",8)(12,"ion-toolbar")(13,"ion-title",9),a(14,"Offline Data"),n()()(),t(15,"ion-card")(16,"ion-card-header")(17,"ion-card-title"),c(18,"ion-icon",10),a(19," Network Status "),n()(),t(20,"ion-card-content")(21,"ion-chip",11),c(22,"ion-icon",12),t(23,"ion-label"),a(24),n()()()(),h(25,Z,42,5,"ion-card",13)(26,te,10,2,"ion-card",13)(27,ie,10,2,"ion-card",13),t(28,"ion-card")(29,"ion-card-header")(30,"ion-card-title"),c(31,"ion-icon",14),a(32," Cache Management "),n()(),t(33,"ion-card-content")(34,"ion-list"),h(35,ae,11,4,"ion-item",15),n(),t(36,"div",16)(37,"ion-button",17),_("click",function(){return l.refreshOfflineData()}),c(38,"ion-icon",18),a(39," Refresh Data "),n(),t(40,"ion-button",19),_("click",function(){return l.clearAllCache()}),c(41,"ion-icon",20),a(42," Clear All Cache "),n()()()()()),o&2&&(s("translucent",!0),i(8),s("disabled",!l.isOnline),i(2),s("fullscreen",!0),i(8),s("name",l.isOnline?"wifi":"wifi-outline")("color",l.isOnline?"success":"danger"),i(3),s("color",l.isOnline?"success":"danger"),i(),s("name",l.isOnline?"checkmark-circle":"close-circle"),i(2),m(l.isOnline?"Online":"Offline"),i(),s("ngIf",l.offlineDataSummary),i(),s("ngIf",l.emergencyContacts.length>0),i(),s("ngIf",l.disasterInfo.length>0),i(8),s("ngForOf",l.cacheMetadata),i(2),s("disabled",!l.isOnline))},dependencies:[W,E,D,k,T,F,z,L,A,$,N,B,R,U,V,j,H,q,P,v,w,M],styles:["ion-content[_ngcontent-%COMP%]{--background: #f5f5f5}ion-card[_ngcontent-%COMP%]{margin:16px;border-radius:12px;box-shadow:0 2px 8px #0000001a}ion-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:18px;font-weight:600}ion-card-subtitle[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:14px;margin-top:4px}ion-list[_ngcontent-%COMP%]{background:transparent}ion-item[_ngcontent-%COMP%]{--background: transparent;--border-color: rgba(0, 0, 0, .1);--padding-start: 0;--inner-padding-end: 0;margin-bottom:8px;border-radius:8px}ion-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.contact-description[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin-top:2px}.cache-actions[_ngcontent-%COMP%]{margin-top:16px;display:flex;flex-direction:column;gap:12px}ion-chip[_ngcontent-%COMP%]{font-size:12px;height:24px}ion-badge[_ngcontent-%COMP%]{font-size:10px;margin-left:8px}ion-card[_ngcontent-%COMP%]:first-child   ion-chip[_ngcontent-%COMP%]{margin:0}.emergency-contacts[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{cursor:pointer;transition:background-color .2s ease}.emergency-contacts[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{--background: rgba(var(--ion-color-primary-rgb), .05)}.cache-metadata[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-weight:500;text-transform:capitalize}.cache-metadata[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px;color:var(--ion-color-medium);margin:2px 0}@media (max-width: 768px){ion-card[_ngcontent-%COMP%]{margin:12px 8px}ion-card-title[_ngcontent-%COMP%]{font-size:16px}.cache-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-size:14px}}@media (prefers-color-scheme: dark){ion-content[_ngcontent-%COMP%]{--background: #1a1a1a}ion-card[_ngcontent-%COMP%]{--background: #2a2a2a;box-shadow:0 2px 8px #0000004d}ion-item[_ngcontent-%COMP%]{--border-color: rgba(255, 255, 255, .1)}}.loading-placeholder[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;color:var(--ion-color-medium)}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:var(--ion-color-medium)}.empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-weight:500}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}.status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.status-indicator.online[_ngcontent-%COMP%]{color:var(--ion-color-success)}.status-indicator.offline[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.priority-high[_ngcontent-%COMP%]{border-left:4px solid var(--ion-color-danger)}.priority-medium[_ngcontent-%COMP%]{border-left:4px solid var(--ion-color-warning)}.priority-low[_ngcontent-%COMP%]{border-left:4px solid var(--ion-color-medium)}"]})}}return r})();export{he as OfflineDataPage};
