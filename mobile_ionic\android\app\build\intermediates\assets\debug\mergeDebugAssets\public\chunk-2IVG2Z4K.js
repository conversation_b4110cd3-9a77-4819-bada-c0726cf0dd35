import{a as ea}from"./chunk-G7IUXWVS.js";import{i as $o,l as Aa,va as ta}from"./chunk-Q5Y64KIB.js";import{a as qo,f as jo,h as Xs}from"./chunk-B7O3QC5Z.js";var Xh=jo((zs,Ws)=>{"use strict";(function(v,mA){typeof zs=="object"&&typeof Ws<"u"?Ws.exports=mA():typeof define=="function"&&define.amd?define(mA):(v=typeof globalThis<"u"?globalThis:v||self,v.html2canvas=mA())})(zs,function(){"use strict";var v=function(r,e){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,i){s.__proto__=i}||function(s,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(s[a]=i[a])},v(r,e)};function mA(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");v(r,e);function s(){this.constructor=r}r.prototype=e===null?Object.create(e):(s.prototype=e.prototype,new s)}var E=function(){return E=Object.assign||function(e){for(var s,i=1,a=arguments.length;i<a;i++){s=arguments[i];for(var h in s)Object.prototype.hasOwnProperty.call(s,h)&&(e[h]=s[h])}return e},E.apply(this,arguments)};function j(r,e,s,i){function a(h){return h instanceof s?h:new s(function(c){c(h)})}return new(s||(s=Promise))(function(h,c){function f(w){try{d(i.next(w))}catch(p){c(p)}}function B(w){try{d(i.throw(w))}catch(p){c(p)}}function d(w){w.done?h(w.value):a(w.value).then(f,B)}d((i=i.apply(r,e||[])).next())})}function M(r,e){var s={label:0,sent:function(){if(h[0]&1)throw h[1];return h[1]},trys:[],ops:[]},i,a,h,c;return c={next:f(0),throw:f(1),return:f(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function f(d){return function(w){return B([d,w])}}function B(d){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,a&&(h=d[0]&2?a.return:d[0]?a.throw||((h=a.return)&&h.call(a),0):a.next)&&!(h=h.call(a,d[1])).done)return h;switch(a=0,h&&(d=[d[0]&2,h.value]),d[0]){case 0:case 1:h=d;break;case 4:return s.label++,{value:d[1],done:!1};case 5:s.label++,a=d[1],d=[0];continue;case 7:d=s.ops.pop(),s.trys.pop();continue;default:if(h=s.trys,!(h=h.length>0&&h[h.length-1])&&(d[0]===6||d[0]===2)){s=0;continue}if(d[0]===3&&(!h||d[1]>h[0]&&d[1]<h[3])){s.label=d[1];break}if(d[0]===6&&s.label<h[1]){s.label=h[1],h=d;break}if(h&&s.label<h[2]){s.label=h[2],s.ops.push(d);break}h[2]&&s.ops.pop(),s.trys.pop();continue}d=e.call(r,s)}catch(w){d=[6,w],a=0}finally{i=h=0}if(d[0]&5)throw d[1];return{value:d[0]?d[1]:void 0,done:!0}}}function XA(r,e,s){if(s||arguments.length===2)for(var i=0,a=e.length,h;i<a;i++)(h||!(i in e))&&(h||(h=Array.prototype.slice.call(e,0,i)),h[i]=e[i]);return r.concat(h||e)}for(var R=function(){function r(e,s,i,a){this.left=e,this.top=s,this.width=i,this.height=a}return r.prototype.add=function(e,s,i,a){return new r(this.left+e,this.top+s,this.width+i,this.height+a)},r.fromClientRect=function(e,s){return new r(s.left+e.windowBounds.left,s.top+e.windowBounds.top,s.width,s.height)},r.fromDOMRectList=function(e,s){var i=Array.from(s).find(function(a){return a.width!==0});return i?new r(i.left+e.windowBounds.left,i.top+e.windowBounds.top,i.width,i.height):r.EMPTY},r.EMPTY=new r(0,0,0,0),r}(),dt=function(r,e){return R.fromClientRect(r,e.getBoundingClientRect())},Pt=function(r){var e=r.body,s=r.documentElement;if(!e||!s)throw new Error("Unable to get document size");var i=Math.max(Math.max(e.scrollWidth,s.scrollWidth),Math.max(e.offsetWidth,s.offsetWidth),Math.max(e.clientWidth,s.clientWidth)),a=Math.max(Math.max(e.scrollHeight,s.scrollHeight),Math.max(e.offsetHeight,s.offsetHeight),Math.max(e.clientHeight,s.clientHeight));return new R(0,0,i,a)},sA=function(r){for(var e=[],s=0,i=r.length;s<i;){var a=r.charCodeAt(s++);if(a>=55296&&a<=56319&&s<i){var h=r.charCodeAt(s++);(h&64512)===56320?e.push(((a&1023)<<10)+(h&1023)+65536):(e.push(a),s--)}else e.push(a)}return e},$=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,r);var s=r.length;if(!s)return"";for(var i=[],a=-1,h="";++a<s;){var c=r[a];c<=65535?i.push(c):(c-=65536,i.push((c>>10)+55296,c%1024+56320)),(a+1===s||i.length>16384)&&(h+=String.fromCharCode.apply(String,i),i.length=0)}return h},Ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",UA=typeof Uint8Array>"u"?[]:new Uint8Array(256),rA=0;rA<Ot.length;rA++)UA[Ot.charCodeAt(rA)]=rA;for(var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ft=typeof Uint8Array>"u"?[]:new Uint8Array(256),VA=0;VA<Fe.length;VA++)Ft[Fe.charCodeAt(VA)]=VA;for(var rt=function(r){var e=r.length*.75,s=r.length,i,a=0,h,c,f,B;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var d=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),w=Array.isArray(d)?d:new Uint8Array(d);for(i=0;i<s;i+=4)h=Ft[r.charCodeAt(i)],c=Ft[r.charCodeAt(i+1)],f=Ft[r.charCodeAt(i+2)],B=Ft[r.charCodeAt(i+3)],w[a++]=h<<2|c>>4,w[a++]=(c&15)<<4|f>>2,w[a++]=(f&3)<<6|B&63;return d},Rr=function(r){for(var e=r.length,s=[],i=0;i<e;i+=2)s.push(r[i+1]<<8|r[i]);return s},$e=function(r){for(var e=r.length,s=[],i=0;i<e;i+=4)s.push(r[i+3]<<24|r[i+2]<<16|r[i+1]<<8|r[i]);return s},wt=5,Ar=11,tr=2,Nr=Ar-wt,Gr=65536>>wt,PA=1<<wt,OA=PA-1,On=1024>>wt,pt=Gr+On,Rn=pt,ZA=32,ye=Rn+ZA,V=65536>>Ar,Ei=1<<Nr,N=Ei-1,cA=function(r,e,s){return r.slice?r.slice(e,s):new Uint16Array(Array.prototype.slice.call(r,e,s))},RA=function(r,e,s){return r.slice?r.slice(e,s):new Uint32Array(Array.prototype.slice.call(r,e,s))},NA=function(r,e){var s=rt(r),i=Array.isArray(s)?$e(s):new Uint32Array(s),a=Array.isArray(s)?Rr(s):new Uint16Array(s),h=24,c=cA(a,h/2,i[4]/2),f=i[5]===2?cA(a,(h+i[4])/2):RA(i,Math.ceil((h+i[4])/4));return new wA(i[0],i[1],i[2],i[3],c,f)},wA=function(){function r(e,s,i,a,h,c){this.initialValue=e,this.errorValue=s,this.highStart=i,this.highValueIndex=a,this.index=h,this.data=c}return r.prototype.get=function(e){var s;if(e>=0){if(e<55296||e>56319&&e<=65535)return s=this.index[e>>wt],s=(s<<tr)+(e&OA),this.data[s];if(e<=65535)return s=this.index[Gr+(e-55296>>wt)],s=(s<<tr)+(e&OA),this.data[s];if(e<this.highStart)return s=ye-V+(e>>Ar),s=this.index[s],s+=e>>wt&N,s=this.index[s],s=(s<<tr)+(e&OA),this.data[s];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},r}(),iA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Y=typeof Uint8Array>"u"?[]:new Uint8Array(256),zA=0;zA<iA.length;zA++)Y[iA.charCodeAt(zA)]=zA;var yt="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",kr=50,Vr=1,er=2,re=3,Zr=4,Nn=5,Xr=7,zr=8,rr=9,it=10,Wr=11,Jr=12,Ee=13,Hi=14,ie=15,Yr=16,ir=17,Rt=18,Ii=19,qr=20,jr=21,ne=22,nr=23,se=24,GA=25,Nt=26,oe=27,ae=28,He=29,Gt=30,Gn=31,Ie=32,Le=33,sr=34,$r=35,Ai=36,xe=37,ti=38,or=39,ar=40,be=41,Li=42,kn=43,Vn=[9001,65288],xi="!",P="\xD7",K="\xF7",ur=NA(yt),at=[Gt,Ai],hr=[Vr,er,re,Nn],ei=[it,zr],cr=[oe,Nt],bi=hr.concat(ei),kt=[ti,or,ar,sr,$r],Ti=[ie,Ee],Zn=function(r,e){e===void 0&&(e="strict");var s=[],i=[],a=[];return r.forEach(function(h,c){var f=ur.get(h);if(f>kr?(a.push(!0),f-=kr):a.push(!1),["normal","auto","loose"].indexOf(e)!==-1&&[8208,8211,12316,12448].indexOf(h)!==-1)return i.push(c),s.push(Yr);if(f===Zr||f===Wr){if(c===0)return i.push(c),s.push(Gt);var B=s[c-1];return bi.indexOf(B)===-1?(i.push(i[c-1]),s.push(B)):(i.push(c),s.push(Gt))}if(i.push(c),f===Gn)return s.push(e==="strict"?jr:xe);if(f===Li||f===He)return s.push(Gt);if(f===kn)return h>=131072&&h<=196605||h>=196608&&h<=262141?s.push(xe):s.push(Gt);s.push(f)}),[i,s,a]},ri=function(r,e,s,i){var a=i[s];if(Array.isArray(r)?r.indexOf(a)!==-1:r===a)for(var h=s;h<=i.length;){h++;var c=i[h];if(c===e)return!0;if(c!==it)break}if(a===it)for(var h=s;h>0;){h--;var f=i[h];if(Array.isArray(r)?r.indexOf(f)!==-1:r===f)for(var B=s;B<=i.length;){B++;var c=i[B];if(c===e)return!0;if(c!==it)break}if(f!==it)break}return!1},Ki=function(r,e){for(var s=r;s>=0;){var i=e[s];if(i===it)s--;else return i}return 0},Xn=function(r,e,s,i,a){if(s[i]===0)return P;var h=i-1;if(Array.isArray(a)&&a[h]===!0)return P;var c=h-1,f=h+1,B=e[h],d=c>=0?e[c]:0,w=e[f];if(B===er&&w===re)return P;if(hr.indexOf(B)!==-1)return xi;if(hr.indexOf(w)!==-1||ei.indexOf(w)!==-1)return P;if(Ki(h,e)===zr)return K;if(ur.get(r[h])===Wr||(B===Ie||B===Le)&&ur.get(r[f])===Wr||B===Xr||w===Xr||B===rr||[it,Ee,ie].indexOf(B)===-1&&w===rr||[ir,Rt,Ii,se,ae].indexOf(w)!==-1||Ki(h,e)===ne||ri(nr,ne,h,e)||ri([ir,Rt],jr,h,e)||ri(Jr,Jr,h,e))return P;if(B===it)return K;if(B===nr||w===nr)return P;if(w===Yr||B===Yr)return K;if([Ee,ie,jr].indexOf(w)!==-1||B===Hi||d===Ai&&Ti.indexOf(B)!==-1||B===ae&&w===Ai||w===qr||at.indexOf(w)!==-1&&B===GA||at.indexOf(B)!==-1&&w===GA||B===oe&&[xe,Ie,Le].indexOf(w)!==-1||[xe,Ie,Le].indexOf(B)!==-1&&w===Nt||at.indexOf(B)!==-1&&cr.indexOf(w)!==-1||cr.indexOf(B)!==-1&&at.indexOf(w)!==-1||[oe,Nt].indexOf(B)!==-1&&(w===GA||[ne,ie].indexOf(w)!==-1&&e[f+1]===GA)||[ne,ie].indexOf(B)!==-1&&w===GA||B===GA&&[GA,ae,se].indexOf(w)!==-1)return P;if([GA,ae,se,ir,Rt].indexOf(w)!==-1)for(var p=h;p>=0;){var C=e[p];if(C===GA)return P;if([ae,se].indexOf(C)!==-1)p--;else break}if([oe,Nt].indexOf(w)!==-1)for(var p=[ir,Rt].indexOf(B)!==-1?c:h;p>=0;){var C=e[p];if(C===GA)return P;if([ae,se].indexOf(C)!==-1)p--;else break}if(ti===B&&[ti,or,sr,$r].indexOf(w)!==-1||[or,sr].indexOf(B)!==-1&&[or,ar].indexOf(w)!==-1||[ar,$r].indexOf(B)!==-1&&w===ar||kt.indexOf(B)!==-1&&[qr,Nt].indexOf(w)!==-1||kt.indexOf(w)!==-1&&B===oe||at.indexOf(B)!==-1&&at.indexOf(w)!==-1||B===se&&at.indexOf(w)!==-1||at.concat(GA).indexOf(B)!==-1&&w===ne&&Vn.indexOf(r[f])===-1||at.concat(GA).indexOf(w)!==-1&&B===Rt)return P;if(B===be&&w===be){for(var x=s[h],U=1;x>0&&(x--,e[x]===be);)U++;if(U%2!==0)return P}return B===Ie&&w===Le?P:K},Si=function(r,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var s=Zn(r,e.lineBreak),i=s[0],a=s[1],h=s[2];(e.wordBreak==="break-all"||e.wordBreak==="break-word")&&(a=a.map(function(f){return[GA,Gt,Li].indexOf(f)!==-1?xe:f}));var c=e.wordBreak==="keep-all"?h.map(function(f,B){return f&&r[B]>=19968&&r[B]<=40959}):void 0;return[i,a,c]},zn=function(){function r(e,s,i,a){this.codePoints=e,this.required=s===xi,this.start=i,this.end=a}return r.prototype.slice=function(){return $.apply(void 0,this.codePoints.slice(this.start,this.end))},r}(),lr=function(r,e){var s=sA(r),i=Si(s,e),a=i[0],h=i[1],c=i[2],f=s.length,B=0,d=0;return{next:function(){if(d>=f)return{done:!0,value:null};for(var w=P;d<f&&(w=Xn(s,h,a,++d,c))===P;);if(w!==P||d===f){var p=new zn(s,w,B,d);return B=d,{value:p,done:!1}}return{done:!0,value:null}}}},Wn=1,Jn=2,Te=4,Mi=8,Br=10,fr=47,Qt=92,Di=9,Pi=32,Et=34,J=61,lA=35,gr=36,ue=37,mt=39,he=40,G=41,fA=95,LA=45,dr=33,WA=60,Yn=62,wr=64,Vt=91,pA=93,Zt=61,Ke=123,Ht=63,ii=125,ce=124,ni=126,si=128,Se=65533,Me=42,Ct=43,pr=44,Oi=58,oi=59,De=46,Z=0,ut=8,aA=11,Ri=14,ai=31,ui=127,JA=-1,It=48,Qr=97,le=101,FA=102,Xt=117,Ni=122,hi=65,Gi=69,ci=70,li=85,qn=90,yA=function(r){return r>=It&&r<=57},q=function(r){return r>=55296&&r<=57343},Be=function(r){return yA(r)||r>=hi&&r<=ci||r>=Qr&&r<=FA},nt=function(r){return r>=Qr&&r<=Ni},Pe=function(r){return r>=hi&&r<=qn},ki=function(r){return nt(r)||Pe(r)},jn=function(r){return r>=si},fe=function(r){return r===Br||r===Di||r===Pi},mr=function(r){return ki(r)||jn(r)||r===fA},Bi=function(r){return mr(r)||yA(r)||r===LA},$n=function(r){return r>=Z&&r<=ut||r===aA||r>=Ri&&r<=ai||r===ui},Lt=function(r,e){return r!==Qt?!1:e!==Br},ge=function(r,e,s){return r===LA?mr(e)||Lt(e,s):mr(r)?!0:!!(r===Qt&&Lt(r,e))},fi=function(r,e,s){return r===Ct||r===LA?yA(e)?!0:e===De&&yA(s):yA(r===De?e:r)},ht=function(r){var e=0,s=1;(r[e]===Ct||r[e]===LA)&&(r[e]===LA&&(s=-1),e++);for(var i=[];yA(r[e]);)i.push(r[e++]);var a=i.length?parseInt($.apply(void 0,i),10):0;r[e]===De&&e++;for(var h=[];yA(r[e]);)h.push(r[e++]);var c=h.length,f=c?parseInt($.apply(void 0,h),10):0;(r[e]===Gi||r[e]===le)&&e++;var B=1;(r[e]===Ct||r[e]===LA)&&(r[e]===LA&&(B=-1),e++);for(var d=[];yA(r[e]);)d.push(r[e++]);var w=d.length?parseInt($.apply(void 0,d),10):0;return s*(a+f*Math.pow(10,-c))*Math.pow(10,B*w)},As={type:2},Vi={type:3},xt={type:4},Zi={type:13},Xi={type:8},gi={type:21},ts={type:9},zi={type:10},Wi={type:11},es={type:12},rs={type:14},de={type:23},is={type:1},Ji={type:25},Yi={type:24},Cr={type:26},zt={type:27},ns={type:28},Oe={type:29},YA={type:31},_r={type:32},di=function(){function r(){this._value=[]}return r.prototype.write=function(e){this._value=this._value.concat(sA(e))},r.prototype.read=function(){for(var e=[],s=this.consumeToken();s!==_r;)e.push(s),s=this.consumeToken();return e},r.prototype.consumeToken=function(){var e=this.consumeCodePoint();switch(e){case Et:return this.consumeStringToken(Et);case lA:var s=this.peekCodePoint(0),i=this.peekCodePoint(1),a=this.peekCodePoint(2);if(Bi(s)||Lt(i,a)){var h=ge(s,i,a)?Jn:Wn,c=this.consumeName();return{type:5,value:c,flags:h}}break;case gr:if(this.peekCodePoint(0)===J)return this.consumeCodePoint(),Zi;break;case mt:return this.consumeStringToken(mt);case he:return As;case G:return Vi;case Me:if(this.peekCodePoint(0)===J)return this.consumeCodePoint(),rs;break;case Ct:if(fi(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case pr:return xt;case LA:var f=e,B=this.peekCodePoint(0),d=this.peekCodePoint(1);if(fi(f,B,d))return this.reconsumeCodePoint(e),this.consumeNumericToken();if(ge(f,B,d))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();if(B===LA&&d===Yn)return this.consumeCodePoint(),this.consumeCodePoint(),Yi;break;case De:if(fi(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case fr:if(this.peekCodePoint(0)===Me)for(this.consumeCodePoint();;){var w=this.consumeCodePoint();if(w===Me&&(w=this.consumeCodePoint(),w===fr))return this.consumeToken();if(w===JA)return this.consumeToken()}break;case Oi:return Cr;case oi:return zt;case WA:if(this.peekCodePoint(0)===dr&&this.peekCodePoint(1)===LA&&this.peekCodePoint(2)===LA)return this.consumeCodePoint(),this.consumeCodePoint(),Ji;break;case wr:var p=this.peekCodePoint(0),C=this.peekCodePoint(1),x=this.peekCodePoint(2);if(ge(p,C,x)){var c=this.consumeName();return{type:7,value:c}}break;case Vt:return ns;case Qt:if(Lt(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();break;case pA:return Oe;case Zt:if(this.peekCodePoint(0)===J)return this.consumeCodePoint(),Xi;break;case Ke:return Wi;case ii:return es;case Xt:case li:var U=this.peekCodePoint(0),y=this.peekCodePoint(1);return U===Ct&&(Be(y)||y===Ht)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(e),this.consumeIdentLikeToken();case ce:if(this.peekCodePoint(0)===J)return this.consumeCodePoint(),ts;if(this.peekCodePoint(0)===ce)return this.consumeCodePoint(),gi;break;case ni:if(this.peekCodePoint(0)===J)return this.consumeCodePoint(),zi;break;case JA:return _r}return fe(e)?(this.consumeWhiteSpace(),YA):yA(e)?(this.reconsumeCodePoint(e),this.consumeNumericToken()):mr(e)?(this.reconsumeCodePoint(e),this.consumeIdentLikeToken()):{type:6,value:$(e)}},r.prototype.consumeCodePoint=function(){var e=this._value.shift();return typeof e>"u"?-1:e},r.prototype.reconsumeCodePoint=function(e){this._value.unshift(e)},r.prototype.peekCodePoint=function(e){return e>=this._value.length?-1:this._value[e]},r.prototype.consumeUnicodeRangeToken=function(){for(var e=[],s=this.consumeCodePoint();Be(s)&&e.length<6;)e.push(s),s=this.consumeCodePoint();for(var i=!1;s===Ht&&e.length<6;)e.push(s),s=this.consumeCodePoint(),i=!0;if(i){var a=parseInt($.apply(void 0,e.map(function(B){return B===Ht?It:B})),16),h=parseInt($.apply(void 0,e.map(function(B){return B===Ht?ci:B})),16);return{type:30,start:a,end:h}}var c=parseInt($.apply(void 0,e),16);if(this.peekCodePoint(0)===LA&&Be(this.peekCodePoint(1))){this.consumeCodePoint(),s=this.consumeCodePoint();for(var f=[];Be(s)&&f.length<6;)f.push(s),s=this.consumeCodePoint();var h=parseInt($.apply(void 0,f),16);return{type:30,start:c,end:h}}else return{type:30,start:c,end:c}},r.prototype.consumeIdentLikeToken=function(){var e=this.consumeName();return e.toLowerCase()==="url"&&this.peekCodePoint(0)===he?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===he?(this.consumeCodePoint(),{type:19,value:e}):{type:20,value:e}},r.prototype.consumeUrlToken=function(){var e=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===JA)return{type:22,value:""};var s=this.peekCodePoint(0);if(s===mt||s===Et){var i=this.consumeStringToken(this.consumeCodePoint());return i.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===JA||this.peekCodePoint(0)===G)?(this.consumeCodePoint(),{type:22,value:i.value}):(this.consumeBadUrlRemnants(),de)}for(;;){var a=this.consumeCodePoint();if(a===JA||a===G)return{type:22,value:$.apply(void 0,e)};if(fe(a))return this.consumeWhiteSpace(),this.peekCodePoint(0)===JA||this.peekCodePoint(0)===G?(this.consumeCodePoint(),{type:22,value:$.apply(void 0,e)}):(this.consumeBadUrlRemnants(),de);if(a===Et||a===mt||a===he||$n(a))return this.consumeBadUrlRemnants(),de;if(a===Qt)if(Lt(a,this.peekCodePoint(0)))e.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),de;else e.push(a)}},r.prototype.consumeWhiteSpace=function(){for(;fe(this.peekCodePoint(0));)this.consumeCodePoint()},r.prototype.consumeBadUrlRemnants=function(){for(;;){var e=this.consumeCodePoint();if(e===G||e===JA)return;Lt(e,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},r.prototype.consumeStringSlice=function(e){for(var s=5e4,i="";e>0;){var a=Math.min(s,e);i+=$.apply(void 0,this._value.splice(0,a)),e-=a}return this._value.shift(),i},r.prototype.consumeStringToken=function(e){var s="",i=0;do{var a=this._value[i];if(a===JA||a===void 0||a===e)return s+=this.consumeStringSlice(i),{type:0,value:s};if(a===Br)return this._value.splice(0,i),is;if(a===Qt){var h=this._value[i+1];h!==JA&&h!==void 0&&(h===Br?(s+=this.consumeStringSlice(i),i=-1,this._value.shift()):Lt(a,h)&&(s+=this.consumeStringSlice(i),s+=$(this.consumeEscapedCodePoint()),i=-1))}i++}while(!0)},r.prototype.consumeNumber=function(){var e=[],s=Te,i=this.peekCodePoint(0);for((i===Ct||i===LA)&&e.push(this.consumeCodePoint());yA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());i=this.peekCodePoint(0);var a=this.peekCodePoint(1);if(i===De&&yA(a))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),s=Mi;yA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());i=this.peekCodePoint(0),a=this.peekCodePoint(1);var h=this.peekCodePoint(2);if((i===Gi||i===le)&&((a===Ct||a===LA)&&yA(h)||yA(a)))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),s=Mi;yA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());return[ht(e),s]},r.prototype.consumeNumericToken=function(){var e=this.consumeNumber(),s=e[0],i=e[1],a=this.peekCodePoint(0),h=this.peekCodePoint(1),c=this.peekCodePoint(2);if(ge(a,h,c)){var f=this.consumeName();return{type:15,number:s,flags:i,unit:f}}return a===ue?(this.consumeCodePoint(),{type:16,number:s,flags:i}):{type:17,number:s,flags:i}},r.prototype.consumeEscapedCodePoint=function(){var e=this.consumeCodePoint();if(Be(e)){for(var s=$(e);Be(this.peekCodePoint(0))&&s.length<6;)s+=$(this.consumeCodePoint());fe(this.peekCodePoint(0))&&this.consumeCodePoint();var i=parseInt(s,16);return i===0||q(i)||i>1114111?Se:i}return e===JA?Se:e},r.prototype.consumeName=function(){for(var e="";;){var s=this.consumeCodePoint();if(Bi(s))e+=$(s);else if(Lt(s,this.peekCodePoint(0)))e+=$(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(s),e}},r}(),qi=function(){function r(e){this._tokens=e}return r.create=function(e){var s=new di;return s.write(e),new r(s.read())},r.parseValue=function(e){return r.create(e).parseComponentValue()},r.parseValues=function(e){return r.create(e).parseComponentValues()},r.prototype.parseComponentValue=function(){for(var e=this.consumeToken();e.type===31;)e=this.consumeToken();if(e.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(e);var s=this.consumeComponentValue();do e=this.consumeToken();while(e.type===31);if(e.type===32)return s;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},r.prototype.parseComponentValues=function(){for(var e=[];;){var s=this.consumeComponentValue();if(s.type===32)return e;e.push(s),e.push()}},r.prototype.consumeComponentValue=function(){var e=this.consumeToken();switch(e.type){case 11:case 28:case 2:return this.consumeSimpleBlock(e.type);case 19:return this.consumeFunction(e)}return e},r.prototype.consumeSimpleBlock=function(e){for(var s={type:e,values:[]},i=this.consumeToken();;){if(i.type===32||os(i,e))return s;this.reconsumeToken(i),s.values.push(this.consumeComponentValue()),i=this.consumeToken()}},r.prototype.consumeFunction=function(e){for(var s={name:e.value,values:[],type:18};;){var i=this.consumeToken();if(i.type===32||i.type===3)return s;this.reconsumeToken(i),s.values.push(this.consumeComponentValue())}},r.prototype.consumeToken=function(){var e=this._tokens.shift();return typeof e>"u"?_r:e},r.prototype.reconsumeToken=function(e){this._tokens.unshift(e)},r}(),Wt=function(r){return r.type===15},bt=function(r){return r.type===17},nA=function(r){return r.type===20},ss=function(r){return r.type===0},vr=function(r,e){return nA(r)&&r.value===e},ji=function(r){return r.type!==31},xA=function(r){return r.type!==31&&r.type!==4},KA=function(r){var e=[],s=[];return r.forEach(function(i){if(i.type===4){if(s.length===0)throw new Error("Error parsing function args, zero tokens for arg");e.push(s),s=[];return}i.type!==31&&s.push(i)}),s.length&&e.push(s),e},os=function(r,e){return e===11&&r.type===12||e===28&&r.type===29?!0:e===2&&r.type===3},bA=function(r){return r.type===17||r.type===15},QA=function(r){return r.type===16||bA(r)},Jt=function(r){return r.length>1?[r[0],r[1]]:[r[0]]},EA={type:17,number:0,flags:Te},Yt={type:16,number:50,flags:Te},_t={type:16,number:100,flags:Te},Tt=function(r,e,s){var i=r[0],a=r[1];return[uA(i,e),uA(typeof a<"u"?a:i,s)]},uA=function(r,e){if(r.type===16)return r.number/100*e;if(Wt(r))switch(r.unit){case"rem":case"em":return 16*r.number;case"px":default:return r.number}return r.number},vt="deg",Re="grad",$i="rad",Ur="turn",Fr={name:"angle",parse:function(r,e){if(e.type===15)switch(e.unit){case vt:return Math.PI*e.number/180;case Re:return Math.PI/200*e.number;case $i:return e.number;case Ur:return Math.PI*2*e.number}throw new Error("Unsupported angle type")}},ct=function(r){return r.type===15&&(r.unit===vt||r.unit===Re||r.unit===$i||r.unit===Ur)},An=function(r){var e=r.filter(nA).map(function(s){return s.value}).join(" ");switch(e){case"to bottom right":case"to right bottom":case"left top":case"top left":return[EA,EA];case"to top":case"bottom":return HA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[EA,_t];case"to right":case"left":return HA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[_t,_t];case"to bottom":case"top":return HA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[_t,EA];case"to left":case"right":return HA(270)}return 0},HA=function(r){return Math.PI*r/180},Kt={name:"color",parse:function(r,e){if(e.type===18){var s=we[e.name];if(typeof s>"u")throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return s(r,e.values)}if(e.type===5){if(e.value.length===3){var i=e.value.substring(0,1),a=e.value.substring(1,2),h=e.value.substring(2,3);return Ut(parseInt(i+i,16),parseInt(a+a,16),parseInt(h+h,16),1)}if(e.value.length===4){var i=e.value.substring(0,1),a=e.value.substring(1,2),h=e.value.substring(2,3),c=e.value.substring(3,4);return Ut(parseInt(i+i,16),parseInt(a+a,16),parseInt(h+h,16),parseInt(c+c,16)/255)}if(e.value.length===6){var i=e.value.substring(0,2),a=e.value.substring(2,4),h=e.value.substring(4,6);return Ut(parseInt(i,16),parseInt(a,16),parseInt(h,16),1)}if(e.value.length===8){var i=e.value.substring(0,2),a=e.value.substring(2,4),h=e.value.substring(4,6),c=e.value.substring(6,8);return Ut(parseInt(i,16),parseInt(a,16),parseInt(h,16),parseInt(c,16)/255)}}if(e.type===20){var f=st[e.value.toUpperCase()];if(typeof f<"u")return f}return st.TRANSPARENT}},TA=function(r){return(255&r)===0},gA=function(r){var e=255&r,s=255&r>>8,i=255&r>>16,a=255&r>>24;return e<255?"rgba("+a+","+i+","+s+","+e/255+")":"rgb("+a+","+i+","+s+")"},Ut=function(r,e,s,i){return(r<<24|e<<16|s<<8|Math.round(i*255)<<0)>>>0},yr=function(r,e){if(r.type===17)return r.number;if(r.type===16){var s=e===3?1:255;return e===3?r.number/100*s:Math.round(r.number/100*s)}return 0},Ne=function(r,e){var s=e.filter(xA);if(s.length===3){var i=s.map(yr),a=i[0],h=i[1],c=i[2];return Ut(a,h,c,1)}if(s.length===4){var f=s.map(yr),a=f[0],h=f[1],c=f[2],B=f[3];return Ut(a,h,c,B)}return 0};function Ge(r,e,s){return s<0&&(s+=1),s>=1&&(s-=1),s<1/6?(e-r)*s*6+r:s<1/2?e:s<2/3?(e-r)*6*(2/3-s)+r:r}var ke=function(r,e){var s=e.filter(xA),i=s[0],a=s[1],h=s[2],c=s[3],f=(i.type===17?HA(i.number):Fr.parse(r,i))/(Math.PI*2),B=QA(a)?a.number/100:0,d=QA(h)?h.number/100:0,w=typeof c<"u"&&QA(c)?uA(c,1):1;if(B===0)return Ut(d*255,d*255,d*255,1);var p=d<=.5?d*(B+1):d+B-d*B,C=d*2-p,x=Ge(C,p,f+1/3),U=Ge(C,p,f),y=Ge(C,p,f-1/3);return Ut(x*255,U*255,y*255,w)},we={hsl:ke,hsla:ke,rgb:Ne,rgba:Ne},St=function(r,e){return Kt.parse(r,qi.create(e).parseComponentValue())},st={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},tn={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(r,e){return e.map(function(s){if(nA(s))switch(s.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},as={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},qt=function(r,e){var s=Kt.parse(r,e[0]),i=e[1];return i&&QA(i)?{color:s,stop:i}:{color:s,stop:null}},en=function(r,e){var s=r[0],i=r[r.length-1];s.stop===null&&(s.stop=EA),i.stop===null&&(i.stop=_t);for(var a=[],h=0,c=0;c<r.length;c++){var f=r[c].stop;if(f!==null){var B=uA(f,e);B>h?a.push(B):a.push(h),h=B}else a.push(null)}for(var d=null,c=0;c<a.length;c++){var w=a[c];if(w===null)d===null&&(d=c);else if(d!==null){for(var p=c-d,C=a[d-1],x=(w-C)/(p+1),U=1;U<=p;U++)a[d+U-1]=x*U;d=null}}return r.map(function(y,X){var S=y.color;return{color:S,stop:Math.max(Math.min(1,a[X]/e),0)}})},rn=function(r,e,s){var i=e/2,a=s/2,h=uA(r[0],e)-i,c=a-uA(r[1],s);return(Math.atan2(c,h)+Math.PI*2)%(Math.PI*2)},us=function(r,e,s){var i=typeof r=="number"?r:rn(r,e,s),a=Math.abs(e*Math.sin(i))+Math.abs(s*Math.cos(i)),h=e/2,c=s/2,f=a/2,B=Math.sin(i-Math.PI/2)*f,d=Math.cos(i-Math.PI/2)*f;return[a,h-d,h+d,c-B,c+B]},qA=function(r,e){return Math.sqrt(r*r+e*e)},nn=function(r,e,s,i,a){var h=[[0,0],[0,e],[r,0],[r,e]];return h.reduce(function(c,f){var B=f[0],d=f[1],w=qA(s-B,i-d);return(a?w<c.optimumDistance:w>c.optimumDistance)?{optimumCorner:f,optimumDistance:w}:c},{optimumDistance:a?1/0:-1/0,optimumCorner:null}).optimumCorner},lt=function(r,e,s,i,a){var h=0,c=0;switch(r.size){case 0:r.shape===0?h=c=Math.min(Math.abs(e),Math.abs(e-i),Math.abs(s),Math.abs(s-a)):r.shape===1&&(h=Math.min(Math.abs(e),Math.abs(e-i)),c=Math.min(Math.abs(s),Math.abs(s-a)));break;case 2:if(r.shape===0)h=c=Math.min(qA(e,s),qA(e,s-a),qA(e-i,s),qA(e-i,s-a));else if(r.shape===1){var f=Math.min(Math.abs(s),Math.abs(s-a))/Math.min(Math.abs(e),Math.abs(e-i)),B=nn(i,a,e,s,!0),d=B[0],w=B[1];h=qA(d-e,(w-s)/f),c=f*h}break;case 1:r.shape===0?h=c=Math.max(Math.abs(e),Math.abs(e-i),Math.abs(s),Math.abs(s-a)):r.shape===1&&(h=Math.max(Math.abs(e),Math.abs(e-i)),c=Math.max(Math.abs(s),Math.abs(s-a)));break;case 3:if(r.shape===0)h=c=Math.max(qA(e,s),qA(e,s-a),qA(e-i,s),qA(e-i,s-a));else if(r.shape===1){var f=Math.max(Math.abs(s),Math.abs(s-a))/Math.max(Math.abs(e),Math.abs(e-i)),p=nn(i,a,e,s,!1),d=p[0],w=p[1];h=qA(d-e,(w-s)/f),c=f*h}break}return Array.isArray(r.size)&&(h=uA(r.size[0],i),c=r.size.length===2?uA(r.size[1],a):h),[h,c]},Er=function(r,e){var s=HA(180),i=[];return KA(e).forEach(function(a,h){if(h===0){var c=a[0];if(c.type===20&&c.value==="to"){s=An(a);return}else if(ct(c)){s=Fr.parse(r,c);return}}var f=qt(r,a);i.push(f)}),{angle:s,stops:i,type:1}},Hr=function(r,e){var s=HA(180),i=[];return KA(e).forEach(function(a,h){if(h===0){var c=a[0];if(c.type===20&&["top","left","right","bottom"].indexOf(c.value)!==-1){s=An(a);return}else if(ct(c)){s=(Fr.parse(r,c)+HA(270))%HA(360);return}}var f=qt(r,a);i.push(f)}),{angle:s,stops:i,type:1}},Ir=function(r,e){var s=HA(180),i=[],a=1,h=0,c=3,f=[];return KA(e).forEach(function(B,d){var w=B[0];if(d===0){if(nA(w)&&w.value==="linear"){a=1;return}else if(nA(w)&&w.value==="radial"){a=2;return}}if(w.type===18){if(w.name==="from"){var p=Kt.parse(r,w.values[0]);i.push({stop:EA,color:p})}else if(w.name==="to"){var p=Kt.parse(r,w.values[0]);i.push({stop:_t,color:p})}else if(w.name==="color-stop"){var C=w.values.filter(xA);if(C.length===2){var p=Kt.parse(r,C[1]),x=C[0];bt(x)&&i.push({stop:{type:16,number:x.number*100,flags:x.flags},color:p})}}}}),a===1?{angle:(s+HA(180))%HA(360),stops:i,type:a}:{size:c,shape:h,stops:i,position:f,type:a}},sn="closest-side",wi="farthest-side",on="closest-corner",pe="farthest-corner",an="circle",jt="ellipse",pi="cover",Qi="contain",hs=function(r,e){var s=0,i=3,a=[],h=[];return KA(e).forEach(function(c,f){var B=!0;if(f===0){var d=!1;B=c.reduce(function(p,C){if(d)if(nA(C))switch(C.value){case"center":return h.push(Yt),p;case"top":case"left":return h.push(EA),p;case"right":case"bottom":return h.push(_t),p}else(QA(C)||bA(C))&&h.push(C);else if(nA(C))switch(C.value){case an:return s=0,!1;case jt:return s=1,!1;case"at":return d=!0,!1;case sn:return i=0,!1;case pi:case wi:return i=1,!1;case Qi:case on:return i=2,!1;case pe:return i=3,!1}else if(bA(C)||QA(C))return Array.isArray(i)||(i=[]),i.push(C),!1;return p},B)}if(B){var w=qt(r,c);a.push(w)}}),{size:i,shape:s,stops:a,position:h,type:2}},jA=function(r,e){var s=0,i=3,a=[],h=[];return KA(e).forEach(function(c,f){var B=!0;if(f===0?B=c.reduce(function(w,p){if(nA(p))switch(p.value){case"center":return h.push(Yt),!1;case"top":case"left":return h.push(EA),!1;case"right":case"bottom":return h.push(_t),!1}else if(QA(p)||bA(p))return h.push(p),!1;return w},B):f===1&&(B=c.reduce(function(w,p){if(nA(p))switch(p.value){case an:return s=0,!1;case jt:return s=1,!1;case Qi:case sn:return i=0,!1;case wi:return i=1,!1;case on:return i=2,!1;case pi:case pe:return i=3,!1}else if(bA(p)||QA(p))return Array.isArray(i)||(i=[]),i.push(p),!1;return w},B)),B){var d=qt(r,c);a.push(d)}}),{size:i,shape:s,stops:a,position:h,type:2}},un=function(r){return r.type===1},hn=function(r){return r.type===2},$t={name:"image",parse:function(r,e){if(e.type===22){var s={url:e.value,type:0};return r.cache.addImage(e.value),s}if(e.type===18){var i=Ve[e.name];if(typeof i>"u")throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return i(r,e.values)}throw new Error("Unsupported image type "+e.type)}};function cs(r){return!(r.type===20&&r.value==="none")&&(r.type!==18||!!Ve[r.name])}for(var Ve={"linear-gradient":Er,"-moz-linear-gradient":Hr,"-ms-linear-gradient":Hr,"-o-linear-gradient":Hr,"-webkit-linear-gradient":Hr,"radial-gradient":hs,"-moz-radial-gradient":jA,"-ms-radial-gradient":jA,"-o-radial-gradient":jA,"-webkit-radial-gradient":jA,"-webkit-gradient":Ir},Ze={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(r,e){if(e.length===0)return[];var s=e[0];return s.type===20&&s.value==="none"?[]:e.filter(function(i){return xA(i)&&cs(i)}).map(function(i){return $t.parse(r,i)})}},cn={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(r,e){return e.map(function(s){if(nA(s))switch(s.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},ln={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(r,e){return KA(e).map(function(s){return s.filter(QA)}).map(Jt)}},ls={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(r,e){return KA(e).map(function(s){return s.filter(nA).map(function(i){return i.value}).join(" ")}).map(Bn)}},Bn=function(r){switch(r){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Qe=function(r){return r.AUTO="auto",r.CONTAIN="contain",r.COVER="cover",r}(Qe||{}),fn={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(r,e){return KA(e).map(function(s){return s.filter(gn)})}},gn=function(r){return nA(r)||QA(r)},Xe=function(r){return{name:"border-"+r+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Bs=Xe("top"),dn=Xe("right"),wn=Xe("bottom"),fs=Xe("left"),A=function(r){return{name:"border-radius-"+r,initialValue:"0 0",prefix:!1,type:1,parse:function(e,s){return Jt(s.filter(QA))}}},t=A("top-left"),n=A("top-right"),o=A("bottom-right"),u=A("bottom-left"),l=function(r){return{name:"border-"+r+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(e,s){switch(s){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},g=l("top"),Q=l("right"),m=l("bottom"),_=l("left"),F=function(r){return{name:"border-"+r+"-width",initialValue:"0",type:0,prefix:!1,parse:function(e,s){return Wt(s)?s.number:0}}},D=F("top"),z=F("right"),SA=F("bottom"),_A=F("left"),$A={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},kA={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(r,e){switch(e){case"rtl":return 1;case"ltr":default:return 0}}},me={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(r,e){return e.filter(nA).reduce(function(s,i){return s|gs(i.value)},0)}},gs=function(r){switch(r){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},ds={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},ws={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(r,e){return e.type===20&&e.value==="normal"?0:e.type===17||e.type===15?e.number:0}},Lr=function(r){return r.NORMAL="normal",r.STRICT="strict",r}(Lr||{}),ps={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"strict":return Lr.STRICT;case"normal":default:return Lr.NORMAL}}},pn={name:"line-height",initialValue:"normal",prefix:!1,type:4},dA=function(r,e){return nA(r)&&r.value==="normal"?1.2*e:r.type===17?e*r.number:QA(r)?uA(r,e):e},xr={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(r,e){return e.type===20&&e.value==="none"?null:$t.parse(r,e)}},Qs={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(r,e){switch(e){case"inside":return 0;case"outside":default:return 1}}},mi={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},br=function(r){return{name:"margin-"+r,initialValue:"0",prefix:!1,type:4}},Ci=br("top"),Qn=br("right"),ms=br("bottom"),ia=br("left"),na={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(r,e){return e.filter(nA).map(function(s){switch(s.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},sa={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"break-word":return"break-word";case"normal":default:return"normal"}}},mn=function(r){return{name:"padding-"+r,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},oa=mn("top"),aa=mn("right"),ua=mn("bottom"),ha=mn("left"),ca={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(r,e){switch(e){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},la={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(r,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Ba={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.length===1&&vr(e[0],"none")?[]:KA(e).map(function(s){for(var i={color:st.TRANSPARENT,offsetX:EA,offsetY:EA,blur:EA},a=0,h=0;h<s.length;h++){var c=s[h];bA(c)?(a===0?i.offsetX=c:a===1?i.offsetY=c:i.blur=c,a++):i.color=Kt.parse(r,c)}return i})}},fa={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},ga={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(r,e){if(e.type===20&&e.value==="none")return null;if(e.type===18){var s=pa[e.name];if(typeof s>"u")throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return s(e.values)}return null}},da=function(r){var e=r.filter(function(s){return s.type===17}).map(function(s){return s.number});return e.length===6?e:null},wa=function(r){var e=r.filter(function(B){return B.type===17}).map(function(B){return B.number}),s=e[0],i=e[1];e[2],e[3];var a=e[4],h=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var c=e[12],f=e[13];return e[14],e[15],e.length===16?[s,i,a,h,c,f]:null},pa={matrix:da,matrix3d:wa},Js={type:16,number:50,flags:Te},Qa=[Js,Js],ma={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(r,e){var s=e.filter(QA);return s.length!==2?Qa:[s[0],s[1]]}},Ca={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Cn=function(r){return r.NORMAL="normal",r.BREAK_ALL="break-all",r.KEEP_ALL="keep-all",r}(Cn||{}),_a={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"break-all":return Cn.BREAK_ALL;case"keep-all":return Cn.KEEP_ALL;case"normal":default:return Cn.NORMAL}}},va={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(r,e){if(e.type===20)return{auto:!0,order:0};if(bt(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},Ys={name:"time",parse:function(r,e){if(e.type===15)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},Ua={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(r,e){return bt(e)?e.number:1}},Fa={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ya={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(r,e){return e.filter(nA).map(function(s){switch(s.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(s){return s!==0})}},Ea={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(r,e){var s=[],i=[];return e.forEach(function(a){switch(a.type){case 20:case 0:s.push(a.value);break;case 17:s.push(a.number.toString());break;case 4:i.push(s.join(" ")),s.length=0;break}}),s.length&&i.push(s.join(" ")),i.map(function(a){return a.indexOf(" ")===-1?a:"'"+a+"'"})}},Ha={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Ia={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(r,e){if(bt(e))return e.number;if(nA(e))switch(e.value){case"bold":return 700;case"normal":default:return 400}return 400}},La={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.filter(nA).map(function(s){return s.value})}},xa={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},IA=function(r,e){return(r&e)!==0},ba={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(r,e){if(e.length===0)return[];var s=e[0];return s.type===20&&s.value==="none"?[]:e}},Ta={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return null;var s=e[0];if(s.type===20&&s.value==="none")return null;for(var i=[],a=e.filter(ji),h=0;h<a.length;h++){var c=a[h],f=a[h+1];if(c.type===20){var B=f&&bt(f)?f.number:1;i.push({counter:c.value,increment:B})}}return i}},Ka={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return[];for(var s=[],i=e.filter(ji),a=0;a<i.length;a++){var h=i[a],c=i[a+1];if(nA(h)&&h.value!=="none"){var f=c&&bt(c)?c.number:0;s.push({counter:h.value,reset:f})}}return s}},Sa={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(r,e){return e.filter(Wt).map(function(s){return Ys.parse(r,s)})}},Ma={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return null;var s=e[0];if(s.type===20&&s.value==="none")return null;var i=[],a=e.filter(ss);if(a.length%2!==0)return null;for(var h=0;h<a.length;h+=2){var c=a[h].value,f=a[h+1].value;i.push({open:c,close:f})}return i}},qs=function(r,e,s){if(!r)return"";var i=r[Math.min(e,r.length-1)];return i?s?i.open:i.close:""},Da={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.length===1&&vr(e[0],"none")?[]:KA(e).map(function(s){for(var i={color:255,offsetX:EA,offsetY:EA,blur:EA,spread:EA,inset:!1},a=0,h=0;h<s.length;h++){var c=s[h];vr(c,"inset")?i.inset=!0:bA(c)?(a===0?i.offsetX=c:a===1?i.offsetY=c:a===2?i.blur=c:i.spread=c,a++):i.color=Kt.parse(r,c)}return i})}},Pa={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(r,e){var s=[0,1,2],i=[];return e.filter(nA).forEach(function(a){switch(a.value){case"stroke":i.push(1);break;case"fill":i.push(0);break;case"markers":i.push(2);break}}),s.forEach(function(a){i.indexOf(a)===-1&&i.push(a)}),i}},Oa={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Ra={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(r,e){return Wt(e)?e.number:0}},Na=function(){function r(e,s){var i,a;this.animationDuration=b(e,Sa,s.animationDuration),this.backgroundClip=b(e,tn,s.backgroundClip),this.backgroundColor=b(e,as,s.backgroundColor),this.backgroundImage=b(e,Ze,s.backgroundImage),this.backgroundOrigin=b(e,cn,s.backgroundOrigin),this.backgroundPosition=b(e,ln,s.backgroundPosition),this.backgroundRepeat=b(e,ls,s.backgroundRepeat),this.backgroundSize=b(e,fn,s.backgroundSize),this.borderTopColor=b(e,Bs,s.borderTopColor),this.borderRightColor=b(e,dn,s.borderRightColor),this.borderBottomColor=b(e,wn,s.borderBottomColor),this.borderLeftColor=b(e,fs,s.borderLeftColor),this.borderTopLeftRadius=b(e,t,s.borderTopLeftRadius),this.borderTopRightRadius=b(e,n,s.borderTopRightRadius),this.borderBottomRightRadius=b(e,o,s.borderBottomRightRadius),this.borderBottomLeftRadius=b(e,u,s.borderBottomLeftRadius),this.borderTopStyle=b(e,g,s.borderTopStyle),this.borderRightStyle=b(e,Q,s.borderRightStyle),this.borderBottomStyle=b(e,m,s.borderBottomStyle),this.borderLeftStyle=b(e,_,s.borderLeftStyle),this.borderTopWidth=b(e,D,s.borderTopWidth),this.borderRightWidth=b(e,z,s.borderRightWidth),this.borderBottomWidth=b(e,SA,s.borderBottomWidth),this.borderLeftWidth=b(e,_A,s.borderLeftWidth),this.boxShadow=b(e,Da,s.boxShadow),this.color=b(e,$A,s.color),this.direction=b(e,kA,s.direction),this.display=b(e,me,s.display),this.float=b(e,ds,s.cssFloat),this.fontFamily=b(e,Ea,s.fontFamily),this.fontSize=b(e,Ha,s.fontSize),this.fontStyle=b(e,xa,s.fontStyle),this.fontVariant=b(e,La,s.fontVariant),this.fontWeight=b(e,Ia,s.fontWeight),this.letterSpacing=b(e,ws,s.letterSpacing),this.lineBreak=b(e,ps,s.lineBreak),this.lineHeight=b(e,pn,s.lineHeight),this.listStyleImage=b(e,xr,s.listStyleImage),this.listStylePosition=b(e,Qs,s.listStylePosition),this.listStyleType=b(e,mi,s.listStyleType),this.marginTop=b(e,Ci,s.marginTop),this.marginRight=b(e,Qn,s.marginRight),this.marginBottom=b(e,ms,s.marginBottom),this.marginLeft=b(e,ia,s.marginLeft),this.opacity=b(e,Ua,s.opacity);var h=b(e,na,s.overflow);this.overflowX=h[0],this.overflowY=h[h.length>1?1:0],this.overflowWrap=b(e,sa,s.overflowWrap),this.paddingTop=b(e,oa,s.paddingTop),this.paddingRight=b(e,aa,s.paddingRight),this.paddingBottom=b(e,ua,s.paddingBottom),this.paddingLeft=b(e,ha,s.paddingLeft),this.paintOrder=b(e,Pa,s.paintOrder),this.position=b(e,la,s.position),this.textAlign=b(e,ca,s.textAlign),this.textDecorationColor=b(e,Fa,(i=s.textDecorationColor)!==null&&i!==void 0?i:s.color),this.textDecorationLine=b(e,ya,(a=s.textDecorationLine)!==null&&a!==void 0?a:s.textDecoration),this.textShadow=b(e,Ba,s.textShadow),this.textTransform=b(e,fa,s.textTransform),this.transform=b(e,ga,s.transform),this.transformOrigin=b(e,ma,s.transformOrigin),this.visibility=b(e,Ca,s.visibility),this.webkitTextStrokeColor=b(e,Oa,s.webkitTextStrokeColor),this.webkitTextStrokeWidth=b(e,Ra,s.webkitTextStrokeWidth),this.wordBreak=b(e,_a,s.wordBreak),this.zIndex=b(e,va,s.zIndex)}return r.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},r.prototype.isTransparent=function(){return TA(this.backgroundColor)},r.prototype.isTransformed=function(){return this.transform!==null},r.prototype.isPositioned=function(){return this.position!==0},r.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},r.prototype.isFloating=function(){return this.float!==0},r.prototype.isInlineLevel=function(){return IA(this.display,4)||IA(this.display,33554432)||IA(this.display,268435456)||IA(this.display,536870912)||IA(this.display,67108864)||IA(this.display,134217728)},r}(),Ga=function(){function r(e,s){this.content=b(e,ba,s.content),this.quotes=b(e,Ma,s.quotes)}return r}(),js=function(){function r(e,s){this.counterIncrement=b(e,Ta,s.counterIncrement),this.counterReset=b(e,Ka,s.counterReset)}return r}(),b=function(r,e,s){var i=new di,a=s!==null&&typeof s<"u"?s.toString():e.initialValue;i.write(a);var h=new qi(i.read());switch(e.type){case 2:var c=h.parseComponentValue();return e.parse(r,nA(c)?c.value:e.initialValue);case 0:return e.parse(r,h.parseComponentValue());case 1:return e.parse(r,h.parseComponentValues());case 4:return h.parseComponentValue();case 3:switch(e.format){case"angle":return Fr.parse(r,h.parseComponentValue());case"color":return Kt.parse(r,h.parseComponentValue());case"image":return $t.parse(r,h.parseComponentValue());case"length":var f=h.parseComponentValue();return bA(f)?f:EA;case"length-percentage":var B=h.parseComponentValue();return QA(B)?B:EA;case"time":return Ys.parse(r,h.parseComponentValue())}break}},ka="data-html2canvas-debug",Va=function(r){var e=r.getAttribute(ka);switch(e){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Cs=function(r,e){var s=Va(r);return s===1||e===s},Mt=function(){function r(e,s){if(this.context=e,this.textNodes=[],this.elements=[],this.flags=0,Cs(s,3))debugger;this.styles=new Na(e,window.getComputedStyle(s,null)),Ss(s)&&(this.styles.animationDuration.some(function(i){return i>0})&&(s.style.animationDuration="0s"),this.styles.transform!==null&&(s.style.transform="none")),this.bounds=dt(this.context,s),Cs(s,4)&&(this.flags|=16)}return r}(),Za="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",$s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_i=typeof Uint8Array>"u"?[]:new Uint8Array(256),_n=0;_n<$s.length;_n++)_i[$s.charCodeAt(_n)]=_n;for(var Xa=function(r){var e=r.length*.75,s=r.length,i,a=0,h,c,f,B;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var d=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),w=Array.isArray(d)?d:new Uint8Array(d);for(i=0;i<s;i+=4)h=_i[r.charCodeAt(i)],c=_i[r.charCodeAt(i+1)],f=_i[r.charCodeAt(i+2)],B=_i[r.charCodeAt(i+3)],w[a++]=h<<2|c>>4,w[a++]=(c&15)<<4|f>>2,w[a++]=(f&3)<<6|B&63;return d},za=function(r){for(var e=r.length,s=[],i=0;i<e;i+=2)s.push(r[i+1]<<8|r[i]);return s},Wa=function(r){for(var e=r.length,s=[],i=0;i<e;i+=4)s.push(r[i+3]<<24|r[i+2]<<16|r[i+1]<<8|r[i]);return s},ze=5,_s=11,vs=2,Ja=_s-ze,Ao=65536>>ze,Ya=1<<ze,Us=Ya-1,qa=1024>>ze,ja=Ao+qa,$a=ja,Au=32,tu=$a+Au,eu=65536>>_s,ru=1<<Ja,iu=ru-1,to=function(r,e,s){return r.slice?r.slice(e,s):new Uint16Array(Array.prototype.slice.call(r,e,s))},nu=function(r,e,s){return r.slice?r.slice(e,s):new Uint32Array(Array.prototype.slice.call(r,e,s))},su=function(r,e){var s=Xa(r),i=Array.isArray(s)?Wa(s):new Uint32Array(s),a=Array.isArray(s)?za(s):new Uint16Array(s),h=24,c=to(a,h/2,i[4]/2),f=i[5]===2?to(a,(h+i[4])/2):nu(i,Math.ceil((h+i[4])/4));return new ou(i[0],i[1],i[2],i[3],c,f)},ou=function(){function r(e,s,i,a,h,c){this.initialValue=e,this.errorValue=s,this.highStart=i,this.highValueIndex=a,this.index=h,this.data=c}return r.prototype.get=function(e){var s;if(e>=0){if(e<55296||e>56319&&e<=65535)return s=this.index[e>>ze],s=(s<<vs)+(e&Us),this.data[s];if(e<=65535)return s=this.index[Ao+(e-55296>>ze)],s=(s<<vs)+(e&Us),this.data[s];if(e<this.highStart)return s=tu-eu+(e>>_s),s=this.index[s],s+=e>>ze&iu,s=this.index[s],s=(s<<vs)+(e&Us),this.data[s];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},r}(),eo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",au=typeof Uint8Array>"u"?[]:new Uint8Array(256),vn=0;vn<eo.length;vn++)au[eo.charCodeAt(vn)]=vn;var uu=1,Fs=2,ys=3,ro=4,io=5,hu=7,no=8,Es=9,Hs=10,so=11,oo=12,ao=13,uo=14,Is=15,cu=function(r){for(var e=[],s=0,i=r.length;s<i;){var a=r.charCodeAt(s++);if(a>=55296&&a<=56319&&s<i){var h=r.charCodeAt(s++);(h&64512)===56320?e.push(((a&1023)<<10)+(h&1023)+65536):(e.push(a),s--)}else e.push(a)}return e},lu=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,r);var s=r.length;if(!s)return"";for(var i=[],a=-1,h="";++a<s;){var c=r[a];c<=65535?i.push(c):(c-=65536,i.push((c>>10)+55296,c%1024+56320)),(a+1===s||i.length>16384)&&(h+=String.fromCharCode.apply(String,i),i.length=0)}return h},Bu=su(Za),Bt="\xD7",Ls="\xF7",fu=function(r){return Bu.get(r)},gu=function(r,e,s){var i=s-2,a=e[i],h=e[s-1],c=e[s];if(h===Fs&&c===ys)return Bt;if(h===Fs||h===ys||h===ro||c===Fs||c===ys||c===ro)return Ls;if(h===no&&[no,Es,so,oo].indexOf(c)!==-1||(h===so||h===Es)&&(c===Es||c===Hs)||(h===oo||h===Hs)&&c===Hs||c===ao||c===io||c===hu||h===uu)return Bt;if(h===ao&&c===uo){for(;a===io;)a=e[--i];if(a===uo)return Bt}if(h===Is&&c===Is){for(var f=0;a===Is;)f++,a=e[--i];if(f%2===0)return Bt}return Ls},du=function(r){var e=cu(r),s=e.length,i=0,a=0,h=e.map(fu);return{next:function(){if(i>=s)return{done:!0,value:null};for(var c=Bt;i<s&&(c=gu(e,h,++i))===Bt;);if(c!==Bt||i===s){var f=lu.apply(null,e.slice(a,i));return a=i,{value:f,done:!1}}return{done:!0,value:null}}}},wu=function(r){for(var e=du(r),s=[],i;!(i=e.next()).done;)i.value&&s.push(i.value.slice());return s},pu=function(r){var e=123;if(r.createRange){var s=r.createRange();if(s.getBoundingClientRect){var i=r.createElement("boundtest");i.style.height=e+"px",i.style.display="block",r.body.appendChild(i),s.selectNode(i);var a=s.getBoundingClientRect(),h=Math.round(a.height);if(r.body.removeChild(i),h===e)return!0}}return!1},Qu=function(r){var e=r.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",r.body.appendChild(e);var s=r.createRange();e.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var i=e.firstChild,a=sA(i.data).map(function(B){return $(B)}),h=0,c={},f=a.every(function(B,d){s.setStart(i,h),s.setEnd(i,h+B.length);var w=s.getBoundingClientRect();h+=B.length;var p=w.x>c.x||w.y>c.y;return c=w,d===0?!0:p});return r.body.removeChild(e),f},mu=function(){return typeof new Image().crossOrigin<"u"},Cu=function(){return typeof new XMLHttpRequest().responseType=="string"},_u=function(r){var e=new Image,s=r.createElement("canvas"),i=s.getContext("2d");if(!i)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{i.drawImage(e,0,0),s.toDataURL()}catch{return!1}return!0},ho=function(r){return r[0]===0&&r[1]===255&&r[2]===0&&r[3]===255},vu=function(r){var e=r.createElement("canvas"),s=100;e.width=s,e.height=s;var i=e.getContext("2d");if(!i)return Promise.reject(!1);i.fillStyle="rgb(0, 255, 0)",i.fillRect(0,0,s,s);var a=new Image,h=e.toDataURL();a.src=h;var c=xs(s,s,0,0,a);return i.fillStyle="red",i.fillRect(0,0,s,s),co(c).then(function(f){i.drawImage(f,0,0);var B=i.getImageData(0,0,s,s).data;i.fillStyle="red",i.fillRect(0,0,s,s);var d=r.createElement("div");return d.style.backgroundImage="url("+h+")",d.style.height=s+"px",ho(B)?co(xs(s,s,0,0,d)):Promise.reject(!1)}).then(function(f){return i.drawImage(f,0,0),ho(i.getImageData(0,0,s,s).data)}).catch(function(){return!1})},xs=function(r,e,s,i,a){var h="http://www.w3.org/2000/svg",c=document.createElementNS(h,"svg"),f=document.createElementNS(h,"foreignObject");return c.setAttributeNS(null,"width",r.toString()),c.setAttributeNS(null,"height",e.toString()),f.setAttributeNS(null,"width","100%"),f.setAttributeNS(null,"height","100%"),f.setAttributeNS(null,"x",s.toString()),f.setAttributeNS(null,"y",i.toString()),f.setAttributeNS(null,"externalResourcesRequired","true"),c.appendChild(f),f.appendChild(a),c},co=function(r){return new Promise(function(e,s){var i=new Image;i.onload=function(){return e(i)},i.onerror=s,i.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(r))})},MA={get SUPPORT_RANGE_BOUNDS(){var r=pu(document);return Object.defineProperty(MA,"SUPPORT_RANGE_BOUNDS",{value:r}),r},get SUPPORT_WORD_BREAKING(){var r=MA.SUPPORT_RANGE_BOUNDS&&Qu(document);return Object.defineProperty(MA,"SUPPORT_WORD_BREAKING",{value:r}),r},get SUPPORT_SVG_DRAWING(){var r=_u(document);return Object.defineProperty(MA,"SUPPORT_SVG_DRAWING",{value:r}),r},get SUPPORT_FOREIGNOBJECT_DRAWING(){var r=typeof Array.from=="function"&&typeof window.fetch=="function"?vu(document):Promise.resolve(!1);return Object.defineProperty(MA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:r}),r},get SUPPORT_CORS_IMAGES(){var r=mu();return Object.defineProperty(MA,"SUPPORT_CORS_IMAGES",{value:r}),r},get SUPPORT_RESPONSE_TYPE(){var r=Cu();return Object.defineProperty(MA,"SUPPORT_RESPONSE_TYPE",{value:r}),r},get SUPPORT_CORS_XHR(){var r="withCredentials"in new XMLHttpRequest;return Object.defineProperty(MA,"SUPPORT_CORS_XHR",{value:r}),r},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var r=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(MA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:r}),r}},vi=function(){function r(e,s){this.text=e,this.bounds=s}return r}(),Uu=function(r,e,s,i){var a=Eu(e,s),h=[],c=0;return a.forEach(function(f){if(s.textDecorationLine.length||f.trim().length>0)if(MA.SUPPORT_RANGE_BOUNDS){var B=lo(i,c,f.length).getClientRects();if(B.length>1){var d=bs(f),w=0;d.forEach(function(C){h.push(new vi(C,R.fromDOMRectList(r,lo(i,w+c,C.length).getClientRects()))),w+=C.length})}else h.push(new vi(f,R.fromDOMRectList(r,B)))}else{var p=i.splitText(f.length);h.push(new vi(f,Fu(r,i))),i=p}else MA.SUPPORT_RANGE_BOUNDS||(i=i.splitText(f.length));c+=f.length}),h},Fu=function(r,e){var s=e.ownerDocument;if(s){var i=s.createElement("html2canvaswrapper");i.appendChild(e.cloneNode(!0));var a=e.parentNode;if(a){a.replaceChild(i,e);var h=dt(r,i);return i.firstChild&&a.replaceChild(i.firstChild,i),h}}return R.EMPTY},lo=function(r,e,s){var i=r.ownerDocument;if(!i)throw new Error("Node has no owner document");var a=i.createRange();return a.setStart(r,e),a.setEnd(r,e+s),a},bs=function(r){if(MA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(r)).map(function(s){return s.segment})}return wu(r)},yu=function(r,e){if(MA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var s=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(s.segment(r)).map(function(i){return i.segment})}return Iu(r,e)},Eu=function(r,e){return e.letterSpacing!==0?bs(r):yu(r,e)},Hu=[32,160,4961,65792,65793,4153,4241],Iu=function(r,e){for(var s=lr(r,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap==="break-word"?"break-word":e.wordBreak}),i=[],a,h=function(){if(a.value){var c=a.value.slice(),f=sA(c),B="";f.forEach(function(d){Hu.indexOf(d)===-1?B+=$(d):(B.length&&i.push(B),i.push($(d)),B="")}),B.length&&i.push(B)}};!(a=s.next()).done;)h();return i},Lu=function(){function r(e,s,i){this.text=xu(s.data,i.textTransform),this.textBounds=Uu(e,this.text,i,s)}return r}(),xu=function(r,e){switch(e){case 1:return r.toLowerCase();case 3:return r.replace(bu,Tu);case 2:return r.toUpperCase();default:return r}},bu=/(^|\s|:|-|\(|\))([a-z])/g,Tu=function(r,e,s){return r.length>0?e+s.toUpperCase():r},Bo=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.src=i.currentSrc||i.src,a.intrinsicWidth=i.naturalWidth,a.intrinsicHeight=i.naturalHeight,a.context.cache.addImage(a.src),a}return e}(Mt),fo=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.canvas=i,a.intrinsicWidth=i.width,a.intrinsicHeight=i.height,a}return e}(Mt),go=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this,h=new XMLSerializer,c=dt(s,i);return i.setAttribute("width",c.width+"px"),i.setAttribute("height",c.height+"px"),a.svg="data:image/svg+xml,"+encodeURIComponent(h.serializeToString(i)),a.intrinsicWidth=i.width.baseVal.value,a.intrinsicHeight=i.height.baseVal.value,a.context.cache.addImage(a.svg),a}return e}(Mt),wo=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.value=i.value,a}return e}(Mt),Ts=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.start=i.start,a.reversed=typeof i.reversed=="boolean"&&i.reversed===!0,a}return e}(Mt),Ku=[{type:15,flags:0,unit:"px",number:3}],Su=[{type:16,flags:0,number:50}],Mu=function(r){return r.width>r.height?new R(r.left+(r.width-r.height)/2,r.top,r.height,r.height):r.width<r.height?new R(r.left,r.top+(r.height-r.width)/2,r.width,r.width):r},Du=function(r){var e=r.type===Pu?new Array(r.value.length+1).join("\u2022"):r.value;return e.length===0?r.placeholder||"":e},Un="checkbox",Fn="radio",Pu="password",po=707406591,Ks=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;switch(a.type=i.type.toLowerCase(),a.checked=i.checked,a.value=Du(i),(a.type===Un||a.type===Fn)&&(a.styles.backgroundColor=3739148031,a.styles.borderTopColor=a.styles.borderRightColor=a.styles.borderBottomColor=a.styles.borderLeftColor=2779096575,a.styles.borderTopWidth=a.styles.borderRightWidth=a.styles.borderBottomWidth=a.styles.borderLeftWidth=1,a.styles.borderTopStyle=a.styles.borderRightStyle=a.styles.borderBottomStyle=a.styles.borderLeftStyle=1,a.styles.backgroundClip=[0],a.styles.backgroundOrigin=[0],a.bounds=Mu(a.bounds)),a.type){case Un:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=Ku;break;case Fn:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=Su;break}return a}return e}(Mt),Qo=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this,h=i.options[i.selectedIndex||0];return a.value=h&&h.text||"",a}return e}(Mt),mo=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.value=i.value,a}return e}(Mt),Co=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;a.src=i.src,a.width=parseInt(i.width,10)||0,a.height=parseInt(i.height,10)||0,a.backgroundColor=a.styles.backgroundColor;try{if(i.contentWindow&&i.contentWindow.document&&i.contentWindow.document.documentElement){a.tree=vo(s,i.contentWindow.document.documentElement);var h=i.contentWindow.document.documentElement?St(s,getComputedStyle(i.contentWindow.document.documentElement).backgroundColor):st.TRANSPARENT,c=i.contentWindow.document.body?St(s,getComputedStyle(i.contentWindow.document.body).backgroundColor):st.TRANSPARENT;a.backgroundColor=TA(h)?TA(c)?a.styles.backgroundColor:c:h}}catch{}return a}return e}(Mt),Ou=["OL","UL","MENU"],yn=function(r,e,s,i){for(var a=e.firstChild,h=void 0;a;a=h)if(h=a.nextSibling,Uo(a)&&a.data.trim().length>0)s.textNodes.push(new Lu(r,a,s.styles));else if(Tr(a))if(Lo(a)&&a.assignedNodes)a.assignedNodes().forEach(function(f){return yn(r,f,s,i)});else{var c=_o(r,a);c.styles.isVisible()&&(Ru(a,c,i)?c.flags|=4:Nu(c.styles)&&(c.flags|=2),Ou.indexOf(a.tagName)!==-1&&(c.flags|=8),s.elements.push(c),a.slot,a.shadowRoot?yn(r,a.shadowRoot,c,i):!Hn(a)&&!Fo(a)&&!In(a)&&yn(r,a,c,i))}},_o=function(r,e){return Ds(e)?new Bo(r,e):yo(e)?new fo(r,e):Fo(e)?new go(r,e):Gu(e)?new wo(r,e):ku(e)?new Ts(r,e):Vu(e)?new Ks(r,e):In(e)?new Qo(r,e):Hn(e)?new mo(r,e):Ho(e)?new Co(r,e):new Mt(r,e)},vo=function(r,e){var s=_o(r,e);return s.flags|=4,yn(r,e,s,s),s},Ru=function(r,e,s){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Ms(r)&&s.styles.isTransparent()},Nu=function(r){return r.isPositioned()||r.isFloating()},Uo=function(r){return r.nodeType===Node.TEXT_NODE},Tr=function(r){return r.nodeType===Node.ELEMENT_NODE},Ss=function(r){return Tr(r)&&typeof r.style<"u"&&!En(r)},En=function(r){return typeof r.className=="object"},Gu=function(r){return r.tagName==="LI"},ku=function(r){return r.tagName==="OL"},Vu=function(r){return r.tagName==="INPUT"},Zu=function(r){return r.tagName==="HTML"},Fo=function(r){return r.tagName==="svg"},Ms=function(r){return r.tagName==="BODY"},yo=function(r){return r.tagName==="CANVAS"},Eo=function(r){return r.tagName==="VIDEO"},Ds=function(r){return r.tagName==="IMG"},Ho=function(r){return r.tagName==="IFRAME"},Io=function(r){return r.tagName==="STYLE"},Xu=function(r){return r.tagName==="SCRIPT"},Hn=function(r){return r.tagName==="TEXTAREA"},In=function(r){return r.tagName==="SELECT"},Lo=function(r){return r.tagName==="SLOT"},xo=function(r){return r.tagName.indexOf("-")>0},zu=function(){function r(){this.counters={}}return r.prototype.getCounterValue=function(e){var s=this.counters[e];return s&&s.length?s[s.length-1]:1},r.prototype.getCounterValues=function(e){var s=this.counters[e];return s||[]},r.prototype.pop=function(e){var s=this;e.forEach(function(i){return s.counters[i].pop()})},r.prototype.parse=function(e){var s=this,i=e.counterIncrement,a=e.counterReset,h=!0;i!==null&&i.forEach(function(f){var B=s.counters[f.counter];B&&f.increment!==0&&(h=!1,B.length||B.push(1),B[Math.max(0,B.length-1)]+=f.increment)});var c=[];return h&&a.forEach(function(f){var B=s.counters[f.counter];c.push(f.counter),B||(B=s.counters[f.counter]=[]),B.push(f.reset)}),c},r}(),bo={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},To={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},Wu={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},Ju={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},Kr=function(r,e,s,i,a,h){return r<e||r>s?Fi(r,a,h.length>0):i.integers.reduce(function(c,f,B){for(;r>=f;)r-=f,c+=i.values[B];return c},"")+h},Ko=function(r,e,s,i){var a="";do s||r--,a=i(r)+a,r/=e;while(r*e>=e);return a},CA=function(r,e,s,i,a){var h=s-e+1;return(r<0?"-":"")+(Ko(Math.abs(r),h,i,function(c){return $(Math.floor(c%h)+e)})+a)},We=function(r,e,s){s===void 0&&(s=". ");var i=e.length;return Ko(Math.abs(r),i,!1,function(a){return e[Math.floor(a%i)]})+s},Sr=1,Ce=2,_e=4,Ui=8,Ae=function(r,e,s,i,a,h){if(r<-9999||r>9999)return Fi(r,4,a.length>0);var c=Math.abs(r),f=a;if(c===0)return e[0]+f;for(var B=0;c>0&&B<=4;B++){var d=c%10;d===0&&IA(h,Sr)&&f!==""?f=e[d]+f:d>1||d===1&&B===0||d===1&&B===1&&IA(h,Ce)||d===1&&B===1&&IA(h,_e)&&r>100||d===1&&B>1&&IA(h,Ui)?f=e[d]+(B>0?s[B-1]:"")+f:d===1&&B>0&&(f=s[B-1]+f),c=Math.floor(c/10)}return(r<0?i:"")+f},So="\u5341\u767E\u5343\u842C",Mo="\u62FE\u4F70\u4EDF\u842C",Do="\u30DE\u30A4\u30CA\u30B9",Ps="\uB9C8\uC774\uB108\uC2A4",Fi=function(r,e,s){var i=s?". ":"",a=s?"\u3001":"",h=s?", ":"",c=s?" ":"";switch(e){case 0:return"\u2022"+c;case 1:return"\u25E6"+c;case 2:return"\u25FE"+c;case 5:var f=CA(r,48,57,!0,i);return f.length<4?"0"+f:f;case 4:return We(r,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",a);case 6:return Kr(r,1,3999,bo,3,i).toLowerCase();case 7:return Kr(r,1,3999,bo,3,i);case 8:return CA(r,945,969,!1,i);case 9:return CA(r,97,122,!1,i);case 10:return CA(r,65,90,!1,i);case 11:return CA(r,1632,1641,!0,i);case 12:case 49:return Kr(r,1,9999,To,3,i);case 35:return Kr(r,1,9999,To,3,i).toLowerCase();case 13:return CA(r,2534,2543,!0,i);case 14:case 30:return CA(r,6112,6121,!0,i);case 15:return We(r,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",a);case 16:return We(r,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",a);case 17:case 48:return Ae(r,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",So,"\u8CA0",a,Ce|_e|Ui);case 47:return Ae(r,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",Mo,"\u8CA0",a,Sr|Ce|_e|Ui);case 42:return Ae(r,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",So,"\u8D1F",a,Ce|_e|Ui);case 41:return Ae(r,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",Mo,"\u8D1F",a,Sr|Ce|_e|Ui);case 26:return Ae(r,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",Do,a,0);case 25:return Ae(r,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",Do,a,Sr|Ce|_e);case 31:return Ae(r,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",Ps,h,Sr|Ce|_e);case 33:return Ae(r,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",Ps,h,0);case 32:return Ae(r,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",Ps,h,Sr|Ce|_e);case 18:return CA(r,2406,2415,!0,i);case 20:return Kr(r,1,19999,Ju,3,i);case 21:return CA(r,2790,2799,!0,i);case 22:return CA(r,2662,2671,!0,i);case 22:return Kr(r,1,10999,Wu,3,i);case 23:return We(r,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return We(r,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return CA(r,3302,3311,!0,i);case 28:return We(r,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",a);case 29:return We(r,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",a);case 34:return CA(r,3792,3801,!0,i);case 37:return CA(r,6160,6169,!0,i);case 38:return CA(r,4160,4169,!0,i);case 39:return CA(r,2918,2927,!0,i);case 40:return CA(r,1776,1785,!0,i);case 43:return CA(r,3046,3055,!0,i);case 44:return CA(r,3174,3183,!0,i);case 45:return CA(r,3664,3673,!0,i);case 46:return CA(r,3872,3881,!0,i);case 3:default:return CA(r,48,57,!0,i)}},Po="data-html2canvas-ignore",Oo=function(){function r(e,s,i){if(this.context=e,this.options=i,this.scrolledElements=[],this.referenceElement=s,this.counters=new zu,this.quoteDepth=0,!s.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(s.ownerDocument.documentElement,!1)}return r.prototype.toIFrame=function(e,s){var i=this,a=Yu(e,s);if(!a.contentWindow)return Promise.reject("Unable to find iframe window");var h=e.defaultView.pageXOffset,c=e.defaultView.pageYOffset,f=a.contentWindow,B=f.document,d=$u(a).then(function(){return j(i,void 0,void 0,function(){var w,p;return M(this,function(C){switch(C.label){case 0:return this.scrolledElements.forEach(rh),f&&(f.scrollTo(s.left,s.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(f.scrollY!==s.top||f.scrollX!==s.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(f.scrollX-s.left,f.scrollY-s.top,0,0))),w=this.options.onclone,p=this.clonedReferenceElement,typeof p>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:B.fonts&&B.fonts.ready?[4,B.fonts.ready]:[3,2];case 1:C.sent(),C.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,ju(B)]:[3,4];case 3:C.sent(),C.label=4;case 4:return typeof w=="function"?[2,Promise.resolve().then(function(){return w(B,p)}).then(function(){return a})]:[2,a]}})})});return B.open(),B.write(th(document.doctype)+"<html></html>"),eh(this.referenceElement.ownerDocument,h,c),B.replaceChild(B.adoptNode(this.documentElement),B.documentElement),B.close(),d},r.prototype.createElementClone=function(e){if(Cs(e,2))debugger;if(yo(e))return this.createCanvasClone(e);if(Eo(e))return this.createVideoClone(e);if(Io(e))return this.createStyleClone(e);var s=e.cloneNode(!1);return Ds(s)&&(Ds(e)&&e.currentSrc&&e.currentSrc!==e.src&&(s.src=e.currentSrc,s.srcset=""),s.loading==="lazy"&&(s.loading="eager")),xo(s)?this.createCustomElementClone(s):s},r.prototype.createCustomElementClone=function(e){var s=document.createElement("html2canvascustomelement");return Os(e.style,s),s},r.prototype.createStyleClone=function(e){try{var s=e.sheet;if(s&&s.cssRules){var i=[].slice.call(s.cssRules,0).reduce(function(h,c){return c&&typeof c.cssText=="string"?h+c.cssText:h},""),a=e.cloneNode(!1);return a.textContent=i,a}}catch(h){if(this.context.logger.error("Unable to access cssRules property",h),h.name!=="SecurityError")throw h}return e.cloneNode(!1)},r.prototype.createCanvasClone=function(e){var s;if(this.options.inlineImages&&e.ownerDocument){var i=e.ownerDocument.createElement("img");try{return i.src=e.toDataURL(),i}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",e)}}var a=e.cloneNode(!1);try{a.width=e.width,a.height=e.height;var h=e.getContext("2d"),c=a.getContext("2d");if(c)if(!this.options.allowTaint&&h)c.putImageData(h.getImageData(0,0,e.width,e.height),0,0);else{var f=(s=e.getContext("webgl2"))!==null&&s!==void 0?s:e.getContext("webgl");if(f){var B=f.getContextAttributes();B?.preserveDrawingBuffer===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",e)}c.drawImage(e,0,0)}return a}catch{this.context.logger.info("Unable to clone canvas as it is tainted",e)}return a},r.prototype.createVideoClone=function(e){var s=e.ownerDocument.createElement("canvas");s.width=e.offsetWidth,s.height=e.offsetHeight;var i=s.getContext("2d");try{return i&&(i.drawImage(e,0,0,s.width,s.height),this.options.allowTaint||i.getImageData(0,0,s.width,s.height)),s}catch{this.context.logger.info("Unable to clone video as it is tainted",e)}var a=e.ownerDocument.createElement("canvas");return a.width=e.offsetWidth,a.height=e.offsetHeight,a},r.prototype.appendChildNode=function(e,s,i){(!Tr(s)||!Xu(s)&&!s.hasAttribute(Po)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(s)))&&(!this.options.copyStyles||!Tr(s)||!Io(s))&&e.appendChild(this.cloneNode(s,i))},r.prototype.cloneChildNodes=function(e,s,i){for(var a=this,h=e.shadowRoot?e.shadowRoot.firstChild:e.firstChild;h;h=h.nextSibling)if(Tr(h)&&Lo(h)&&typeof h.assignedNodes=="function"){var c=h.assignedNodes();c.length&&c.forEach(function(f){return a.appendChildNode(s,f,i)})}else this.appendChildNode(s,h,i)},r.prototype.cloneNode=function(e,s){if(Uo(e))return document.createTextNode(e.data);if(!e.ownerDocument)return e.cloneNode(!1);var i=e.ownerDocument.defaultView;if(i&&Tr(e)&&(Ss(e)||En(e))){var a=this.createElementClone(e);a.style.transitionProperty="none";var h=i.getComputedStyle(e),c=i.getComputedStyle(e,":before"),f=i.getComputedStyle(e,":after");this.referenceElement===e&&Ss(a)&&(this.clonedReferenceElement=a),Ms(a)&&sh(a);var B=this.counters.parse(new js(this.context,h)),d=this.resolvePseudoContent(e,a,c,Ln.BEFORE);xo(e)&&(s=!0),Eo(e)||this.cloneChildNodes(e,a,s),d&&a.insertBefore(d,a.firstChild);var w=this.resolvePseudoContent(e,a,f,Ln.AFTER);return w&&a.appendChild(w),this.counters.pop(B),(h&&(this.options.copyStyles||En(e))&&!Ho(e)||s)&&Os(h,a),(e.scrollTop!==0||e.scrollLeft!==0)&&this.scrolledElements.push([a,e.scrollLeft,e.scrollTop]),(Hn(e)||In(e))&&(Hn(a)||In(a))&&(a.value=e.value),a}return e.cloneNode(!1)},r.prototype.resolvePseudoContent=function(e,s,i,a){var h=this;if(i){var c=i.content,f=s.ownerDocument;if(!(!f||!c||c==="none"||c==="-moz-alt-content"||i.display==="none")){this.counters.parse(new js(this.context,i));var B=new Ga(this.context,i),d=f.createElement("html2canvaspseudoelement");Os(i,d),B.content.forEach(function(p){if(p.type===0)d.appendChild(f.createTextNode(p.value));else if(p.type===22){var C=f.createElement("img");C.src=p.value,C.style.opacity="1",d.appendChild(C)}else if(p.type===18){if(p.name==="attr"){var x=p.values.filter(nA);x.length&&d.appendChild(f.createTextNode(e.getAttribute(x[0].value)||""))}else if(p.name==="counter"){var U=p.values.filter(xA),y=U[0],X=U[1];if(y&&nA(y)){var S=h.counters.getCounterValue(y.value),T=X&&nA(X)?mi.parse(h.context,X.value):3;d.appendChild(f.createTextNode(Fi(S,T,!1)))}}else if(p.name==="counters"){var eA=p.values.filter(xA),y=eA[0],W=eA[1],X=eA[2];if(y&&nA(y)){var O=h.counters.getCounterValues(y.value),I=X&&nA(X)?mi.parse(h.context,X.value):3,AA=W&&W.type===0?W.value:"",tA=O.map(function(At){return Fi(At,I,!1)}).join(AA);d.appendChild(f.createTextNode(tA))}}}else if(p.type===20)switch(p.value){case"open-quote":d.appendChild(f.createTextNode(qs(B.quotes,h.quoteDepth++,!0)));break;case"close-quote":d.appendChild(f.createTextNode(qs(B.quotes,--h.quoteDepth,!1)));break;default:d.appendChild(f.createTextNode(p.value))}}),d.className=Rs+" "+Ns;var w=a===Ln.BEFORE?" "+Rs:" "+Ns;return En(s)?s.className.baseValue+=w:s.className+=w,d}}},r.destroy=function(e){return e.parentNode?(e.parentNode.removeChild(e),!0):!1},r}(),Ln=function(r){return r[r.BEFORE=0]="BEFORE",r[r.AFTER=1]="AFTER",r}(Ln||{}),Yu=function(r,e){var s=r.createElement("iframe");return s.className="html2canvas-container",s.style.visibility="hidden",s.style.position="fixed",s.style.left="-10000px",s.style.top="0px",s.style.border="0",s.width=e.width.toString(),s.height=e.height.toString(),s.scrolling="no",s.setAttribute(Po,"true"),r.body.appendChild(s),s},qu=function(r){return new Promise(function(e){if(r.complete){e();return}if(!r.src){e();return}r.onload=e,r.onerror=e})},ju=function(r){return Promise.all([].slice.call(r.images,0).map(qu))},$u=function(r){return new Promise(function(e,s){var i=r.contentWindow;if(!i)return s("No window assigned for iframe");var a=i.document;i.onload=r.onload=function(){i.onload=r.onload=null;var h=setInterval(function(){a.body.childNodes.length>0&&a.readyState==="complete"&&(clearInterval(h),e(r))},50)}})},Ah=["all","d","content"],Os=function(r,e){for(var s=r.length-1;s>=0;s--){var i=r.item(s);Ah.indexOf(i)===-1&&e.style.setProperty(i,r.getPropertyValue(i))}return e},th=function(r){var e="";return r&&(e+="<!DOCTYPE ",r.name&&(e+=r.name),r.internalSubset&&(e+=r.internalSubset),r.publicId&&(e+='"'+r.publicId+'"'),r.systemId&&(e+='"'+r.systemId+'"'),e+=">"),e},eh=function(r,e,s){r&&r.defaultView&&(e!==r.defaultView.pageXOffset||s!==r.defaultView.pageYOffset)&&r.defaultView.scrollTo(e,s)},rh=function(r){var e=r[0],s=r[1],i=r[2];e.scrollLeft=s,e.scrollTop=i},ih=":before",nh=":after",Rs="___html2canvas___pseudoelement_before",Ns="___html2canvas___pseudoelement_after",Ro=`{
    content: "" !important;
    display: none !important;
}`,sh=function(r){oh(r,"."+Rs+ih+Ro+`
         .`+Ns+nh+Ro)},oh=function(r,e){var s=r.ownerDocument;if(s){var i=s.createElement("style");i.textContent=e,r.appendChild(i)}},No=function(){function r(){}return r.getOrigin=function(e){var s=r._link;return s?(s.href=e,s.href=s.href,s.protocol+s.hostname+s.port):"about:blank"},r.isSameOrigin=function(e){return r.getOrigin(e)===r._origin},r.setContext=function(e){r._link=e.document.createElement("a"),r._origin=r.getOrigin(e.location.href)},r._origin="about:blank",r}(),ah=function(){function r(e,s){this.context=e,this._options=s,this._cache={}}return r.prototype.addImage=function(e){var s=Promise.resolve();return this.has(e)||(ks(e)||lh(e))&&(this._cache[e]=this.loadImage(e)).catch(function(){}),s},r.prototype.match=function(e){return this._cache[e]},r.prototype.loadImage=function(e){return j(this,void 0,void 0,function(){var s,i,a,h,c=this;return M(this,function(f){switch(f.label){case 0:return s=No.isSameOrigin(e),i=!Gs(e)&&this._options.useCORS===!0&&MA.SUPPORT_CORS_IMAGES&&!s,a=!Gs(e)&&!s&&!ks(e)&&typeof this._options.proxy=="string"&&MA.SUPPORT_CORS_XHR&&!i,!s&&this._options.allowTaint===!1&&!Gs(e)&&!ks(e)&&!a&&!i?[2]:(h=e,a?[4,this.proxy(h)]:[3,2]);case 1:h=f.sent(),f.label=2;case 2:return this.context.logger.debug("Added image "+e.substring(0,256)),[4,new Promise(function(B,d){var w=new Image;w.onload=function(){return B(w)},w.onerror=d,(Bh(h)||i)&&(w.crossOrigin="anonymous"),w.src=h,w.complete===!0&&setTimeout(function(){return B(w)},500),c._options.imageTimeout>0&&setTimeout(function(){return d("Timed out ("+c._options.imageTimeout+"ms) loading image")},c._options.imageTimeout)})];case 3:return[2,f.sent()]}})})},r.prototype.has=function(e){return typeof this._cache[e]<"u"},r.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},r.prototype.proxy=function(e){var s=this,i=this._options.proxy;if(!i)throw new Error("No proxy defined");var a=e.substring(0,256);return new Promise(function(h,c){var f=MA.SUPPORT_RESPONSE_TYPE?"blob":"text",B=new XMLHttpRequest;B.onload=function(){if(B.status===200)if(f==="text")h(B.response);else{var p=new FileReader;p.addEventListener("load",function(){return h(p.result)},!1),p.addEventListener("error",function(C){return c(C)},!1),p.readAsDataURL(B.response)}else c("Failed to proxy resource "+a+" with status code "+B.status)},B.onerror=c;var d=i.indexOf("?")>-1?"&":"?";if(B.open("GET",""+i+d+"url="+encodeURIComponent(e)+"&responseType="+f),f!=="text"&&B instanceof XMLHttpRequest&&(B.responseType=f),s._options.imageTimeout){var w=s._options.imageTimeout;B.timeout=w,B.ontimeout=function(){return c("Timed out ("+w+"ms) proxying "+a)}}B.send()})},r}(),uh=/^data:image\/svg\+xml/i,hh=/^data:image\/.*;base64,/i,ch=/^data:image\/.*/i,lh=function(r){return MA.SUPPORT_SVG_DRAWING||!fh(r)},Gs=function(r){return ch.test(r)},Bh=function(r){return hh.test(r)},ks=function(r){return r.substr(0,4)==="blob"},fh=function(r){return r.substr(-3).toLowerCase()==="svg"||uh.test(r)},H=function(){function r(e,s){this.type=0,this.x=e,this.y=s}return r.prototype.add=function(e,s){return new r(this.x+e,this.y+s)},r}(),Mr=function(r,e,s){return new H(r.x+(e.x-r.x)*s,r.y+(e.y-r.y)*s)},xn=function(){function r(e,s,i,a){this.type=1,this.start=e,this.startControl=s,this.endControl=i,this.end=a}return r.prototype.subdivide=function(e,s){var i=Mr(this.start,this.startControl,e),a=Mr(this.startControl,this.endControl,e),h=Mr(this.endControl,this.end,e),c=Mr(i,a,e),f=Mr(a,h,e),B=Mr(c,f,e);return s?new r(this.start,i,c,B):new r(B,f,h,this.end)},r.prototype.add=function(e,s){return new r(this.start.add(e,s),this.startControl.add(e,s),this.endControl.add(e,s),this.end.add(e,s))},r.prototype.reverse=function(){return new r(this.end,this.endControl,this.startControl,this.start)},r}(),ft=function(r){return r.type===1},gh=function(){function r(e){var s=e.styles,i=e.bounds,a=Tt(s.borderTopLeftRadius,i.width,i.height),h=a[0],c=a[1],f=Tt(s.borderTopRightRadius,i.width,i.height),B=f[0],d=f[1],w=Tt(s.borderBottomRightRadius,i.width,i.height),p=w[0],C=w[1],x=Tt(s.borderBottomLeftRadius,i.width,i.height),U=x[0],y=x[1],X=[];X.push((h+B)/i.width),X.push((U+p)/i.width),X.push((c+y)/i.height),X.push((d+C)/i.height);var S=Math.max.apply(Math,X);S>1&&(h/=S,c/=S,B/=S,d/=S,p/=S,C/=S,U/=S,y/=S);var T=i.width-B,eA=i.height-C,W=i.width-p,O=i.height-y,I=s.borderTopWidth,AA=s.borderRightWidth,tA=s.borderBottomWidth,k=s.borderLeftWidth,vA=uA(s.paddingTop,e.bounds.width),At=uA(s.paddingRight,e.bounds.width),ot=uA(s.paddingBottom,e.bounds.width),hA=uA(s.paddingLeft,e.bounds.width);this.topLeftBorderDoubleOuterBox=h>0||c>0?BA(i.left+k/3,i.top+I/3,h-k/3,c-I/3,oA.TOP_LEFT):new H(i.left+k/3,i.top+I/3),this.topRightBorderDoubleOuterBox=h>0||c>0?BA(i.left+T,i.top+I/3,B-AA/3,d-I/3,oA.TOP_RIGHT):new H(i.left+i.width-AA/3,i.top+I/3),this.bottomRightBorderDoubleOuterBox=p>0||C>0?BA(i.left+W,i.top+eA,p-AA/3,C-tA/3,oA.BOTTOM_RIGHT):new H(i.left+i.width-AA/3,i.top+i.height-tA/3),this.bottomLeftBorderDoubleOuterBox=U>0||y>0?BA(i.left+k/3,i.top+O,U-k/3,y-tA/3,oA.BOTTOM_LEFT):new H(i.left+k/3,i.top+i.height-tA/3),this.topLeftBorderDoubleInnerBox=h>0||c>0?BA(i.left+k*2/3,i.top+I*2/3,h-k*2/3,c-I*2/3,oA.TOP_LEFT):new H(i.left+k*2/3,i.top+I*2/3),this.topRightBorderDoubleInnerBox=h>0||c>0?BA(i.left+T,i.top+I*2/3,B-AA*2/3,d-I*2/3,oA.TOP_RIGHT):new H(i.left+i.width-AA*2/3,i.top+I*2/3),this.bottomRightBorderDoubleInnerBox=p>0||C>0?BA(i.left+W,i.top+eA,p-AA*2/3,C-tA*2/3,oA.BOTTOM_RIGHT):new H(i.left+i.width-AA*2/3,i.top+i.height-tA*2/3),this.bottomLeftBorderDoubleInnerBox=U>0||y>0?BA(i.left+k*2/3,i.top+O,U-k*2/3,y-tA*2/3,oA.BOTTOM_LEFT):new H(i.left+k*2/3,i.top+i.height-tA*2/3),this.topLeftBorderStroke=h>0||c>0?BA(i.left+k/2,i.top+I/2,h-k/2,c-I/2,oA.TOP_LEFT):new H(i.left+k/2,i.top+I/2),this.topRightBorderStroke=h>0||c>0?BA(i.left+T,i.top+I/2,B-AA/2,d-I/2,oA.TOP_RIGHT):new H(i.left+i.width-AA/2,i.top+I/2),this.bottomRightBorderStroke=p>0||C>0?BA(i.left+W,i.top+eA,p-AA/2,C-tA/2,oA.BOTTOM_RIGHT):new H(i.left+i.width-AA/2,i.top+i.height-tA/2),this.bottomLeftBorderStroke=U>0||y>0?BA(i.left+k/2,i.top+O,U-k/2,y-tA/2,oA.BOTTOM_LEFT):new H(i.left+k/2,i.top+i.height-tA/2),this.topLeftBorderBox=h>0||c>0?BA(i.left,i.top,h,c,oA.TOP_LEFT):new H(i.left,i.top),this.topRightBorderBox=B>0||d>0?BA(i.left+T,i.top,B,d,oA.TOP_RIGHT):new H(i.left+i.width,i.top),this.bottomRightBorderBox=p>0||C>0?BA(i.left+W,i.top+eA,p,C,oA.BOTTOM_RIGHT):new H(i.left+i.width,i.top+i.height),this.bottomLeftBorderBox=U>0||y>0?BA(i.left,i.top+O,U,y,oA.BOTTOM_LEFT):new H(i.left,i.top+i.height),this.topLeftPaddingBox=h>0||c>0?BA(i.left+k,i.top+I,Math.max(0,h-k),Math.max(0,c-I),oA.TOP_LEFT):new H(i.left+k,i.top+I),this.topRightPaddingBox=B>0||d>0?BA(i.left+Math.min(T,i.width-AA),i.top+I,T>i.width+AA?0:Math.max(0,B-AA),Math.max(0,d-I),oA.TOP_RIGHT):new H(i.left+i.width-AA,i.top+I),this.bottomRightPaddingBox=p>0||C>0?BA(i.left+Math.min(W,i.width-k),i.top+Math.min(eA,i.height-tA),Math.max(0,p-AA),Math.max(0,C-tA),oA.BOTTOM_RIGHT):new H(i.left+i.width-AA,i.top+i.height-tA),this.bottomLeftPaddingBox=U>0||y>0?BA(i.left+k,i.top+Math.min(O,i.height-tA),Math.max(0,U-k),Math.max(0,y-tA),oA.BOTTOM_LEFT):new H(i.left+k,i.top+i.height-tA),this.topLeftContentBox=h>0||c>0?BA(i.left+k+hA,i.top+I+vA,Math.max(0,h-(k+hA)),Math.max(0,c-(I+vA)),oA.TOP_LEFT):new H(i.left+k+hA,i.top+I+vA),this.topRightContentBox=B>0||d>0?BA(i.left+Math.min(T,i.width+k+hA),i.top+I+vA,T>i.width+k+hA?0:B-k+hA,d-(I+vA),oA.TOP_RIGHT):new H(i.left+i.width-(AA+At),i.top+I+vA),this.bottomRightContentBox=p>0||C>0?BA(i.left+Math.min(W,i.width-(k+hA)),i.top+Math.min(eA,i.height+I+vA),Math.max(0,p-(AA+At)),C-(tA+ot),oA.BOTTOM_RIGHT):new H(i.left+i.width-(AA+At),i.top+i.height-(tA+ot)),this.bottomLeftContentBox=U>0||y>0?BA(i.left+k+hA,i.top+O,Math.max(0,U-(k+hA)),y-(tA+ot),oA.BOTTOM_LEFT):new H(i.left+k+hA,i.top+i.height-(tA+ot))}return r}(),oA=function(r){return r[r.TOP_LEFT=0]="TOP_LEFT",r[r.TOP_RIGHT=1]="TOP_RIGHT",r[r.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",r[r.BOTTOM_LEFT=3]="BOTTOM_LEFT",r}(oA||{}),BA=function(r,e,s,i,a){var h=4*((Math.sqrt(2)-1)/3),c=s*h,f=i*h,B=r+s,d=e+i;switch(a){case oA.TOP_LEFT:return new xn(new H(r,d),new H(r,d-f),new H(B-c,e),new H(B,e));case oA.TOP_RIGHT:return new xn(new H(r,e),new H(r+c,e),new H(B,d-f),new H(B,d));case oA.BOTTOM_RIGHT:return new xn(new H(B,e),new H(B,e+f),new H(r+c,d),new H(r,d));case oA.BOTTOM_LEFT:default:return new xn(new H(B,d),new H(B-c,d),new H(r,e+f),new H(r,e))}},bn=function(r){return[r.topLeftBorderBox,r.topRightBorderBox,r.bottomRightBorderBox,r.bottomLeftBorderBox]},dh=function(r){return[r.topLeftContentBox,r.topRightContentBox,r.bottomRightContentBox,r.bottomLeftContentBox]},Tn=function(r){return[r.topLeftPaddingBox,r.topRightPaddingBox,r.bottomRightPaddingBox,r.bottomLeftPaddingBox]},wh=function(){function r(e,s,i){this.offsetX=e,this.offsetY=s,this.matrix=i,this.type=0,this.target=6}return r}(),Kn=function(){function r(e,s){this.path=e,this.target=s,this.type=1}return r}(),ph=function(){function r(e){this.opacity=e,this.type=2,this.target=6}return r}(),Qh=function(r){return r.type===0},Go=function(r){return r.type===1},mh=function(r){return r.type===2},ko=function(r,e){return r.length===e.length?r.some(function(s,i){return s===e[i]}):!1},Ch=function(r,e,s,i,a){return r.map(function(h,c){switch(c){case 0:return h.add(e,s);case 1:return h.add(e+i,s);case 2:return h.add(e+i,s+a);case 3:return h.add(e,s+a)}return h})},Vo=function(){function r(e){this.element=e,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return r}(),Zo=function(){function r(e,s){if(this.container=e,this.parent=s,this.effects=[],this.curves=new gh(this.container),this.container.styles.opacity<1&&this.effects.push(new ph(this.container.styles.opacity)),this.container.styles.transform!==null){var i=this.container.bounds.left+this.container.styles.transformOrigin[0].number,a=this.container.bounds.top+this.container.styles.transformOrigin[1].number,h=this.container.styles.transform;this.effects.push(new wh(i,a,h))}if(this.container.styles.overflowX!==0){var c=bn(this.curves),f=Tn(this.curves);ko(c,f)?this.effects.push(new Kn(c,6)):(this.effects.push(new Kn(c,2)),this.effects.push(new Kn(f,4)))}}return r.prototype.getEffects=function(e){for(var s=[2,3].indexOf(this.container.styles.position)===-1,i=this.parent,a=this.effects.slice(0);i;){var h=i.effects.filter(function(B){return!Go(B)});if(s||i.container.styles.position!==0||!i.parent){if(a.unshift.apply(a,h),s=[2,3].indexOf(i.container.styles.position)===-1,i.container.styles.overflowX!==0){var c=bn(i.curves),f=Tn(i.curves);ko(c,f)||a.unshift(new Kn(f,6))}}else a.unshift.apply(a,h);i=i.parent}return a.filter(function(B){return IA(B.target,e)})},r}(),Vs=function(r,e,s,i){r.container.elements.forEach(function(a){var h=IA(a.flags,4),c=IA(a.flags,2),f=new Zo(a,r);IA(a.styles.display,2048)&&i.push(f);var B=IA(a.flags,8)?[]:i;if(h||c){var d=h||a.styles.isPositioned()?s:e,w=new Vo(f);if(a.styles.isPositioned()||a.styles.opacity<1||a.styles.isTransformed()){var p=a.styles.zIndex.order;if(p<0){var C=0;d.negativeZIndex.some(function(U,y){return p>U.element.container.styles.zIndex.order?(C=y,!1):C>0}),d.negativeZIndex.splice(C,0,w)}else if(p>0){var x=0;d.positiveZIndex.some(function(U,y){return p>=U.element.container.styles.zIndex.order?(x=y+1,!1):x>0}),d.positiveZIndex.splice(x,0,w)}else d.zeroOrAutoZIndexOrTransformedOrOpacity.push(w)}else a.styles.isFloating()?d.nonPositionedFloats.push(w):d.nonPositionedInlineLevel.push(w);Vs(f,w,h?w:s,B)}else a.styles.isInlineLevel()?e.inlineLevel.push(f):e.nonInlineLevel.push(f),Vs(f,e,s,B);IA(a.flags,8)&&Xo(a,B)})},Xo=function(r,e){for(var s=r instanceof Ts?r.start:1,i=r instanceof Ts?r.reversed:!1,a=0;a<e.length;a++){var h=e[a];h.container instanceof wo&&typeof h.container.value=="number"&&h.container.value!==0&&(s=h.container.value),h.listValue=Fi(s,h.container.styles.listStyleType,!0),s+=i?-1:1}},_h=function(r){var e=new Zo(r,null),s=new Vo(e),i=[];return Vs(e,s,s,i),Xo(e.container,i),s},zo=function(r,e){switch(e){case 0:return gt(r.topLeftBorderBox,r.topLeftPaddingBox,r.topRightBorderBox,r.topRightPaddingBox);case 1:return gt(r.topRightBorderBox,r.topRightPaddingBox,r.bottomRightBorderBox,r.bottomRightPaddingBox);case 2:return gt(r.bottomRightBorderBox,r.bottomRightPaddingBox,r.bottomLeftBorderBox,r.bottomLeftPaddingBox);case 3:default:return gt(r.bottomLeftBorderBox,r.bottomLeftPaddingBox,r.topLeftBorderBox,r.topLeftPaddingBox)}},vh=function(r,e){switch(e){case 0:return gt(r.topLeftBorderBox,r.topLeftBorderDoubleOuterBox,r.topRightBorderBox,r.topRightBorderDoubleOuterBox);case 1:return gt(r.topRightBorderBox,r.topRightBorderDoubleOuterBox,r.bottomRightBorderBox,r.bottomRightBorderDoubleOuterBox);case 2:return gt(r.bottomRightBorderBox,r.bottomRightBorderDoubleOuterBox,r.bottomLeftBorderBox,r.bottomLeftBorderDoubleOuterBox);case 3:default:return gt(r.bottomLeftBorderBox,r.bottomLeftBorderDoubleOuterBox,r.topLeftBorderBox,r.topLeftBorderDoubleOuterBox)}},Uh=function(r,e){switch(e){case 0:return gt(r.topLeftBorderDoubleInnerBox,r.topLeftPaddingBox,r.topRightBorderDoubleInnerBox,r.topRightPaddingBox);case 1:return gt(r.topRightBorderDoubleInnerBox,r.topRightPaddingBox,r.bottomRightBorderDoubleInnerBox,r.bottomRightPaddingBox);case 2:return gt(r.bottomRightBorderDoubleInnerBox,r.bottomRightPaddingBox,r.bottomLeftBorderDoubleInnerBox,r.bottomLeftPaddingBox);case 3:default:return gt(r.bottomLeftBorderDoubleInnerBox,r.bottomLeftPaddingBox,r.topLeftBorderDoubleInnerBox,r.topLeftPaddingBox)}},Fh=function(r,e){switch(e){case 0:return Sn(r.topLeftBorderStroke,r.topRightBorderStroke);case 1:return Sn(r.topRightBorderStroke,r.bottomRightBorderStroke);case 2:return Sn(r.bottomRightBorderStroke,r.bottomLeftBorderStroke);case 3:default:return Sn(r.bottomLeftBorderStroke,r.topLeftBorderStroke)}},Sn=function(r,e){var s=[];return ft(r)?s.push(r.subdivide(.5,!1)):s.push(r),ft(e)?s.push(e.subdivide(.5,!0)):s.push(e),s},gt=function(r,e,s,i){var a=[];return ft(r)?a.push(r.subdivide(.5,!1)):a.push(r),ft(s)?a.push(s.subdivide(.5,!0)):a.push(s),ft(i)?a.push(i.subdivide(.5,!0).reverse()):a.push(i),ft(e)?a.push(e.subdivide(.5,!1).reverse()):a.push(e),a},Wo=function(r){var e=r.bounds,s=r.styles;return e.add(s.borderLeftWidth,s.borderTopWidth,-(s.borderRightWidth+s.borderLeftWidth),-(s.borderTopWidth+s.borderBottomWidth))},Mn=function(r){var e=r.styles,s=r.bounds,i=uA(e.paddingLeft,s.width),a=uA(e.paddingRight,s.width),h=uA(e.paddingTop,s.width),c=uA(e.paddingBottom,s.width);return s.add(i+e.borderLeftWidth,h+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+i+a),-(e.borderTopWidth+e.borderBottomWidth+h+c))},yh=function(r,e){return r===0?e.bounds:r===2?Mn(e):Wo(e)},Eh=function(r,e){return r===0?e.bounds:r===2?Mn(e):Wo(e)},Zs=function(r,e,s){var i=yh(Pr(r.styles.backgroundOrigin,e),r),a=Eh(Pr(r.styles.backgroundClip,e),r),h=Hh(Pr(r.styles.backgroundSize,e),s,i),c=h[0],f=h[1],B=Tt(Pr(r.styles.backgroundPosition,e),i.width-c,i.height-f),d=Ih(Pr(r.styles.backgroundRepeat,e),B,h,i,a),w=Math.round(i.left+B[0]),p=Math.round(i.top+B[1]);return[d,w,p,c,f]},Dr=function(r){return nA(r)&&r.value===Qe.AUTO},Dn=function(r){return typeof r=="number"},Hh=function(r,e,s){var i=e[0],a=e[1],h=e[2],c=r[0],f=r[1];if(!c)return[0,0];if(QA(c)&&f&&QA(f))return[uA(c,s.width),uA(f,s.height)];var B=Dn(h);if(nA(c)&&(c.value===Qe.CONTAIN||c.value===Qe.COVER)){if(Dn(h)){var d=s.width/s.height;return d<h!=(c.value===Qe.COVER)?[s.width,s.width/h]:[s.height*h,s.height]}return[s.width,s.height]}var w=Dn(i),p=Dn(a),C=w||p;if(Dr(c)&&(!f||Dr(f))){if(w&&p)return[i,a];if(!B&&!C)return[s.width,s.height];if(C&&B){var x=w?i:a*h,U=p?a:i/h;return[x,U]}var y=w?i:s.width,X=p?a:s.height;return[y,X]}if(B){var S=0,T=0;return QA(c)?S=uA(c,s.width):QA(f)&&(T=uA(f,s.height)),Dr(c)?S=T*h:(!f||Dr(f))&&(T=S/h),[S,T]}var eA=null,W=null;if(QA(c)?eA=uA(c,s.width):f&&QA(f)&&(W=uA(f,s.height)),eA!==null&&(!f||Dr(f))&&(W=w&&p?eA/i*a:s.height),W!==null&&Dr(c)&&(eA=w&&p?W/a*i:s.width),eA!==null&&W!==null)return[eA,W];throw new Error("Unable to calculate background-size for element")},Pr=function(r,e){var s=r[e];return typeof s>"u"?r[0]:s},Ih=function(r,e,s,i,a){var h=e[0],c=e[1],f=s[0],B=s[1];switch(r){case 2:return[new H(Math.round(i.left),Math.round(i.top+c)),new H(Math.round(i.left+i.width),Math.round(i.top+c)),new H(Math.round(i.left+i.width),Math.round(B+i.top+c)),new H(Math.round(i.left),Math.round(B+i.top+c))];case 3:return[new H(Math.round(i.left+h),Math.round(i.top)),new H(Math.round(i.left+h+f),Math.round(i.top)),new H(Math.round(i.left+h+f),Math.round(i.height+i.top)),new H(Math.round(i.left+h),Math.round(i.height+i.top))];case 1:return[new H(Math.round(i.left+h),Math.round(i.top+c)),new H(Math.round(i.left+h+f),Math.round(i.top+c)),new H(Math.round(i.left+h+f),Math.round(i.top+c+B)),new H(Math.round(i.left+h),Math.round(i.top+c+B))];default:return[new H(Math.round(a.left),Math.round(a.top)),new H(Math.round(a.left+a.width),Math.round(a.top)),new H(Math.round(a.left+a.width),Math.round(a.height+a.top)),new H(Math.round(a.left),Math.round(a.height+a.top))]}},Lh="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Jo="Hidden Text",xh=function(){function r(e){this._data={},this._document=e}return r.prototype.parseMetrics=function(e,s){var i=this._document.createElement("div"),a=this._document.createElement("img"),h=this._document.createElement("span"),c=this._document.body;i.style.visibility="hidden",i.style.fontFamily=e,i.style.fontSize=s,i.style.margin="0",i.style.padding="0",i.style.whiteSpace="nowrap",c.appendChild(i),a.src=Lh,a.width=1,a.height=1,a.style.margin="0",a.style.padding="0",a.style.verticalAlign="baseline",h.style.fontFamily=e,h.style.fontSize=s,h.style.margin="0",h.style.padding="0",h.appendChild(this._document.createTextNode(Jo)),i.appendChild(h),i.appendChild(a);var f=a.offsetTop-h.offsetTop+2;i.removeChild(h),i.appendChild(this._document.createTextNode(Jo)),i.style.lineHeight="normal",a.style.verticalAlign="super";var B=a.offsetTop-i.offsetTop+2;return c.removeChild(i),{baseline:f,middle:B}},r.prototype.getMetrics=function(e,s){var i=e+" "+s;return typeof this._data[i]>"u"&&(this._data[i]=this.parseMetrics(e,s)),this._data[i]},r}(),Yo=function(){function r(e,s){this.context=e,this.options=s}return r}(),bh=1e4,Th=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a._activeEffects=[],a.canvas=i.canvas?i.canvas:document.createElement("canvas"),a.ctx=a.canvas.getContext("2d"),i.canvas||(a.canvas.width=Math.floor(i.width*i.scale),a.canvas.height=Math.floor(i.height*i.scale),a.canvas.style.width=i.width+"px",a.canvas.style.height=i.height+"px"),a.fontMetrics=new xh(document),a.ctx.scale(a.options.scale,a.options.scale),a.ctx.translate(-i.x,-i.y),a.ctx.textBaseline="bottom",a._activeEffects=[],a.context.logger.debug("Canvas renderer initialized ("+i.width+"x"+i.height+") with scale "+i.scale),a}return e.prototype.applyEffects=function(s){for(var i=this;this._activeEffects.length;)this.popEffect();s.forEach(function(a){return i.applyEffect(a)})},e.prototype.applyEffect=function(s){this.ctx.save(),mh(s)&&(this.ctx.globalAlpha=s.opacity),Qh(s)&&(this.ctx.translate(s.offsetX,s.offsetY),this.ctx.transform(s.matrix[0],s.matrix[1],s.matrix[2],s.matrix[3],s.matrix[4],s.matrix[5]),this.ctx.translate(-s.offsetX,-s.offsetY)),Go(s)&&(this.path(s.path),this.ctx.clip()),this._activeEffects.push(s)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(s){return j(this,void 0,void 0,function(){var i;return M(this,function(a){switch(a.label){case 0:return i=s.element.container.styles,i.isVisible()?[4,this.renderStackContent(s)]:[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}})})},e.prototype.renderNode=function(s){return j(this,void 0,void 0,function(){return M(this,function(i){switch(i.label){case 0:if(IA(s.container.flags,16))debugger;return s.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(s)]:[3,3];case 1:return i.sent(),[4,this.renderNodeContent(s)];case 2:i.sent(),i.label=3;case 3:return[2]}})})},e.prototype.renderTextWithLetterSpacing=function(s,i,a){var h=this;if(i===0)this.ctx.fillText(s.text,s.bounds.left,s.bounds.top+a);else{var c=bs(s.text);c.reduce(function(f,B){return h.ctx.fillText(B,f,s.bounds.top+a),f+h.ctx.measureText(B).width},s.bounds.left)}},e.prototype.createFontStyle=function(s){var i=s.fontVariant.filter(function(c){return c==="normal"||c==="small-caps"}).join(""),a=Ph(s.fontFamily).join(", "),h=Wt(s.fontSize)?""+s.fontSize.number+s.fontSize.unit:s.fontSize.number+"px";return[[s.fontStyle,i,s.fontWeight,h,a].join(" "),a,h]},e.prototype.renderTextNode=function(s,i){return j(this,void 0,void 0,function(){var a,h,c,f,B,d,w,p,C=this;return M(this,function(x){return a=this.createFontStyle(i),h=a[0],c=a[1],f=a[2],this.ctx.font=h,this.ctx.direction=i.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",B=this.fontMetrics.getMetrics(c,f),d=B.baseline,w=B.middle,p=i.paintOrder,s.textBounds.forEach(function(U){p.forEach(function(y){switch(y){case 0:C.ctx.fillStyle=gA(i.color),C.renderTextWithLetterSpacing(U,i.letterSpacing,d);var X=i.textShadow;X.length&&U.text.trim().length&&(X.slice(0).reverse().forEach(function(S){C.ctx.shadowColor=gA(S.color),C.ctx.shadowOffsetX=S.offsetX.number*C.options.scale,C.ctx.shadowOffsetY=S.offsetY.number*C.options.scale,C.ctx.shadowBlur=S.blur.number,C.renderTextWithLetterSpacing(U,i.letterSpacing,d)}),C.ctx.shadowColor="",C.ctx.shadowOffsetX=0,C.ctx.shadowOffsetY=0,C.ctx.shadowBlur=0),i.textDecorationLine.length&&(C.ctx.fillStyle=gA(i.textDecorationColor||i.color),i.textDecorationLine.forEach(function(S){switch(S){case 1:C.ctx.fillRect(U.bounds.left,Math.round(U.bounds.top+d),U.bounds.width,1);break;case 2:C.ctx.fillRect(U.bounds.left,Math.round(U.bounds.top),U.bounds.width,1);break;case 3:C.ctx.fillRect(U.bounds.left,Math.ceil(U.bounds.top+w),U.bounds.width,1);break}}));break;case 1:i.webkitTextStrokeWidth&&U.text.trim().length&&(C.ctx.strokeStyle=gA(i.webkitTextStrokeColor),C.ctx.lineWidth=i.webkitTextStrokeWidth,C.ctx.lineJoin=window.chrome?"miter":"round",C.ctx.strokeText(U.text,U.bounds.left,U.bounds.top+d)),C.ctx.strokeStyle="",C.ctx.lineWidth=0,C.ctx.lineJoin="miter";break}})}),[2]})})},e.prototype.renderReplacedElement=function(s,i,a){if(a&&s.intrinsicWidth>0&&s.intrinsicHeight>0){var h=Mn(s),c=Tn(i);this.path(c),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(a,0,0,s.intrinsicWidth,s.intrinsicHeight,h.left,h.top,h.width,h.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(s){return j(this,void 0,void 0,function(){var i,a,h,c,f,B,T,T,d,w,p,C,W,x,U,O,y,X,S,T,eA,W,O;return M(this,function(I){switch(I.label){case 0:this.applyEffects(s.getEffects(4)),i=s.container,a=s.curves,h=i.styles,c=0,f=i.textNodes,I.label=1;case 1:return c<f.length?(B=f[c],[4,this.renderTextNode(B,h)]):[3,4];case 2:I.sent(),I.label=3;case 3:return c++,[3,1];case 4:if(!(i instanceof Bo))return[3,8];I.label=5;case 5:return I.trys.push([5,7,,8]),[4,this.context.cache.match(i.src)];case 6:return T=I.sent(),this.renderReplacedElement(i,a,T),[3,8];case 7:return I.sent(),this.context.logger.error("Error loading image "+i.src),[3,8];case 8:if(i instanceof fo&&this.renderReplacedElement(i,a,i.canvas),!(i instanceof go))return[3,12];I.label=9;case 9:return I.trys.push([9,11,,12]),[4,this.context.cache.match(i.svg)];case 10:return T=I.sent(),this.renderReplacedElement(i,a,T),[3,12];case 11:return I.sent(),this.context.logger.error("Error loading svg "+i.svg.substring(0,255)),[3,12];case 12:return i instanceof Co&&i.tree?(d=new e(this.context,{scale:this.options.scale,backgroundColor:i.backgroundColor,x:0,y:0,width:i.width,height:i.height}),[4,d.render(i.tree)]):[3,14];case 13:w=I.sent(),i.width&&i.height&&this.ctx.drawImage(w,0,0,i.width,i.height,i.bounds.left,i.bounds.top,i.bounds.width,i.bounds.height),I.label=14;case 14:if(i instanceof Ks&&(p=Math.min(i.bounds.width,i.bounds.height),i.type===Un?i.checked&&(this.ctx.save(),this.path([new H(i.bounds.left+p*.39363,i.bounds.top+p*.79),new H(i.bounds.left+p*.16,i.bounds.top+p*.5549),new H(i.bounds.left+p*.27347,i.bounds.top+p*.44071),new H(i.bounds.left+p*.39694,i.bounds.top+p*.5649),new H(i.bounds.left+p*.72983,i.bounds.top+p*.23),new H(i.bounds.left+p*.84,i.bounds.top+p*.34085),new H(i.bounds.left+p*.39363,i.bounds.top+p*.79)]),this.ctx.fillStyle=gA(po),this.ctx.fill(),this.ctx.restore()):i.type===Fn&&i.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(i.bounds.left+p/2,i.bounds.top+p/2,p/4,0,Math.PI*2,!0),this.ctx.fillStyle=gA(po),this.ctx.fill(),this.ctx.restore())),Kh(i)&&i.value.length){switch(C=this.createFontStyle(h),W=C[0],x=C[1],U=this.fontMetrics.getMetrics(W,x).baseline,this.ctx.font=W,this.ctx.fillStyle=gA(h.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Mh(i.styles.textAlign),O=Mn(i),y=0,i.styles.textAlign){case 1:y+=O.width/2;break;case 2:y+=O.width;break}X=O.add(y,0,0,-O.height/2+1),this.ctx.save(),this.path([new H(O.left,O.top),new H(O.left+O.width,O.top),new H(O.left+O.width,O.top+O.height),new H(O.left,O.top+O.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new vi(i.value,X),h.letterSpacing,U),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!IA(i.styles.display,2048))return[3,20];if(i.styles.listStyleImage===null)return[3,19];if(S=i.styles.listStyleImage,S.type!==0)return[3,18];T=void 0,eA=S.url,I.label=15;case 15:return I.trys.push([15,17,,18]),[4,this.context.cache.match(eA)];case 16:return T=I.sent(),this.ctx.drawImage(T,i.bounds.left-(T.width+10),i.bounds.top),[3,18];case 17:return I.sent(),this.context.logger.error("Error loading list-style-image "+eA),[3,18];case 18:return[3,20];case 19:s.listValue&&i.styles.listStyleType!==-1&&(W=this.createFontStyle(h)[0],this.ctx.font=W,this.ctx.fillStyle=gA(h.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",O=new R(i.bounds.left,i.bounds.top+uA(i.styles.paddingTop,i.bounds.width),i.bounds.width,dA(h.lineHeight,h.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new vi(s.listValue,O),h.letterSpacing,dA(h.lineHeight,h.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),I.label=20;case 20:return[2]}})})},e.prototype.renderStackContent=function(s){return j(this,void 0,void 0,function(){var i,a,S,h,c,S,f,B,S,d,w,S,p,C,S,x,U,S,y,X,S;return M(this,function(T){switch(T.label){case 0:if(IA(s.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(s.element)];case 1:T.sent(),i=0,a=s.negativeZIndex,T.label=2;case 2:return i<a.length?(S=a[i],[4,this.renderStack(S)]):[3,5];case 3:T.sent(),T.label=4;case 4:return i++,[3,2];case 5:return[4,this.renderNodeContent(s.element)];case 6:T.sent(),h=0,c=s.nonInlineLevel,T.label=7;case 7:return h<c.length?(S=c[h],[4,this.renderNode(S)]):[3,10];case 8:T.sent(),T.label=9;case 9:return h++,[3,7];case 10:f=0,B=s.nonPositionedFloats,T.label=11;case 11:return f<B.length?(S=B[f],[4,this.renderStack(S)]):[3,14];case 12:T.sent(),T.label=13;case 13:return f++,[3,11];case 14:d=0,w=s.nonPositionedInlineLevel,T.label=15;case 15:return d<w.length?(S=w[d],[4,this.renderStack(S)]):[3,18];case 16:T.sent(),T.label=17;case 17:return d++,[3,15];case 18:p=0,C=s.inlineLevel,T.label=19;case 19:return p<C.length?(S=C[p],[4,this.renderNode(S)]):[3,22];case 20:T.sent(),T.label=21;case 21:return p++,[3,19];case 22:x=0,U=s.zeroOrAutoZIndexOrTransformedOrOpacity,T.label=23;case 23:return x<U.length?(S=U[x],[4,this.renderStack(S)]):[3,26];case 24:T.sent(),T.label=25;case 25:return x++,[3,23];case 26:y=0,X=s.positiveZIndex,T.label=27;case 27:return y<X.length?(S=X[y],[4,this.renderStack(S)]):[3,30];case 28:T.sent(),T.label=29;case 29:return y++,[3,27];case 30:return[2]}})})},e.prototype.mask=function(s){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(s.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(s){this.ctx.beginPath(),this.formatPath(s),this.ctx.closePath()},e.prototype.formatPath=function(s){var i=this;s.forEach(function(a,h){var c=ft(a)?a.start:a;h===0?i.ctx.moveTo(c.x,c.y):i.ctx.lineTo(c.x,c.y),ft(a)&&i.ctx.bezierCurveTo(a.startControl.x,a.startControl.y,a.endControl.x,a.endControl.y,a.end.x,a.end.y)})},e.prototype.renderRepeat=function(s,i,a,h){this.path(s),this.ctx.fillStyle=i,this.ctx.translate(a,h),this.ctx.fill(),this.ctx.translate(-a,-h)},e.prototype.resizeImage=function(s,i,a){var h;if(s.width===i&&s.height===a)return s;var c=(h=this.canvas.ownerDocument)!==null&&h!==void 0?h:document,f=c.createElement("canvas");f.width=Math.max(1,i),f.height=Math.max(1,a);var B=f.getContext("2d");return B.drawImage(s,0,0,s.width,s.height,0,0,i,a),f},e.prototype.renderBackgroundImage=function(s){return j(this,void 0,void 0,function(){var i,a,h,c,f,B;return M(this,function(d){switch(d.label){case 0:i=s.styles.backgroundImage.length-1,a=function(w){var p,C,x,vA,tt,et,hA,DA,tA,U,vA,tt,et,hA,DA,y,X,S,T,eA,W,O,I,AA,tA,k,vA,At,ot,hA,DA,ve,tt,et,Je,Dt,Ue,Ye,qe,te,je,ee;return M(this,function(Or){switch(Or.label){case 0:if(w.type!==0)return[3,5];p=void 0,C=w.url,Or.label=1;case 1:return Or.trys.push([1,3,,4]),[4,h.context.cache.match(C)];case 2:return p=Or.sent(),[3,4];case 3:return Or.sent(),h.context.logger.error("Error loading background-image "+C),[3,4];case 4:return p&&(x=Zs(s,i,[p.width,p.height,p.width/p.height]),vA=x[0],tt=x[1],et=x[2],hA=x[3],DA=x[4],tA=h.ctx.createPattern(h.resizeImage(p,hA,DA),"repeat"),h.renderRepeat(vA,tA,tt,et)),[3,6];case 5:un(w)?(U=Zs(s,i,[null,null,null]),vA=U[0],tt=U[1],et=U[2],hA=U[3],DA=U[4],y=us(w.angle,hA,DA),X=y[0],S=y[1],T=y[2],eA=y[3],W=y[4],O=document.createElement("canvas"),O.width=hA,O.height=DA,I=O.getContext("2d"),AA=I.createLinearGradient(S,eA,T,W),en(w.stops,X).forEach(function(yi){return AA.addColorStop(yi.stop,gA(yi.color))}),I.fillStyle=AA,I.fillRect(0,0,hA,DA),hA>0&&DA>0&&(tA=h.ctx.createPattern(O,"repeat"),h.renderRepeat(vA,tA,tt,et))):hn(w)&&(k=Zs(s,i,[null,null,null]),vA=k[0],At=k[1],ot=k[2],hA=k[3],DA=k[4],ve=w.position.length===0?[Yt]:w.position,tt=uA(ve[0],hA),et=uA(ve[ve.length-1],DA),Je=lt(w,tt,et,hA,DA),Dt=Je[0],Ue=Je[1],Dt>0&&Ue>0&&(Ye=h.ctx.createRadialGradient(At+tt,ot+et,0,At+tt,ot+et,Dt),en(w.stops,Dt*2).forEach(function(yi){return Ye.addColorStop(yi.stop,gA(yi.color))}),h.path(vA),h.ctx.fillStyle=Ye,Dt!==Ue?(qe=s.bounds.left+.5*s.bounds.width,te=s.bounds.top+.5*s.bounds.height,je=Ue/Dt,ee=1/je,h.ctx.save(),h.ctx.translate(qe,te),h.ctx.transform(1,0,0,je,0,0),h.ctx.translate(-qe,-te),h.ctx.fillRect(At,ee*(ot-te)+te,hA,DA*ee),h.ctx.restore()):h.ctx.fill())),Or.label=6;case 6:return i--,[2]}})},h=this,c=0,f=s.styles.backgroundImage.slice(0).reverse(),d.label=1;case 1:return c<f.length?(B=f[c],[5,a(B)]):[3,4];case 2:d.sent(),d.label=3;case 3:return c++,[3,1];case 4:return[2]}})})},e.prototype.renderSolidBorder=function(s,i,a){return j(this,void 0,void 0,function(){return M(this,function(h){return this.path(zo(a,i)),this.ctx.fillStyle=gA(s),this.ctx.fill(),[2]})})},e.prototype.renderDoubleBorder=function(s,i,a,h){return j(this,void 0,void 0,function(){var c,f;return M(this,function(B){switch(B.label){case 0:return i<3?[4,this.renderSolidBorder(s,a,h)]:[3,2];case 1:return B.sent(),[2];case 2:return c=vh(h,a),this.path(c),this.ctx.fillStyle=gA(s),this.ctx.fill(),f=Uh(h,a),this.path(f),this.ctx.fill(),[2]}})})},e.prototype.renderNodeBackgroundAndBorders=function(s){return j(this,void 0,void 0,function(){var i,a,h,c,f,B,d,w,p=this;return M(this,function(C){switch(C.label){case 0:return this.applyEffects(s.getEffects(2)),i=s.container.styles,a=!TA(i.backgroundColor)||i.backgroundImage.length,h=[{style:i.borderTopStyle,color:i.borderTopColor,width:i.borderTopWidth},{style:i.borderRightStyle,color:i.borderRightColor,width:i.borderRightWidth},{style:i.borderBottomStyle,color:i.borderBottomColor,width:i.borderBottomWidth},{style:i.borderLeftStyle,color:i.borderLeftColor,width:i.borderLeftWidth}],c=Sh(Pr(i.backgroundClip,0),s.curves),a||i.boxShadow.length?(this.ctx.save(),this.path(c),this.ctx.clip(),TA(i.backgroundColor)||(this.ctx.fillStyle=gA(i.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(s.container)]):[3,2];case 1:C.sent(),this.ctx.restore(),i.boxShadow.slice(0).reverse().forEach(function(x){p.ctx.save();var U=bn(s.curves),y=x.inset?0:bh,X=Ch(U,-y+(x.inset?1:-1)*x.spread.number,(x.inset?1:-1)*x.spread.number,x.spread.number*(x.inset?-2:2),x.spread.number*(x.inset?-2:2));x.inset?(p.path(U),p.ctx.clip(),p.mask(X)):(p.mask(U),p.ctx.clip(),p.path(X)),p.ctx.shadowOffsetX=x.offsetX.number+y,p.ctx.shadowOffsetY=x.offsetY.number,p.ctx.shadowColor=gA(x.color),p.ctx.shadowBlur=x.blur.number,p.ctx.fillStyle=x.inset?gA(x.color):"rgba(0,0,0,1)",p.ctx.fill(),p.ctx.restore()}),C.label=2;case 2:f=0,B=0,d=h,C.label=3;case 3:return B<d.length?(w=d[B],w.style!==0&&!TA(w.color)&&w.width>0?w.style!==2?[3,5]:[4,this.renderDashedDottedBorder(w.color,w.width,f,s.curves,2)]:[3,11]):[3,13];case 4:return C.sent(),[3,11];case 5:return w.style!==3?[3,7]:[4,this.renderDashedDottedBorder(w.color,w.width,f,s.curves,3)];case 6:return C.sent(),[3,11];case 7:return w.style!==4?[3,9]:[4,this.renderDoubleBorder(w.color,w.width,f,s.curves)];case 8:return C.sent(),[3,11];case 9:return[4,this.renderSolidBorder(w.color,f,s.curves)];case 10:C.sent(),C.label=11;case 11:f++,C.label=12;case 12:return B++,[3,3];case 13:return[2]}})})},e.prototype.renderDashedDottedBorder=function(s,i,a,h,c){return j(this,void 0,void 0,function(){var f,B,d,w,p,C,x,U,y,X,S,T,eA,W,O,I,O,I;return M(this,function(AA){return this.ctx.save(),f=Fh(h,a),B=zo(h,a),c===2&&(this.path(B),this.ctx.clip()),ft(B[0])?(d=B[0].start.x,w=B[0].start.y):(d=B[0].x,w=B[0].y),ft(B[1])?(p=B[1].end.x,C=B[1].end.y):(p=B[1].x,C=B[1].y),a===0||a===2?x=Math.abs(d-p):x=Math.abs(w-C),this.ctx.beginPath(),c===3?this.formatPath(f):this.formatPath(B.slice(0,2)),U=i<3?i*3:i*2,y=i<3?i*2:i,c===3&&(U=i,y=i),X=!0,x<=U*2?X=!1:x<=U*2+y?(S=x/(2*U+y),U*=S,y*=S):(T=Math.floor((x+y)/(U+y)),eA=(x-T*U)/(T-1),W=(x-(T+1)*U)/T,y=W<=0||Math.abs(y-eA)<Math.abs(y-W)?eA:W),X&&(c===3?this.ctx.setLineDash([0,U+y]):this.ctx.setLineDash([U,y])),c===3?(this.ctx.lineCap="round",this.ctx.lineWidth=i):this.ctx.lineWidth=i*2+1.1,this.ctx.strokeStyle=gA(s),this.ctx.stroke(),this.ctx.setLineDash([]),c===2&&(ft(B[0])&&(O=B[3],I=B[0],this.ctx.beginPath(),this.formatPath([new H(O.end.x,O.end.y),new H(I.start.x,I.start.y)]),this.ctx.stroke()),ft(B[1])&&(O=B[1],I=B[2],this.ctx.beginPath(),this.formatPath([new H(O.end.x,O.end.y),new H(I.start.x,I.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},e.prototype.render=function(s){return j(this,void 0,void 0,function(){var i;return M(this,function(a){switch(a.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=gA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),i=_h(s),[4,this.renderStack(i)];case 1:return a.sent(),this.applyEffects([]),[2,this.canvas]}})})},e}(Yo),Kh=function(r){return r instanceof mo||r instanceof Qo?!0:r instanceof Ks&&r.type!==Fn&&r.type!==Un},Sh=function(r,e){switch(r){case 0:return bn(e);case 2:return dh(e);case 1:default:return Tn(e)}},Mh=function(r){switch(r){case 1:return"center";case 2:return"right";case 0:default:return"left"}},Dh=["-apple-system","system-ui"],Ph=function(r){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?r.filter(function(e){return Dh.indexOf(e)===-1}):r},Oh=function(r){mA(e,r);function e(s,i){var a=r.call(this,s,i)||this;return a.canvas=i.canvas?i.canvas:document.createElement("canvas"),a.ctx=a.canvas.getContext("2d"),a.options=i,a.canvas.width=Math.floor(i.width*i.scale),a.canvas.height=Math.floor(i.height*i.scale),a.canvas.style.width=i.width+"px",a.canvas.style.height=i.height+"px",a.ctx.scale(a.options.scale,a.options.scale),a.ctx.translate(-i.x,-i.y),a.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+i.width+"x"+i.height+" at "+i.x+","+i.y+") with scale "+i.scale),a}return e.prototype.render=function(s){return j(this,void 0,void 0,function(){var i,a;return M(this,function(h){switch(h.label){case 0:return i=xs(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,s),[4,Rh(i)];case 1:return a=h.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=gA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(a,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},e}(Yo),Rh=function(r){return new Promise(function(e,s){var i=new Image;i.onload=function(){e(i)},i.onerror=s,i.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(r))})},Nh=function(){function r(e){var s=e.id,i=e.enabled;this.id=s,this.enabled=i,this.start=Date.now()}return r.prototype.debug=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,XA([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.prototype.getTime=function(){return Date.now()-this.start},r.prototype.info=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,XA([this.id,this.getTime()+"ms"],e))},r.prototype.warn=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,XA([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.prototype.error=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,XA([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.instances={},r}(),Gh=function(){function r(e,s){var i;this.windowBounds=s,this.instanceName="#"+r.instanceCount++,this.logger=new Nh({id:this.instanceName,enabled:e.logging}),this.cache=(i=e.cache)!==null&&i!==void 0?i:new ah(this,e)}return r.instanceCount=1,r}(),kh=function(r,e){return e===void 0&&(e={}),Vh(r,e)};typeof window<"u"&&No.setContext(window);var Vh=function(r,e){return j(void 0,void 0,void 0,function(){var s,i,a,h,c,f,B,d,w,p,C,x,U,y,X,S,T,eA,W,O,AA,I,AA,tA,k,vA,At,ot,hA,DA,ve,tt,et,Je,Dt,Ue,Ye,qe,te,je;return M(this,function(ee){switch(ee.label){case 0:if(!r||typeof r!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(s=r.ownerDocument,!s)throw new Error("Element is not attached to a Document");if(i=s.defaultView,!i)throw new Error("Document is not attached to a Window");return a={allowTaint:(tA=e.allowTaint)!==null&&tA!==void 0?tA:!1,imageTimeout:(k=e.imageTimeout)!==null&&k!==void 0?k:15e3,proxy:e.proxy,useCORS:(vA=e.useCORS)!==null&&vA!==void 0?vA:!1},h=E({logging:(At=e.logging)!==null&&At!==void 0?At:!0,cache:e.cache},a),c={windowWidth:(ot=e.windowWidth)!==null&&ot!==void 0?ot:i.innerWidth,windowHeight:(hA=e.windowHeight)!==null&&hA!==void 0?hA:i.innerHeight,scrollX:(DA=e.scrollX)!==null&&DA!==void 0?DA:i.pageXOffset,scrollY:(ve=e.scrollY)!==null&&ve!==void 0?ve:i.pageYOffset},f=new R(c.scrollX,c.scrollY,c.windowWidth,c.windowHeight),B=new Gh(h,f),d=(tt=e.foreignObjectRendering)!==null&&tt!==void 0?tt:!1,w={allowTaint:(et=e.allowTaint)!==null&&et!==void 0?et:!1,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:d,copyStyles:d},B.logger.debug("Starting document clone with size "+f.width+"x"+f.height+" scrolled to "+-f.left+","+-f.top),p=new Oo(B,r,w),C=p.clonedReferenceElement,C?[4,p.toIFrame(s,f)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return x=ee.sent(),U=Ms(C)||Zu(C)?Pt(C.ownerDocument):dt(B,C),y=U.width,X=U.height,S=U.left,T=U.top,eA=Zh(B,C,e.backgroundColor),W={canvas:e.canvas,backgroundColor:eA,scale:(Dt=(Je=e.scale)!==null&&Je!==void 0?Je:i.devicePixelRatio)!==null&&Dt!==void 0?Dt:1,x:((Ue=e.x)!==null&&Ue!==void 0?Ue:0)+S,y:((Ye=e.y)!==null&&Ye!==void 0?Ye:0)+T,width:(qe=e.width)!==null&&qe!==void 0?qe:Math.ceil(y),height:(te=e.height)!==null&&te!==void 0?te:Math.ceil(X)},d?(B.logger.debug("Document cloned, using foreign object rendering"),AA=new Oh(B,W),[4,AA.render(C)]):[3,3];case 2:return O=ee.sent(),[3,5];case 3:return B.logger.debug("Document cloned, element located at "+S+","+T+" with size "+y+"x"+X+" using computed rendering"),B.logger.debug("Starting DOM parsing"),I=vo(B,C),eA===I.styles.backgroundColor&&(I.styles.backgroundColor=st.TRANSPARENT),B.logger.debug("Starting renderer for element at "+W.x+","+W.y+" with size "+W.width+"x"+W.height),AA=new Th(B,W),[4,AA.render(I)];case 4:O=ee.sent(),ee.label=5;case 5:return(!((je=e.removeContainer)!==null&&je!==void 0)||je)&&(Oo.destroy(x)||B.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),B.logger.debug("Finished rendering"),[2,O]}})})},Zh=function(r,e,s){var i=e.ownerDocument,a=i.documentElement?St(r,getComputedStyle(i.documentElement).backgroundColor):st.TRANSPARENT,h=i.body?St(r,getComputedStyle(i.body).backgroundColor):st.TRANSPARENT,c=typeof s=="string"?St(r,s):s===null?st.TRANSPARENT:4294967295;return e===i.documentElement?TA(a)?TA(h)?c:h:a:c};return kh})});var zh=jo((Pn,ra)=>{"use strict";(function(v,mA){typeof Pn=="object"&&typeof ra<"u"?mA(Pn):typeof define=="function"&&define.amd?define(["exports"],mA):(v=typeof globalThis<"u"?globalThis:v||self,mA(v.leaflet={}))})(Pn,function(v){"use strict";var mA="1.9.4";function E(A){var t,n,o,u;for(n=1,o=arguments.length;n<o;n++){u=arguments[n];for(t in u)A[t]=u[t]}return A}var j=Object.create||function(){function A(){}return function(t){return A.prototype=t,new A}}();function M(A,t){var n=Array.prototype.slice;if(A.bind)return A.bind.apply(A,n.call(arguments,1));var o=n.call(arguments,2);return function(){return A.apply(t,o.length?o.concat(n.call(arguments)):arguments)}}var XA=0;function R(A){return"_leaflet_id"in A||(A._leaflet_id=++XA),A._leaflet_id}function dt(A,t,n){var o,u,l,g;return g=function(){o=!1,u&&(l.apply(n,u),u=!1)},l=function(){o?u=arguments:(A.apply(n,arguments),setTimeout(g,t),o=!0)},l}function Pt(A,t,n){var o=t[1],u=t[0],l=o-u;return A===o&&n?A:((A-u)%l+l)%l+u}function sA(){return!1}function $(A,t){if(t===!1)return A;var n=Math.pow(10,t===void 0?6:t);return Math.round(A*n)/n}function Ot(A){return A.trim?A.trim():A.replace(/^\s+|\s+$/g,"")}function UA(A){return Ot(A).split(/\s+/)}function rA(A,t){Object.prototype.hasOwnProperty.call(A,"options")||(A.options=A.options?j(A.options):{});for(var n in t)A.options[n]=t[n];return A.options}function Fe(A,t,n){var o=[];for(var u in A)o.push(encodeURIComponent(n?u.toUpperCase():u)+"="+encodeURIComponent(A[u]));return(!t||t.indexOf("?")===-1?"?":"&")+o.join("&")}var Ft=/\{ *([\w_ -]+) *\}/g;function VA(A,t){return A.replace(Ft,function(n,o){var u=t[o];if(u===void 0)throw new Error("No value provided for variable "+n);return typeof u=="function"&&(u=u(t)),u})}var rt=Array.isArray||function(A){return Object.prototype.toString.call(A)==="[object Array]"};function Rr(A,t){for(var n=0;n<A.length;n++)if(A[n]===t)return n;return-1}var $e="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function wt(A){return window["webkit"+A]||window["moz"+A]||window["ms"+A]}var Ar=0;function tr(A){var t=+new Date,n=Math.max(0,16-(t-Ar));return Ar=t+n,window.setTimeout(A,n)}var Nr=window.requestAnimationFrame||wt("RequestAnimationFrame")||tr,Gr=window.cancelAnimationFrame||wt("CancelAnimationFrame")||wt("CancelRequestAnimationFrame")||function(A){window.clearTimeout(A)};function PA(A,t,n){if(n&&Nr===tr)A.call(t);else return Nr.call(window,M(A,t))}function OA(A){A&&Gr.call(window,A)}var On={__proto__:null,extend:E,create:j,bind:M,get lastId(){return XA},stamp:R,throttle:dt,wrapNum:Pt,falseFn:sA,formatNum:$,trim:Ot,splitWords:UA,setOptions:rA,getParamString:Fe,template:VA,isArray:rt,indexOf:Rr,emptyImageUrl:$e,requestFn:Nr,cancelFn:Gr,requestAnimFrame:PA,cancelAnimFrame:OA};function pt(){}pt.extend=function(A){var t=function(){rA(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},n=t.__super__=this.prototype,o=j(n);o.constructor=t,t.prototype=o;for(var u in this)Object.prototype.hasOwnProperty.call(this,u)&&u!=="prototype"&&u!=="__super__"&&(t[u]=this[u]);return A.statics&&E(t,A.statics),A.includes&&(Rn(A.includes),E.apply(null,[o].concat(A.includes))),E(o,A),delete o.statics,delete o.includes,o.options&&(o.options=n.options?j(n.options):{},E(o.options,A.options)),o._initHooks=[],o.callInitHooks=function(){if(!this._initHooksCalled){n.callInitHooks&&n.callInitHooks.call(this),this._initHooksCalled=!0;for(var l=0,g=o._initHooks.length;l<g;l++)o._initHooks[l].call(this)}},t},pt.include=function(A){var t=this.prototype.options;return E(this.prototype,A),A.options&&(this.prototype.options=t,this.mergeOptions(A.options)),this},pt.mergeOptions=function(A){return E(this.prototype.options,A),this},pt.addInitHook=function(A){var t=Array.prototype.slice.call(arguments,1),n=typeof A=="function"?A:function(){this[A].apply(this,t)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(n),this};function Rn(A){if(!(typeof L>"u"||!L||!L.Mixin)){A=rt(A)?A:[A];for(var t=0;t<A.length;t++)A[t]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var ZA={on:function(A,t,n){if(typeof A=="object")for(var o in A)this._on(o,A[o],t);else{A=UA(A);for(var u=0,l=A.length;u<l;u++)this._on(A[u],t,n)}return this},off:function(A,t,n){if(!arguments.length)delete this._events;else if(typeof A=="object")for(var o in A)this._off(o,A[o],t);else{A=UA(A);for(var u=arguments.length===1,l=0,g=A.length;l<g;l++)u?this._off(A[l]):this._off(A[l],t,n)}return this},_on:function(A,t,n,o){if(typeof t!="function"){console.warn("wrong listener type: "+typeof t);return}if(this._listens(A,t,n)===!1){n===this&&(n=void 0);var u={fn:t,ctx:n};o&&(u.once=!0),this._events=this._events||{},this._events[A]=this._events[A]||[],this._events[A].push(u)}},_off:function(A,t,n){var o,u,l;if(this._events&&(o=this._events[A],!!o)){if(arguments.length===1){if(this._firingCount)for(u=0,l=o.length;u<l;u++)o[u].fn=sA;delete this._events[A];return}if(typeof t!="function"){console.warn("wrong listener type: "+typeof t);return}var g=this._listens(A,t,n);if(g!==!1){var Q=o[g];this._firingCount&&(Q.fn=sA,this._events[A]=o=o.slice()),o.splice(g,1)}}},fire:function(A,t,n){if(!this.listens(A,n))return this;var o=E({},t,{type:A,target:this,sourceTarget:t&&t.sourceTarget||this});if(this._events){var u=this._events[A];if(u){this._firingCount=this._firingCount+1||1;for(var l=0,g=u.length;l<g;l++){var Q=u[l],m=Q.fn;Q.once&&this.off(A,m,Q.ctx),m.call(Q.ctx||this,o)}this._firingCount--}}return n&&this._propagateEvent(o),this},listens:function(A,t,n,o){typeof A!="string"&&console.warn('"string" type argument expected');var u=t;typeof t!="function"&&(o=!!t,u=void 0,n=void 0);var l=this._events&&this._events[A];if(l&&l.length&&this._listens(A,u,n)!==!1)return!0;if(o){for(var g in this._eventParents)if(this._eventParents[g].listens(A,t,n,o))return!0}return!1},_listens:function(A,t,n){if(!this._events)return!1;var o=this._events[A]||[];if(!t)return!!o.length;n===this&&(n=void 0);for(var u=0,l=o.length;u<l;u++)if(o[u].fn===t&&o[u].ctx===n)return u;return!1},once:function(A,t,n){if(typeof A=="object")for(var o in A)this._on(o,A[o],t,!0);else{A=UA(A);for(var u=0,l=A.length;u<l;u++)this._on(A[u],t,n,!0)}return this},addEventParent:function(A){return this._eventParents=this._eventParents||{},this._eventParents[R(A)]=A,this},removeEventParent:function(A){return this._eventParents&&delete this._eventParents[R(A)],this},_propagateEvent:function(A){for(var t in this._eventParents)this._eventParents[t].fire(A.type,E({layer:A.target,propagatedFrom:A.target},A),!0)}};ZA.addEventListener=ZA.on,ZA.removeEventListener=ZA.clearAllEventListeners=ZA.off,ZA.addOneTimeEventListener=ZA.once,ZA.fireEvent=ZA.fire,ZA.hasEventListeners=ZA.listens;var ye=pt.extend(ZA);function V(A,t,n){this.x=n?Math.round(A):A,this.y=n?Math.round(t):t}var Ei=Math.trunc||function(A){return A>0?Math.floor(A):Math.ceil(A)};V.prototype={clone:function(){return new V(this.x,this.y)},add:function(A){return this.clone()._add(N(A))},_add:function(A){return this.x+=A.x,this.y+=A.y,this},subtract:function(A){return this.clone()._subtract(N(A))},_subtract:function(A){return this.x-=A.x,this.y-=A.y,this},divideBy:function(A){return this.clone()._divideBy(A)},_divideBy:function(A){return this.x/=A,this.y/=A,this},multiplyBy:function(A){return this.clone()._multiplyBy(A)},_multiplyBy:function(A){return this.x*=A,this.y*=A,this},scaleBy:function(A){return new V(this.x*A.x,this.y*A.y)},unscaleBy:function(A){return new V(this.x/A.x,this.y/A.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Ei(this.x),this.y=Ei(this.y),this},distanceTo:function(A){A=N(A);var t=A.x-this.x,n=A.y-this.y;return Math.sqrt(t*t+n*n)},equals:function(A){return A=N(A),A.x===this.x&&A.y===this.y},contains:function(A){return A=N(A),Math.abs(A.x)<=Math.abs(this.x)&&Math.abs(A.y)<=Math.abs(this.y)},toString:function(){return"Point("+$(this.x)+", "+$(this.y)+")"}};function N(A,t,n){return A instanceof V?A:rt(A)?new V(A[0],A[1]):A==null?A:typeof A=="object"&&"x"in A&&"y"in A?new V(A.x,A.y):new V(A,t,n)}function cA(A,t){if(A)for(var n=t?[A,t]:A,o=0,u=n.length;o<u;o++)this.extend(n[o])}cA.prototype={extend:function(A){var t,n;if(!A)return this;if(A instanceof V||typeof A[0]=="number"||"x"in A)t=n=N(A);else if(A=RA(A),t=A.min,n=A.max,!t||!n)return this;return!this.min&&!this.max?(this.min=t.clone(),this.max=n.clone()):(this.min.x=Math.min(t.x,this.min.x),this.max.x=Math.max(n.x,this.max.x),this.min.y=Math.min(t.y,this.min.y),this.max.y=Math.max(n.y,this.max.y)),this},getCenter:function(A){return N((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,A)},getBottomLeft:function(){return N(this.min.x,this.max.y)},getTopRight:function(){return N(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(A){var t,n;return typeof A[0]=="number"||A instanceof V?A=N(A):A=RA(A),A instanceof cA?(t=A.min,n=A.max):t=n=A,t.x>=this.min.x&&n.x<=this.max.x&&t.y>=this.min.y&&n.y<=this.max.y},intersects:function(A){A=RA(A);var t=this.min,n=this.max,o=A.min,u=A.max,l=u.x>=t.x&&o.x<=n.x,g=u.y>=t.y&&o.y<=n.y;return l&&g},overlaps:function(A){A=RA(A);var t=this.min,n=this.max,o=A.min,u=A.max,l=u.x>t.x&&o.x<n.x,g=u.y>t.y&&o.y<n.y;return l&&g},isValid:function(){return!!(this.min&&this.max)},pad:function(A){var t=this.min,n=this.max,o=Math.abs(t.x-n.x)*A,u=Math.abs(t.y-n.y)*A;return RA(N(t.x-o,t.y-u),N(n.x+o,n.y+u))},equals:function(A){return A?(A=RA(A),this.min.equals(A.getTopLeft())&&this.max.equals(A.getBottomRight())):!1}};function RA(A,t){return!A||A instanceof cA?A:new cA(A,t)}function NA(A,t){if(A)for(var n=t?[A,t]:A,o=0,u=n.length;o<u;o++)this.extend(n[o])}NA.prototype={extend:function(A){var t=this._southWest,n=this._northEast,o,u;if(A instanceof iA)o=A,u=A;else if(A instanceof NA){if(o=A._southWest,u=A._northEast,!o||!u)return this}else return A?this.extend(Y(A)||wA(A)):this;return!t&&!n?(this._southWest=new iA(o.lat,o.lng),this._northEast=new iA(u.lat,u.lng)):(t.lat=Math.min(o.lat,t.lat),t.lng=Math.min(o.lng,t.lng),n.lat=Math.max(u.lat,n.lat),n.lng=Math.max(u.lng,n.lng)),this},pad:function(A){var t=this._southWest,n=this._northEast,o=Math.abs(t.lat-n.lat)*A,u=Math.abs(t.lng-n.lng)*A;return new NA(new iA(t.lat-o,t.lng-u),new iA(n.lat+o,n.lng+u))},getCenter:function(){return new iA((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new iA(this.getNorth(),this.getWest())},getSouthEast:function(){return new iA(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(A){typeof A[0]=="number"||A instanceof iA||"lat"in A?A=Y(A):A=wA(A);var t=this._southWest,n=this._northEast,o,u;return A instanceof NA?(o=A.getSouthWest(),u=A.getNorthEast()):o=u=A,o.lat>=t.lat&&u.lat<=n.lat&&o.lng>=t.lng&&u.lng<=n.lng},intersects:function(A){A=wA(A);var t=this._southWest,n=this._northEast,o=A.getSouthWest(),u=A.getNorthEast(),l=u.lat>=t.lat&&o.lat<=n.lat,g=u.lng>=t.lng&&o.lng<=n.lng;return l&&g},overlaps:function(A){A=wA(A);var t=this._southWest,n=this._northEast,o=A.getSouthWest(),u=A.getNorthEast(),l=u.lat>t.lat&&o.lat<n.lat,g=u.lng>t.lng&&o.lng<n.lng;return l&&g},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(A,t){return A?(A=wA(A),this._southWest.equals(A.getSouthWest(),t)&&this._northEast.equals(A.getNorthEast(),t)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function wA(A,t){return A instanceof NA?A:new NA(A,t)}function iA(A,t,n){if(isNaN(A)||isNaN(t))throw new Error("Invalid LatLng object: ("+A+", "+t+")");this.lat=+A,this.lng=+t,n!==void 0&&(this.alt=+n)}iA.prototype={equals:function(A,t){if(!A)return!1;A=Y(A);var n=Math.max(Math.abs(this.lat-A.lat),Math.abs(this.lng-A.lng));return n<=(t===void 0?1e-9:t)},toString:function(A){return"LatLng("+$(this.lat,A)+", "+$(this.lng,A)+")"},distanceTo:function(A){return yt.distance(this,Y(A))},wrap:function(){return yt.wrapLatLng(this)},toBounds:function(A){var t=180*A/40075017,n=t/Math.cos(Math.PI/180*this.lat);return wA([this.lat-t,this.lng-n],[this.lat+t,this.lng+n])},clone:function(){return new iA(this.lat,this.lng,this.alt)}};function Y(A,t,n){return A instanceof iA?A:rt(A)&&typeof A[0]!="object"?A.length===3?new iA(A[0],A[1],A[2]):A.length===2?new iA(A[0],A[1]):null:A==null?A:typeof A=="object"&&"lat"in A?new iA(A.lat,"lng"in A?A.lng:A.lon,A.alt):t===void 0?null:new iA(A,t,n)}var zA={latLngToPoint:function(A,t){var n=this.projection.project(A),o=this.scale(t);return this.transformation._transform(n,o)},pointToLatLng:function(A,t){var n=this.scale(t),o=this.transformation.untransform(A,n);return this.projection.unproject(o)},project:function(A){return this.projection.project(A)},unproject:function(A){return this.projection.unproject(A)},scale:function(A){return 256*Math.pow(2,A)},zoom:function(A){return Math.log(A/256)/Math.LN2},getProjectedBounds:function(A){if(this.infinite)return null;var t=this.projection.bounds,n=this.scale(A),o=this.transformation.transform(t.min,n),u=this.transformation.transform(t.max,n);return new cA(o,u)},infinite:!1,wrapLatLng:function(A){var t=this.wrapLng?Pt(A.lng,this.wrapLng,!0):A.lng,n=this.wrapLat?Pt(A.lat,this.wrapLat,!0):A.lat,o=A.alt;return new iA(n,t,o)},wrapLatLngBounds:function(A){var t=A.getCenter(),n=this.wrapLatLng(t),o=t.lat-n.lat,u=t.lng-n.lng;if(o===0&&u===0)return A;var l=A.getSouthWest(),g=A.getNorthEast(),Q=new iA(l.lat-o,l.lng-u),m=new iA(g.lat-o,g.lng-u);return new NA(Q,m)}},yt=E({},zA,{wrapLng:[-180,180],R:6371e3,distance:function(A,t){var n=Math.PI/180,o=A.lat*n,u=t.lat*n,l=Math.sin((t.lat-A.lat)*n/2),g=Math.sin((t.lng-A.lng)*n/2),Q=l*l+Math.cos(o)*Math.cos(u)*g*g,m=2*Math.atan2(Math.sqrt(Q),Math.sqrt(1-Q));return this.R*m}}),kr=6378137,Vr={R:kr,MAX_LATITUDE:85.0511287798,project:function(A){var t=Math.PI/180,n=this.MAX_LATITUDE,o=Math.max(Math.min(n,A.lat),-n),u=Math.sin(o*t);return new V(this.R*A.lng*t,this.R*Math.log((1+u)/(1-u))/2)},unproject:function(A){var t=180/Math.PI;return new iA((2*Math.atan(Math.exp(A.y/this.R))-Math.PI/2)*t,A.x*t/this.R)},bounds:function(){var A=kr*Math.PI;return new cA([-A,-A],[A,A])}()};function er(A,t,n,o){if(rt(A)){this._a=A[0],this._b=A[1],this._c=A[2],this._d=A[3];return}this._a=A,this._b=t,this._c=n,this._d=o}er.prototype={transform:function(A,t){return this._transform(A.clone(),t)},_transform:function(A,t){return t=t||1,A.x=t*(this._a*A.x+this._b),A.y=t*(this._c*A.y+this._d),A},untransform:function(A,t){return t=t||1,new V((A.x/t-this._b)/this._a,(A.y/t-this._d)/this._c)}};function re(A,t,n,o){return new er(A,t,n,o)}var Zr=E({},yt,{code:"EPSG:3857",projection:Vr,transformation:function(){var A=.5/(Math.PI*Vr.R);return re(A,.5,-A,.5)}()}),Nn=E({},Zr,{code:"EPSG:900913"});function Xr(A){return document.createElementNS("http://www.w3.org/2000/svg",A)}function zr(A,t){var n="",o,u,l,g,Q,m;for(o=0,l=A.length;o<l;o++){for(Q=A[o],u=0,g=Q.length;u<g;u++)m=Q[u],n+=(u?"L":"M")+m.x+" "+m.y;n+=t?K.svg?"z":"x":""}return n||"M0 0"}var rr=document.documentElement.style,it="ActiveXObject"in window,Wr=it&&!document.addEventListener,Jr="msLaunchUri"in navigator&&!("documentMode"in document),Ee=P("webkit"),Hi=P("android"),ie=P("android 2")||P("android 3"),Yr=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),ir=Hi&&P("Google")&&Yr<537&&!("AudioNode"in window),Rt=!!window.opera,Ii=!Jr&&P("chrome"),qr=P("gecko")&&!Ee&&!Rt&&!it,jr=!Ii&&P("safari"),ne=P("phantom"),nr="OTransition"in rr,se=navigator.platform.indexOf("Win")===0,GA=it&&"transition"in rr,Nt="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!ie,oe="MozPerspective"in rr,ae=!window.L_DISABLE_3D&&(GA||Nt||oe)&&!nr&&!ne,He=typeof orientation<"u"||P("mobile"),Gt=He&&Ee,Gn=He&&Nt,Ie=!window.PointerEvent&&window.MSPointerEvent,Le=!!(window.PointerEvent||Ie),sr="ontouchstart"in window||!!window.TouchEvent,$r=!window.L_NO_TOUCH&&(sr||Le),Ai=He&&Rt,xe=He&&qr,ti=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,or=function(){var A=!1;try{var t=Object.defineProperty({},"passive",{get:function(){A=!0}});window.addEventListener("testPassiveEventSupport",sA,t),window.removeEventListener("testPassiveEventSupport",sA,t)}catch{}return A}(),ar=function(){return!!document.createElement("canvas").getContext}(),be=!!(document.createElementNS&&Xr("svg").createSVGRect),Li=!!be&&function(){var A=document.createElement("div");return A.innerHTML="<svg/>",(A.firstChild&&A.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),kn=!be&&function(){try{var A=document.createElement("div");A.innerHTML='<v:shape adj="1"/>';var t=A.firstChild;return t.style.behavior="url(#default#VML)",t&&typeof t.adj=="object"}catch{return!1}}(),Vn=navigator.platform.indexOf("Mac")===0,xi=navigator.platform.indexOf("Linux")===0;function P(A){return navigator.userAgent.toLowerCase().indexOf(A)>=0}var K={ie:it,ielt9:Wr,edge:Jr,webkit:Ee,android:Hi,android23:ie,androidStock:ir,opera:Rt,chrome:Ii,gecko:qr,safari:jr,phantom:ne,opera12:nr,win:se,ie3d:GA,webkit3d:Nt,gecko3d:oe,any3d:ae,mobile:He,mobileWebkit:Gt,mobileWebkit3d:Gn,msPointer:Ie,pointer:Le,touch:$r,touchNative:sr,mobileOpera:Ai,mobileGecko:xe,retina:ti,passiveEvents:or,canvas:ar,svg:be,vml:kn,inlineSvg:Li,mac:Vn,linux:xi},ur=K.msPointer?"MSPointerDown":"pointerdown",at=K.msPointer?"MSPointerMove":"pointermove",hr=K.msPointer?"MSPointerUp":"pointerup",ei=K.msPointer?"MSPointerCancel":"pointercancel",cr={touchstart:ur,touchmove:at,touchend:hr,touchcancel:ei},bi={touchstart:Wn,touchmove:lr,touchend:lr,touchcancel:lr},kt={},Ti=!1;function Zn(A,t,n){return t==="touchstart"&&zn(),bi[t]?(n=bi[t].bind(this,n),A.addEventListener(cr[t],n,!1),n):(console.warn("wrong event specified:",t),sA)}function ri(A,t,n){if(!cr[t]){console.warn("wrong event specified:",t);return}A.removeEventListener(cr[t],n,!1)}function Ki(A){kt[A.pointerId]=A}function Xn(A){kt[A.pointerId]&&(kt[A.pointerId]=A)}function Si(A){delete kt[A.pointerId]}function zn(){Ti||(document.addEventListener(ur,Ki,!0),document.addEventListener(at,Xn,!0),document.addEventListener(hr,Si,!0),document.addEventListener(ei,Si,!0),Ti=!0)}function lr(A,t){if(t.pointerType!==(t.MSPOINTER_TYPE_MOUSE||"mouse")){t.touches=[];for(var n in kt)t.touches.push(kt[n]);t.changedTouches=[t],A(t)}}function Wn(A,t){t.MSPOINTER_TYPE_TOUCH&&t.pointerType===t.MSPOINTER_TYPE_TOUCH&&FA(t),lr(A,t)}function Jn(A){var t={},n,o;for(o in A)n=A[o],t[o]=n&&n.bind?n.bind(A):n;return A=t,t.type="dblclick",t.detail=2,t.isTrusted=!1,t._simulated=!0,t}var Te=200;function Mi(A,t){A.addEventListener("dblclick",t);var n=0,o;function u(l){if(l.detail!==1){o=l.detail;return}if(!(l.pointerType==="mouse"||l.sourceCapabilities&&!l.sourceCapabilities.firesTouchEvents)){var g=Ni(l);if(!(g.some(function(m){return m instanceof HTMLLabelElement&&m.attributes.for})&&!g.some(function(m){return m instanceof HTMLInputElement||m instanceof HTMLSelectElement}))){var Q=Date.now();Q-n<=Te?(o++,o===2&&t(Jn(l))):o=1,n=Q}}}return A.addEventListener("click",u),{dblclick:t,simDblclick:u}}function Br(A,t){A.removeEventListener("dblclick",t.dblclick),A.removeEventListener("click",t.simDblclick)}var fr=wr(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Qt=wr(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Di=Qt==="webkitTransition"||Qt==="OTransition"?Qt+"End":"transitionend";function Pi(A){return typeof A=="string"?document.getElementById(A):A}function Et(A,t){var n=A.style[t]||A.currentStyle&&A.currentStyle[t];if((!n||n==="auto")&&document.defaultView){var o=document.defaultView.getComputedStyle(A,null);n=o?o[t]:null}return n==="auto"?null:n}function J(A,t,n){var o=document.createElement(A);return o.className=t||"",n&&n.appendChild(o),o}function lA(A){var t=A.parentNode;t&&t.removeChild(A)}function gr(A){for(;A.firstChild;)A.removeChild(A.firstChild)}function ue(A){var t=A.parentNode;t&&t.lastChild!==A&&t.appendChild(A)}function mt(A){var t=A.parentNode;t&&t.firstChild!==A&&t.insertBefore(A,t.firstChild)}function he(A,t){if(A.classList!==void 0)return A.classList.contains(t);var n=dr(A);return n.length>0&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(n)}function G(A,t){if(A.classList!==void 0)for(var n=UA(t),o=0,u=n.length;o<u;o++)A.classList.add(n[o]);else if(!he(A,t)){var l=dr(A);LA(A,(l?l+" ":"")+t)}}function fA(A,t){A.classList!==void 0?A.classList.remove(t):LA(A,Ot((" "+dr(A)+" ").replace(" "+t+" "," ")))}function LA(A,t){A.className.baseVal===void 0?A.className=t:A.className.baseVal=t}function dr(A){return A.correspondingElement&&(A=A.correspondingElement),A.className.baseVal===void 0?A.className:A.className.baseVal}function WA(A,t){"opacity"in A.style?A.style.opacity=t:"filter"in A.style&&Yn(A,t)}function Yn(A,t){var n=!1,o="DXImageTransform.Microsoft.Alpha";try{n=A.filters.item(o)}catch{if(t===1)return}t=Math.round(t*100),n?(n.Enabled=t!==100,n.Opacity=t):A.style.filter+=" progid:"+o+"(opacity="+t+")"}function wr(A){for(var t=document.documentElement.style,n=0;n<A.length;n++)if(A[n]in t)return A[n];return!1}function Vt(A,t,n){var o=t||new V(0,0);A.style[fr]=(K.ie3d?"translate("+o.x+"px,"+o.y+"px)":"translate3d("+o.x+"px,"+o.y+"px,0)")+(n?" scale("+n+")":"")}function pA(A,t){A._leaflet_pos=t,K.any3d?Vt(A,t):(A.style.left=t.x+"px",A.style.top=t.y+"px")}function Zt(A){return A._leaflet_pos||new V(0,0)}var Ke,Ht,ii;if("onselectstart"in document)Ke=function(){Z(window,"selectstart",FA)},Ht=function(){aA(window,"selectstart",FA)};else{var ce=wr(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);Ke=function(){if(ce){var A=document.documentElement.style;ii=A[ce],A[ce]="none"}},Ht=function(){ce&&(document.documentElement.style[ce]=ii,ii=void 0)}}function ni(){Z(window,"dragstart",FA)}function si(){aA(window,"dragstart",FA)}var Se,Me;function Ct(A){for(;A.tabIndex===-1;)A=A.parentNode;A.style&&(pr(),Se=A,Me=A.style.outlineStyle,A.style.outlineStyle="none",Z(window,"keydown",pr))}function pr(){Se&&(Se.style.outlineStyle=Me,Se=void 0,Me=void 0,aA(window,"keydown",pr))}function Oi(A){do A=A.parentNode;while((!A.offsetWidth||!A.offsetHeight)&&A!==document.body);return A}function oi(A){var t=A.getBoundingClientRect();return{x:t.width/A.offsetWidth||1,y:t.height/A.offsetHeight||1,boundingClientRect:t}}var De={__proto__:null,TRANSFORM:fr,TRANSITION:Qt,TRANSITION_END:Di,get:Pi,getStyle:Et,create:J,remove:lA,empty:gr,toFront:ue,toBack:mt,hasClass:he,addClass:G,removeClass:fA,setClass:LA,getClass:dr,setOpacity:WA,testProp:wr,setTransform:Vt,setPosition:pA,getPosition:Zt,get disableTextSelection(){return Ke},get enableTextSelection(){return Ht},disableImageDrag:ni,enableImageDrag:si,preventOutline:Ct,restoreOutline:pr,getSizedParentNode:Oi,getScale:oi};function Z(A,t,n,o){if(t&&typeof t=="object")for(var u in t)ui(A,u,t[u],n);else{t=UA(t);for(var l=0,g=t.length;l<g;l++)ui(A,t[l],n,o)}return this}var ut="_leaflet_events";function aA(A,t,n,o){if(arguments.length===1)Ri(A),delete A[ut];else if(t&&typeof t=="object")for(var u in t)JA(A,u,t[u],n);else if(t=UA(t),arguments.length===2)Ri(A,function(Q){return Rr(t,Q)!==-1});else for(var l=0,g=t.length;l<g;l++)JA(A,t[l],n,o);return this}function Ri(A,t){for(var n in A[ut]){var o=n.split(/\d/)[0];(!t||t(o))&&JA(A,o,null,null,n)}}var ai={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function ui(A,t,n,o){var u=t+R(n)+(o?"_"+R(o):"");if(A[ut]&&A[ut][u])return this;var l=function(Q){return n.call(o||A,Q||window.event)},g=l;!K.touchNative&&K.pointer&&t.indexOf("touch")===0?l=Zn(A,t,l):K.touch&&t==="dblclick"?l=Mi(A,l):"addEventListener"in A?t==="touchstart"||t==="touchmove"||t==="wheel"||t==="mousewheel"?A.addEventListener(ai[t]||t,l,K.passiveEvents?{passive:!1}:!1):t==="mouseenter"||t==="mouseleave"?(l=function(Q){Q=Q||window.event,li(A,Q)&&g(Q)},A.addEventListener(ai[t],l,!1)):A.addEventListener(t,g,!1):A.attachEvent("on"+t,l),A[ut]=A[ut]||{},A[ut][u]=l}function JA(A,t,n,o,u){u=u||t+R(n)+(o?"_"+R(o):"");var l=A[ut]&&A[ut][u];if(!l)return this;!K.touchNative&&K.pointer&&t.indexOf("touch")===0?ri(A,t,l):K.touch&&t==="dblclick"?Br(A,l):"removeEventListener"in A?A.removeEventListener(ai[t]||t,l,!1):A.detachEvent("on"+t,l),A[ut][u]=null}function It(A){return A.stopPropagation?A.stopPropagation():A.originalEvent?A.originalEvent._stopped=!0:A.cancelBubble=!0,this}function Qr(A){return ui(A,"wheel",It),this}function le(A){return Z(A,"mousedown touchstart dblclick contextmenu",It),A._leaflet_disable_click=!0,this}function FA(A){return A.preventDefault?A.preventDefault():A.returnValue=!1,this}function Xt(A){return FA(A),It(A),this}function Ni(A){if(A.composedPath)return A.composedPath();for(var t=[],n=A.target;n;)t.push(n),n=n.parentNode;return t}function hi(A,t){if(!t)return new V(A.clientX,A.clientY);var n=oi(t),o=n.boundingClientRect;return new V((A.clientX-o.left)/n.x-t.clientLeft,(A.clientY-o.top)/n.y-t.clientTop)}var Gi=K.linux&&K.chrome?window.devicePixelRatio:K.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function ci(A){return K.edge?A.wheelDeltaY/2:A.deltaY&&A.deltaMode===0?-A.deltaY/Gi:A.deltaY&&A.deltaMode===1?-A.deltaY*20:A.deltaY&&A.deltaMode===2?-A.deltaY*60:A.deltaX||A.deltaZ?0:A.wheelDelta?(A.wheelDeltaY||A.wheelDelta)/2:A.detail&&Math.abs(A.detail)<32765?-A.detail*20:A.detail?A.detail/-32765*60:0}function li(A,t){var n=t.relatedTarget;if(!n)return!0;try{for(;n&&n!==A;)n=n.parentNode}catch{return!1}return n!==A}var qn={__proto__:null,on:Z,off:aA,stopPropagation:It,disableScrollPropagation:Qr,disableClickPropagation:le,preventDefault:FA,stop:Xt,getPropagationPath:Ni,getMousePosition:hi,getWheelDelta:ci,isExternalTarget:li,addListener:Z,removeListener:aA},yA=ye.extend({run:function(A,t,n,o){this.stop(),this._el=A,this._inProgress=!0,this._duration=n||.25,this._easeOutPower=1/Math.max(o||.5,.2),this._startPos=Zt(A),this._offset=t.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=PA(this._animate,this),this._step()},_step:function(A){var t=+new Date-this._startTime,n=this._duration*1e3;t<n?this._runFrame(this._easeOut(t/n),A):(this._runFrame(1),this._complete())},_runFrame:function(A,t){var n=this._startPos.add(this._offset.multiplyBy(A));t&&n._round(),pA(this._el,n),this.fire("step")},_complete:function(){OA(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(A){return 1-Math.pow(1-A,this._easeOutPower)}}),q=ye.extend({options:{crs:Zr,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(A,t){t=rA(this,t),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(A),this._initLayout(),this._onResize=M(this._onResize,this),this._initEvents(),t.maxBounds&&this.setMaxBounds(t.maxBounds),t.zoom!==void 0&&(this._zoom=this._limitZoom(t.zoom)),t.center&&t.zoom!==void 0&&this.setView(Y(t.center),t.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Qt&&K.any3d&&!K.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),Z(this._proxy,Di,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(A,t,n){if(t=t===void 0?this._zoom:this._limitZoom(t),A=this._limitCenter(Y(A),t,this.options.maxBounds),n=n||{},this._stop(),this._loaded&&!n.reset&&n!==!0){n.animate!==void 0&&(n.zoom=E({animate:n.animate},n.zoom),n.pan=E({animate:n.animate,duration:n.duration},n.pan));var o=this._zoom!==t?this._tryAnimatedZoom&&this._tryAnimatedZoom(A,t,n.zoom):this._tryAnimatedPan(A,n.pan);if(o)return clearTimeout(this._sizeTimer),this}return this._resetView(A,t,n.pan&&n.pan.noMoveStart),this},setZoom:function(A,t){return this._loaded?this.setView(this.getCenter(),A,{zoom:t}):(this._zoom=A,this)},zoomIn:function(A,t){return A=A||(K.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+A,t)},zoomOut:function(A,t){return A=A||(K.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-A,t)},setZoomAround:function(A,t,n){var o=this.getZoomScale(t),u=this.getSize().divideBy(2),l=A instanceof V?A:this.latLngToContainerPoint(A),g=l.subtract(u).multiplyBy(1-1/o),Q=this.containerPointToLatLng(u.add(g));return this.setView(Q,t,{zoom:n})},_getBoundsCenterZoom:function(A,t){t=t||{},A=A.getBounds?A.getBounds():wA(A);var n=N(t.paddingTopLeft||t.padding||[0,0]),o=N(t.paddingBottomRight||t.padding||[0,0]),u=this.getBoundsZoom(A,!1,n.add(o));if(u=typeof t.maxZoom=="number"?Math.min(t.maxZoom,u):u,u===1/0)return{center:A.getCenter(),zoom:u};var l=o.subtract(n).divideBy(2),g=this.project(A.getSouthWest(),u),Q=this.project(A.getNorthEast(),u),m=this.unproject(g.add(Q).divideBy(2).add(l),u);return{center:m,zoom:u}},fitBounds:function(A,t){if(A=wA(A),!A.isValid())throw new Error("Bounds are not valid.");var n=this._getBoundsCenterZoom(A,t);return this.setView(n.center,n.zoom,t)},fitWorld:function(A){return this.fitBounds([[-90,-180],[90,180]],A)},panTo:function(A,t){return this.setView(A,this._zoom,{pan:t})},panBy:function(A,t){if(A=N(A).round(),t=t||{},!A.x&&!A.y)return this.fire("moveend");if(t.animate!==!0&&!this.getSize().contains(A))return this._resetView(this.unproject(this.project(this.getCenter()).add(A)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new yA,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),t.noMoveStart||this.fire("movestart"),t.animate!==!1){G(this._mapPane,"leaflet-pan-anim");var n=this._getMapPanePos().subtract(A).round();this._panAnim.run(this._mapPane,n,t.duration||.25,t.easeLinearity)}else this._rawPanBy(A),this.fire("move").fire("moveend");return this},flyTo:function(A,t,n){if(n=n||{},n.animate===!1||!K.any3d)return this.setView(A,t,n);this._stop();var o=this.project(this.getCenter()),u=this.project(A),l=this.getSize(),g=this._zoom;A=Y(A),t=t===void 0?g:t;var Q=Math.max(l.x,l.y),m=Q*this.getZoomScale(g,t),_=u.distanceTo(o)||1,F=1.42,D=F*F;function z(dA){var xr=dA?-1:1,Qs=dA?m:Q,mi=m*m-Q*Q+xr*D*D*_*_,br=2*Qs*D*_,Ci=mi/br,Qn=Math.sqrt(Ci*Ci+1)-Ci,ms=Qn<1e-9?-18:Math.log(Qn);return ms}function SA(dA){return(Math.exp(dA)-Math.exp(-dA))/2}function _A(dA){return(Math.exp(dA)+Math.exp(-dA))/2}function $A(dA){return SA(dA)/_A(dA)}var kA=z(0);function me(dA){return Q*(_A(kA)/_A(kA+F*dA))}function gs(dA){return Q*(_A(kA)*$A(kA+F*dA)-SA(kA))/D}function ds(dA){return 1-Math.pow(1-dA,1.5)}var ws=Date.now(),Lr=(z(1)-kA)/F,ps=n.duration?1e3*n.duration:1e3*Lr*.8;function pn(){var dA=(Date.now()-ws)/ps,xr=ds(dA)*Lr;dA<=1?(this._flyToFrame=PA(pn,this),this._move(this.unproject(o.add(u.subtract(o).multiplyBy(gs(xr)/_)),g),this.getScaleZoom(Q/me(xr),g),{flyTo:!0})):this._move(A,t)._moveEnd(!0)}return this._moveStart(!0,n.noMoveStart),pn.call(this),this},flyToBounds:function(A,t){var n=this._getBoundsCenterZoom(A,t);return this.flyTo(n.center,n.zoom,t)},setMaxBounds:function(A){return A=wA(A),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),A.isValid()?(this.options.maxBounds=A,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(A){var t=this.options.minZoom;return this.options.minZoom=A,this._loaded&&t!==A&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(A):this},setMaxZoom:function(A){var t=this.options.maxZoom;return this.options.maxZoom=A,this._loaded&&t!==A&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(A):this},panInsideBounds:function(A,t){this._enforcingBounds=!0;var n=this.getCenter(),o=this._limitCenter(n,this._zoom,wA(A));return n.equals(o)||this.panTo(o,t),this._enforcingBounds=!1,this},panInside:function(A,t){t=t||{};var n=N(t.paddingTopLeft||t.padding||[0,0]),o=N(t.paddingBottomRight||t.padding||[0,0]),u=this.project(this.getCenter()),l=this.project(A),g=this.getPixelBounds(),Q=RA([g.min.add(n),g.max.subtract(o)]),m=Q.getSize();if(!Q.contains(l)){this._enforcingBounds=!0;var _=l.subtract(Q.getCenter()),F=Q.extend(l).getSize().subtract(m);u.x+=_.x<0?-F.x:F.x,u.y+=_.y<0?-F.y:F.y,this.panTo(this.unproject(u),t),this._enforcingBounds=!1}return this},invalidateSize:function(A){if(!this._loaded)return this;A=E({animate:!1,pan:!0},A===!0?{animate:!0}:A);var t=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var n=this.getSize(),o=t.divideBy(2).round(),u=n.divideBy(2).round(),l=o.subtract(u);return!l.x&&!l.y?this:(A.animate&&A.pan?this.panBy(l):(A.pan&&this._rawPanBy(l),this.fire("move"),A.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(M(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:t,newSize:n}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(A){if(A=this._locateOptions=E({timeout:1e4,watch:!1},A),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var t=M(this._handleGeolocationResponse,this),n=M(this._handleGeolocationError,this);return A.watch?this._locationWatchId=navigator.geolocation.watchPosition(t,n,A):navigator.geolocation.getCurrentPosition(t,n,A),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(A){if(this._container._leaflet_id){var t=A.code,n=A.message||(t===1?"permission denied":t===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:t,message:"Geolocation error: "+n+"."})}},_handleGeolocationResponse:function(A){if(this._container._leaflet_id){var t=A.coords.latitude,n=A.coords.longitude,o=new iA(t,n),u=o.toBounds(A.coords.accuracy*2),l=this._locateOptions;if(l.setView){var g=this.getBoundsZoom(u);this.setView(o,l.maxZoom?Math.min(g,l.maxZoom):g)}var Q={latlng:o,bounds:u,timestamp:A.timestamp};for(var m in A.coords)typeof A.coords[m]=="number"&&(Q[m]=A.coords[m]);this.fire("locationfound",Q)}},addHandler:function(A,t){if(!t)return this;var n=this[A]=new t(this);return this._handlers.push(n),this.options[A]&&n.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),lA(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(OA(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var A;for(A in this._layers)this._layers[A].remove();for(A in this._panes)lA(this._panes[A]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(A,t){var n="leaflet-pane"+(A?" leaflet-"+A.replace("Pane","")+"-pane":""),o=J("div",n,t||this._mapPane);return A&&(this._panes[A]=o),o},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var A=this.getPixelBounds(),t=this.unproject(A.getBottomLeft()),n=this.unproject(A.getTopRight());return new NA(t,n)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(A,t,n){A=wA(A),n=N(n||[0,0]);var o=this.getZoom()||0,u=this.getMinZoom(),l=this.getMaxZoom(),g=A.getNorthWest(),Q=A.getSouthEast(),m=this.getSize().subtract(n),_=RA(this.project(Q,o),this.project(g,o)).getSize(),F=K.any3d?this.options.zoomSnap:1,D=m.x/_.x,z=m.y/_.y,SA=t?Math.max(D,z):Math.min(D,z);return o=this.getScaleZoom(SA,o),F&&(o=Math.round(o/(F/100))*(F/100),o=t?Math.ceil(o/F)*F:Math.floor(o/F)*F),Math.max(u,Math.min(l,o))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new V(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(A,t){var n=this._getTopLeftPoint(A,t);return new cA(n,n.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(A){return this.options.crs.getProjectedBounds(A===void 0?this.getZoom():A)},getPane:function(A){return typeof A=="string"?this._panes[A]:A},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(A,t){var n=this.options.crs;return t=t===void 0?this._zoom:t,n.scale(A)/n.scale(t)},getScaleZoom:function(A,t){var n=this.options.crs;t=t===void 0?this._zoom:t;var o=n.zoom(A*n.scale(t));return isNaN(o)?1/0:o},project:function(A,t){return t=t===void 0?this._zoom:t,this.options.crs.latLngToPoint(Y(A),t)},unproject:function(A,t){return t=t===void 0?this._zoom:t,this.options.crs.pointToLatLng(N(A),t)},layerPointToLatLng:function(A){var t=N(A).add(this.getPixelOrigin());return this.unproject(t)},latLngToLayerPoint:function(A){var t=this.project(Y(A))._round();return t._subtract(this.getPixelOrigin())},wrapLatLng:function(A){return this.options.crs.wrapLatLng(Y(A))},wrapLatLngBounds:function(A){return this.options.crs.wrapLatLngBounds(wA(A))},distance:function(A,t){return this.options.crs.distance(Y(A),Y(t))},containerPointToLayerPoint:function(A){return N(A).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(A){return N(A).add(this._getMapPanePos())},containerPointToLatLng:function(A){var t=this.containerPointToLayerPoint(N(A));return this.layerPointToLatLng(t)},latLngToContainerPoint:function(A){return this.layerPointToContainerPoint(this.latLngToLayerPoint(Y(A)))},mouseEventToContainerPoint:function(A){return hi(A,this._container)},mouseEventToLayerPoint:function(A){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(A))},mouseEventToLatLng:function(A){return this.layerPointToLatLng(this.mouseEventToLayerPoint(A))},_initContainer:function(A){var t=this._container=Pi(A);if(t){if(t._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");Z(t,"scroll",this._onScroll,this),this._containerId=R(t)},_initLayout:function(){var A=this._container;this._fadeAnimated=this.options.fadeAnimation&&K.any3d,G(A,"leaflet-container"+(K.touch?" leaflet-touch":"")+(K.retina?" leaflet-retina":"")+(K.ielt9?" leaflet-oldie":"")+(K.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var t=Et(A,"position");t!=="absolute"&&t!=="relative"&&t!=="fixed"&&t!=="sticky"&&(A.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var A=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),pA(this._mapPane,new V(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(G(A.markerPane,"leaflet-zoom-hide"),G(A.shadowPane,"leaflet-zoom-hide"))},_resetView:function(A,t,n){pA(this._mapPane,new V(0,0));var o=!this._loaded;this._loaded=!0,t=this._limitZoom(t),this.fire("viewprereset");var u=this._zoom!==t;this._moveStart(u,n)._move(A,t)._moveEnd(u),this.fire("viewreset"),o&&this.fire("load")},_moveStart:function(A,t){return A&&this.fire("zoomstart"),t||this.fire("movestart"),this},_move:function(A,t,n,o){t===void 0&&(t=this._zoom);var u=this._zoom!==t;return this._zoom=t,this._lastCenter=A,this._pixelOrigin=this._getNewPixelOrigin(A),o?n&&n.pinch&&this.fire("zoom",n):((u||n&&n.pinch)&&this.fire("zoom",n),this.fire("move",n)),this},_moveEnd:function(A){return A&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return OA(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(A){pA(this._mapPane,this._getMapPanePos().subtract(A))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(A){this._targets={},this._targets[R(this._container)]=this;var t=A?aA:Z;t(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&t(window,"resize",this._onResize,this),K.any3d&&this.options.transform3DLimit&&(A?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){OA(this._resizeRequest),this._resizeRequest=PA(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var A=this._getMapPanePos();Math.max(Math.abs(A.x),Math.abs(A.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(A,t){for(var n=[],o,u=t==="mouseout"||t==="mouseover",l=A.target||A.srcElement,g=!1;l;){if(o=this._targets[R(l)],o&&(t==="click"||t==="preclick")&&this._draggableMoved(o)){g=!0;break}if(o&&o.listens(t,!0)&&(u&&!li(l,A)||(n.push(o),u))||l===this._container)break;l=l.parentNode}return!n.length&&!g&&!u&&this.listens(t,!0)&&(n=[this]),n},_isClickDisabled:function(A){for(;A&&A!==this._container;){if(A._leaflet_disable_click)return!0;A=A.parentNode}},_handleDOMEvent:function(A){var t=A.target||A.srcElement;if(!(!this._loaded||t._leaflet_disable_events||A.type==="click"&&this._isClickDisabled(t))){var n=A.type;n==="mousedown"&&Ct(t),this._fireDOMEvent(A,n)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(A,t,n){if(A.type==="click"){var o=E({},A);o.type="preclick",this._fireDOMEvent(o,o.type,n)}var u=this._findEventTargets(A,t);if(n){for(var l=[],g=0;g<n.length;g++)n[g].listens(t,!0)&&l.push(n[g]);u=l.concat(u)}if(u.length){t==="contextmenu"&&FA(A);var Q=u[0],m={originalEvent:A};if(A.type!=="keypress"&&A.type!=="keydown"&&A.type!=="keyup"){var _=Q.getLatLng&&(!Q._radius||Q._radius<=10);m.containerPoint=_?this.latLngToContainerPoint(Q.getLatLng()):this.mouseEventToContainerPoint(A),m.layerPoint=this.containerPointToLayerPoint(m.containerPoint),m.latlng=_?Q.getLatLng():this.layerPointToLatLng(m.layerPoint)}for(g=0;g<u.length;g++)if(u[g].fire(t,m,!0),m.originalEvent._stopped||u[g].options.bubblingMouseEvents===!1&&Rr(this._mouseEvents,t)!==-1)return}},_draggableMoved:function(A){return A=A.dragging&&A.dragging.enabled()?A:this,A.dragging&&A.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var A=0,t=this._handlers.length;A<t;A++)this._handlers[A].disable()},whenReady:function(A,t){return this._loaded?A.call(t||this,{target:this}):this.on("load",A,t),this},_getMapPanePos:function(){return Zt(this._mapPane)||new V(0,0)},_moved:function(){var A=this._getMapPanePos();return A&&!A.equals([0,0])},_getTopLeftPoint:function(A,t){var n=A&&t!==void 0?this._getNewPixelOrigin(A,t):this.getPixelOrigin();return n.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(A,t){var n=this.getSize()._divideBy(2);return this.project(A,t)._subtract(n)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(A,t,n){var o=this._getNewPixelOrigin(n,t);return this.project(A,t)._subtract(o)},_latLngBoundsToNewLayerBounds:function(A,t,n){var o=this._getNewPixelOrigin(n,t);return RA([this.project(A.getSouthWest(),t)._subtract(o),this.project(A.getNorthWest(),t)._subtract(o),this.project(A.getSouthEast(),t)._subtract(o),this.project(A.getNorthEast(),t)._subtract(o)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(A){return this.latLngToLayerPoint(A).subtract(this._getCenterLayerPoint())},_limitCenter:function(A,t,n){if(!n)return A;var o=this.project(A,t),u=this.getSize().divideBy(2),l=new cA(o.subtract(u),o.add(u)),g=this._getBoundsOffset(l,n,t);return Math.abs(g.x)<=1&&Math.abs(g.y)<=1?A:this.unproject(o.add(g),t)},_limitOffset:function(A,t){if(!t)return A;var n=this.getPixelBounds(),o=new cA(n.min.add(A),n.max.add(A));return A.add(this._getBoundsOffset(o,t))},_getBoundsOffset:function(A,t,n){var o=RA(this.project(t.getNorthEast(),n),this.project(t.getSouthWest(),n)),u=o.min.subtract(A.min),l=o.max.subtract(A.max),g=this._rebound(u.x,-l.x),Q=this._rebound(u.y,-l.y);return new V(g,Q)},_rebound:function(A,t){return A+t>0?Math.round(A-t)/2:Math.max(0,Math.ceil(A))-Math.max(0,Math.floor(t))},_limitZoom:function(A){var t=this.getMinZoom(),n=this.getMaxZoom(),o=K.any3d?this.options.zoomSnap:1;return o&&(A=Math.round(A/o)*o),Math.max(t,Math.min(n,A))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){fA(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(A,t){var n=this._getCenterOffset(A)._trunc();return(t&&t.animate)!==!0&&!this.getSize().contains(n)?!1:(this.panBy(n,t),!0)},_createAnimProxy:function(){var A=this._proxy=J("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(A),this.on("zoomanim",function(t){var n=fr,o=this._proxy.style[n];Vt(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),o===this._proxy.style[n]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){lA(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var A=this.getCenter(),t=this.getZoom();Vt(this._proxy,this.project(A,t),this.getZoomScale(t,1))},_catchTransitionEnd:function(A){this._animatingZoom&&A.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(A,t,n){if(this._animatingZoom)return!0;if(n=n||{},!this._zoomAnimated||n.animate===!1||this._nothingToAnimate()||Math.abs(t-this._zoom)>this.options.zoomAnimationThreshold)return!1;var o=this.getZoomScale(t),u=this._getCenterOffset(A)._divideBy(1-1/o);return n.animate!==!0&&!this.getSize().contains(u)?!1:(PA(function(){this._moveStart(!0,n.noMoveStart||!1)._animateZoom(A,t,!0)},this),!0)},_animateZoom:function(A,t,n,o){this._mapPane&&(n&&(this._animatingZoom=!0,this._animateToCenter=A,this._animateToZoom=t,G(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:A,zoom:t,noUpdate:o}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(M(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&fA(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Be(A,t){return new q(A,t)}var nt=pt.extend({options:{position:"topright"},initialize:function(A){rA(this,A)},getPosition:function(){return this.options.position},setPosition:function(A){var t=this._map;return t&&t.removeControl(this),this.options.position=A,t&&t.addControl(this),this},getContainer:function(){return this._container},addTo:function(A){this.remove(),this._map=A;var t=this._container=this.onAdd(A),n=this.getPosition(),o=A._controlCorners[n];return G(t,"leaflet-control"),n.indexOf("bottom")!==-1?o.insertBefore(t,o.firstChild):o.appendChild(t),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(lA(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(A){this._map&&A&&A.screenX>0&&A.screenY>0&&this._map.getContainer().focus()}}),Pe=function(A){return new nt(A)};q.include({addControl:function(A){return A.addTo(this),this},removeControl:function(A){return A.remove(),this},_initControlPos:function(){var A=this._controlCorners={},t="leaflet-",n=this._controlContainer=J("div",t+"control-container",this._container);function o(u,l){var g=t+u+" "+t+l;A[u+l]=J("div",g,n)}o("top","left"),o("top","right"),o("bottom","left"),o("bottom","right")},_clearControlPos:function(){for(var A in this._controlCorners)lA(this._controlCorners[A]);lA(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var ki=nt.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(A,t,n,o){return n<o?-1:o<n?1:0}},initialize:function(A,t,n){rA(this,n),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var o in A)this._addLayer(A[o],o);for(o in t)this._addLayer(t[o],o,!0)},onAdd:function(A){this._initLayout(),this._update(),this._map=A,A.on("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(A){return nt.prototype.addTo.call(this,A),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var A=0;A<this._layers.length;A++)this._layers[A].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(A,t){return this._addLayer(A,t),this._map?this._update():this},addOverlay:function(A,t){return this._addLayer(A,t,!0),this._map?this._update():this},removeLayer:function(A){A.off("add remove",this._onLayerChange,this);var t=this._getLayer(R(A));return t&&this._layers.splice(this._layers.indexOf(t),1),this._map?this._update():this},expand:function(){G(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var A=this._map.getSize().y-(this._container.offsetTop+50);return A<this._section.clientHeight?(G(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=A+"px"):fA(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return fA(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var A="leaflet-control-layers",t=this._container=J("div",A),n=this.options.collapsed;t.setAttribute("aria-haspopup",!0),le(t),Qr(t);var o=this._section=J("section",A+"-list");n&&(this._map.on("click",this.collapse,this),Z(t,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var u=this._layersLink=J("a",A+"-toggle",t);u.href="#",u.title="Layers",u.setAttribute("role","button"),Z(u,{keydown:function(l){l.keyCode===13&&this._expandSafely()},click:function(l){FA(l),this._expandSafely()}},this),n||this.expand(),this._baseLayersList=J("div",A+"-base",o),this._separator=J("div",A+"-separator",o),this._overlaysList=J("div",A+"-overlays",o),t.appendChild(o)},_getLayer:function(A){for(var t=0;t<this._layers.length;t++)if(this._layers[t]&&R(this._layers[t].layer)===A)return this._layers[t]},_addLayer:function(A,t,n){this._map&&A.on("add remove",this._onLayerChange,this),this._layers.push({layer:A,name:t,overlay:n}),this.options.sortLayers&&this._layers.sort(M(function(o,u){return this.options.sortFunction(o.layer,u.layer,o.name,u.name)},this)),this.options.autoZIndex&&A.setZIndex&&(this._lastZIndex++,A.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;gr(this._baseLayersList),gr(this._overlaysList),this._layerControlInputs=[];var A,t,n,o,u=0;for(n=0;n<this._layers.length;n++)o=this._layers[n],this._addItem(o),t=t||o.overlay,A=A||!o.overlay,u+=o.overlay?0:1;return this.options.hideSingleBase&&(A=A&&u>1,this._baseLayersList.style.display=A?"":"none"),this._separator.style.display=t&&A?"":"none",this},_onLayerChange:function(A){this._handlingClick||this._update();var t=this._getLayer(R(A.target)),n=t.overlay?A.type==="add"?"overlayadd":"overlayremove":A.type==="add"?"baselayerchange":null;n&&this._map.fire(n,t)},_createRadioElement:function(A,t){var n='<input type="radio" class="leaflet-control-layers-selector" name="'+A+'"'+(t?' checked="checked"':"")+"/>",o=document.createElement("div");return o.innerHTML=n,o.firstChild},_addItem:function(A){var t=document.createElement("label"),n=this._map.hasLayer(A.layer),o;A.overlay?(o=document.createElement("input"),o.type="checkbox",o.className="leaflet-control-layers-selector",o.defaultChecked=n):o=this._createRadioElement("leaflet-base-layers_"+R(this),n),this._layerControlInputs.push(o),o.layerId=R(A.layer),Z(o,"click",this._onInputClick,this);var u=document.createElement("span");u.innerHTML=" "+A.name;var l=document.createElement("span");t.appendChild(l),l.appendChild(o),l.appendChild(u);var g=A.overlay?this._overlaysList:this._baseLayersList;return g.appendChild(t),this._checkDisabledLayers(),t},_onInputClick:function(){if(!this._preventClick){var A=this._layerControlInputs,t,n,o=[],u=[];this._handlingClick=!0;for(var l=A.length-1;l>=0;l--)t=A[l],n=this._getLayer(t.layerId).layer,t.checked?o.push(n):t.checked||u.push(n);for(l=0;l<u.length;l++)this._map.hasLayer(u[l])&&this._map.removeLayer(u[l]);for(l=0;l<o.length;l++)this._map.hasLayer(o[l])||this._map.addLayer(o[l]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var A=this._layerControlInputs,t,n,o=this._map.getZoom(),u=A.length-1;u>=0;u--)t=A[u],n=this._getLayer(t.layerId).layer,t.disabled=n.options.minZoom!==void 0&&o<n.options.minZoom||n.options.maxZoom!==void 0&&o>n.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var A=this._section;this._preventClick=!0,Z(A,"click",FA),this.expand();var t=this;setTimeout(function(){aA(A,"click",FA),t._preventClick=!1})}}),jn=function(A,t,n){return new ki(A,t,n)},fe=nt.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(A){var t="leaflet-control-zoom",n=J("div",t+" leaflet-bar"),o=this.options;return this._zoomInButton=this._createButton(o.zoomInText,o.zoomInTitle,t+"-in",n,this._zoomIn),this._zoomOutButton=this._createButton(o.zoomOutText,o.zoomOutTitle,t+"-out",n,this._zoomOut),this._updateDisabled(),A.on("zoomend zoomlevelschange",this._updateDisabled,this),n},onRemove:function(A){A.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(A){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(A.shiftKey?3:1))},_zoomOut:function(A){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(A.shiftKey?3:1))},_createButton:function(A,t,n,o,u){var l=J("a",n,o);return l.innerHTML=A,l.href="#",l.title=t,l.setAttribute("role","button"),l.setAttribute("aria-label",t),le(l),Z(l,"click",Xt),Z(l,"click",u,this),Z(l,"click",this._refocusOnMap,this),l},_updateDisabled:function(){var A=this._map,t="leaflet-disabled";fA(this._zoomInButton,t),fA(this._zoomOutButton,t),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||A._zoom===A.getMinZoom())&&(G(this._zoomOutButton,t),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||A._zoom===A.getMaxZoom())&&(G(this._zoomInButton,t),this._zoomInButton.setAttribute("aria-disabled","true"))}});q.mergeOptions({zoomControl:!0}),q.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new fe,this.addControl(this.zoomControl))});var mr=function(A){return new fe(A)},Bi=nt.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(A){var t="leaflet-control-scale",n=J("div",t),o=this.options;return this._addScales(o,t+"-line",n),A.on(o.updateWhenIdle?"moveend":"move",this._update,this),A.whenReady(this._update,this),n},onRemove:function(A){A.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(A,t,n){A.metric&&(this._mScale=J("div",t,n)),A.imperial&&(this._iScale=J("div",t,n))},_update:function(){var A=this._map,t=A.getSize().y/2,n=A.distance(A.containerPointToLatLng([0,t]),A.containerPointToLatLng([this.options.maxWidth,t]));this._updateScales(n)},_updateScales:function(A){this.options.metric&&A&&this._updateMetric(A),this.options.imperial&&A&&this._updateImperial(A)},_updateMetric:function(A){var t=this._getRoundNum(A),n=t<1e3?t+" m":t/1e3+" km";this._updateScale(this._mScale,n,t/A)},_updateImperial:function(A){var t=A*3.2808399,n,o,u;t>5280?(n=t/5280,o=this._getRoundNum(n),this._updateScale(this._iScale,o+" mi",o/n)):(u=this._getRoundNum(t),this._updateScale(this._iScale,u+" ft",u/t))},_updateScale:function(A,t,n){A.style.width=Math.round(this.options.maxWidth*n)+"px",A.innerHTML=t},_getRoundNum:function(A){var t=Math.pow(10,(Math.floor(A)+"").length-1),n=A/t;return n=n>=10?10:n>=5?5:n>=3?3:n>=2?2:1,t*n}}),$n=function(A){return new Bi(A)},Lt='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',ge=nt.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(K.inlineSvg?Lt+" ":"")+"Leaflet</a>"},initialize:function(A){rA(this,A),this._attributions={}},onAdd:function(A){A.attributionControl=this,this._container=J("div","leaflet-control-attribution"),le(this._container);for(var t in A._layers)A._layers[t].getAttribution&&this.addAttribution(A._layers[t].getAttribution());return this._update(),A.on("layeradd",this._addAttribution,this),this._container},onRemove:function(A){A.off("layeradd",this._addAttribution,this)},_addAttribution:function(A){A.layer.getAttribution&&(this.addAttribution(A.layer.getAttribution()),A.layer.once("remove",function(){this.removeAttribution(A.layer.getAttribution())},this))},setPrefix:function(A){return this.options.prefix=A,this._update(),this},addAttribution:function(A){return A?(this._attributions[A]||(this._attributions[A]=0),this._attributions[A]++,this._update(),this):this},removeAttribution:function(A){return A?(this._attributions[A]&&(this._attributions[A]--,this._update()),this):this},_update:function(){if(this._map){var A=[];for(var t in this._attributions)this._attributions[t]&&A.push(t);var n=[];this.options.prefix&&n.push(this.options.prefix),A.length&&n.push(A.join(", ")),this._container.innerHTML=n.join(' <span aria-hidden="true">|</span> ')}}});q.mergeOptions({attributionControl:!0}),q.addInitHook(function(){this.options.attributionControl&&new ge().addTo(this)});var fi=function(A){return new ge(A)};nt.Layers=ki,nt.Zoom=fe,nt.Scale=Bi,nt.Attribution=ge,Pe.layers=jn,Pe.zoom=mr,Pe.scale=$n,Pe.attribution=fi;var ht=pt.extend({initialize:function(A){this._map=A},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});ht.addTo=function(A,t){return A.addHandler(t,this),this};var As={Events:ZA},Vi=K.touch?"touchstart mousedown":"mousedown",xt=ye.extend({options:{clickTolerance:3},initialize:function(A,t,n,o){rA(this,o),this._element=A,this._dragStartTarget=t||A,this._preventOutline=n},enable:function(){this._enabled||(Z(this._dragStartTarget,Vi,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(xt._dragging===this&&this.finishDrag(!0),aA(this._dragStartTarget,Vi,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(A){if(this._enabled&&(this._moved=!1,!he(this._element,"leaflet-zoom-anim"))){if(A.touches&&A.touches.length!==1){xt._dragging===this&&this.finishDrag();return}if(!(xt._dragging||A.shiftKey||A.which!==1&&A.button!==1&&!A.touches)&&(xt._dragging=this,this._preventOutline&&Ct(this._element),ni(),Ke(),!this._moving)){this.fire("down");var t=A.touches?A.touches[0]:A,n=Oi(this._element);this._startPoint=new V(t.clientX,t.clientY),this._startPos=Zt(this._element),this._parentScale=oi(n);var o=A.type==="mousedown";Z(document,o?"mousemove":"touchmove",this._onMove,this),Z(document,o?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(A){if(this._enabled){if(A.touches&&A.touches.length>1){this._moved=!0;return}var t=A.touches&&A.touches.length===1?A.touches[0]:A,n=new V(t.clientX,t.clientY)._subtract(this._startPoint);!n.x&&!n.y||Math.abs(n.x)+Math.abs(n.y)<this.options.clickTolerance||(n.x/=this._parentScale.x,n.y/=this._parentScale.y,FA(A),this._moved||(this.fire("dragstart"),this._moved=!0,G(document.body,"leaflet-dragging"),this._lastTarget=A.target||A.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),G(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(n),this._moving=!0,this._lastEvent=A,this._updatePosition())}},_updatePosition:function(){var A={originalEvent:this._lastEvent};this.fire("predrag",A),pA(this._element,this._newPos),this.fire("drag",A)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(A){fA(document.body,"leaflet-dragging"),this._lastTarget&&(fA(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),aA(document,"mousemove touchmove",this._onMove,this),aA(document,"mouseup touchend touchcancel",this._onUp,this),si(),Ht();var t=this._moved&&this._moving;this._moving=!1,xt._dragging=!1,t&&this.fire("dragend",{noInertia:A,distance:this._newPos.distanceTo(this._startPos)})}});function Zi(A,t,n){var o,u=[1,4,2,8],l,g,Q,m,_,F,D,z;for(l=0,F=A.length;l<F;l++)A[l]._code=zt(A[l],t);for(Q=0;Q<4;Q++){for(D=u[Q],o=[],l=0,F=A.length,g=F-1;l<F;g=l++)m=A[l],_=A[g],m._code&D?_._code&D||(z=Cr(_,m,D,t,n),z._code=zt(z,t),o.push(z)):(_._code&D&&(z=Cr(_,m,D,t,n),z._code=zt(z,t),o.push(z)),o.push(m));A=o}return A}function Xi(A,t){var n,o,u,l,g,Q,m,_,F;if(!A||A.length===0)throw new Error("latlngs not passed");YA(A)||(console.warn("latlngs are not flat! Only the first ring will be used"),A=A[0]);var D=Y([0,0]),z=wA(A),SA=z.getNorthWest().distanceTo(z.getSouthWest())*z.getNorthEast().distanceTo(z.getNorthWest());SA<1700&&(D=gi(A));var _A=A.length,$A=[];for(n=0;n<_A;n++){var kA=Y(A[n]);$A.push(t.project(Y([kA.lat-D.lat,kA.lng-D.lng])))}for(Q=m=_=0,n=0,o=_A-1;n<_A;o=n++)u=$A[n],l=$A[o],g=u.y*l.x-l.y*u.x,m+=(u.x+l.x)*g,_+=(u.y+l.y)*g,Q+=g*3;Q===0?F=$A[0]:F=[m/Q,_/Q];var me=t.unproject(N(F));return Y([me.lat+D.lat,me.lng+D.lng])}function gi(A){for(var t=0,n=0,o=0,u=0;u<A.length;u++){var l=Y(A[u]);t+=l.lat,n+=l.lng,o++}return Y([t/o,n/o])}var ts={__proto__:null,clipPolygon:Zi,polygonCenter:Xi,centroid:gi};function zi(A,t){if(!t||!A.length)return A.slice();var n=t*t;return A=is(A,n),A=rs(A,n),A}function Wi(A,t,n){return Math.sqrt(Oe(A,t,n,!0))}function es(A,t,n){return Oe(A,t,n)}function rs(A,t){var n=A.length,o=typeof Uint8Array<"u"?Uint8Array:Array,u=new o(n);u[0]=u[n-1]=1,de(A,u,t,0,n-1);var l,g=[];for(l=0;l<n;l++)u[l]&&g.push(A[l]);return g}function de(A,t,n,o,u){var l=0,g,Q,m;for(Q=o+1;Q<=u-1;Q++)m=Oe(A[Q],A[o],A[u],!0),m>l&&(g=Q,l=m);l>n&&(t[g]=1,de(A,t,n,o,g),de(A,t,n,g,u))}function is(A,t){for(var n=[A[0]],o=1,u=0,l=A.length;o<l;o++)ns(A[o],A[u])>t&&(n.push(A[o]),u=o);return u<l-1&&n.push(A[l-1]),n}var Ji;function Yi(A,t,n,o,u){var l=o?Ji:zt(A,n),g=zt(t,n),Q,m,_;for(Ji=g;;){if(!(l|g))return[A,t];if(l&g)return!1;Q=l||g,m=Cr(A,t,Q,n,u),_=zt(m,n),Q===l?(A=m,l=_):(t=m,g=_)}}function Cr(A,t,n,o,u){var l=t.x-A.x,g=t.y-A.y,Q=o.min,m=o.max,_,F;return n&8?(_=A.x+l*(m.y-A.y)/g,F=m.y):n&4?(_=A.x+l*(Q.y-A.y)/g,F=Q.y):n&2?(_=m.x,F=A.y+g*(m.x-A.x)/l):n&1&&(_=Q.x,F=A.y+g*(Q.x-A.x)/l),new V(_,F,u)}function zt(A,t){var n=0;return A.x<t.min.x?n|=1:A.x>t.max.x&&(n|=2),A.y<t.min.y?n|=4:A.y>t.max.y&&(n|=8),n}function ns(A,t){var n=t.x-A.x,o=t.y-A.y;return n*n+o*o}function Oe(A,t,n,o){var u=t.x,l=t.y,g=n.x-u,Q=n.y-l,m=g*g+Q*Q,_;return m>0&&(_=((A.x-u)*g+(A.y-l)*Q)/m,_>1?(u=n.x,l=n.y):_>0&&(u+=g*_,l+=Q*_)),g=A.x-u,Q=A.y-l,o?g*g+Q*Q:new V(u,l)}function YA(A){return!rt(A[0])||typeof A[0][0]!="object"&&typeof A[0][0]<"u"}function _r(A){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),YA(A)}function di(A,t){var n,o,u,l,g,Q,m,_;if(!A||A.length===0)throw new Error("latlngs not passed");YA(A)||(console.warn("latlngs are not flat! Only the first ring will be used"),A=A[0]);var F=Y([0,0]),D=wA(A),z=D.getNorthWest().distanceTo(D.getSouthWest())*D.getNorthEast().distanceTo(D.getNorthWest());z<1700&&(F=gi(A));var SA=A.length,_A=[];for(n=0;n<SA;n++){var $A=Y(A[n]);_A.push(t.project(Y([$A.lat-F.lat,$A.lng-F.lng])))}for(n=0,o=0;n<SA-1;n++)o+=_A[n].distanceTo(_A[n+1])/2;if(o===0)_=_A[0];else for(n=0,l=0;n<SA-1;n++)if(g=_A[n],Q=_A[n+1],u=g.distanceTo(Q),l+=u,l>o){m=(l-o)/u,_=[Q.x-m*(Q.x-g.x),Q.y-m*(Q.y-g.y)];break}var kA=t.unproject(N(_));return Y([kA.lat+F.lat,kA.lng+F.lng])}var qi={__proto__:null,simplify:zi,pointToSegmentDistance:Wi,closestPointOnSegment:es,clipSegment:Yi,_getEdgeIntersection:Cr,_getBitCode:zt,_sqClosestPointOnSegment:Oe,isFlat:YA,_flat:_r,polylineCenter:di},Wt={project:function(A){return new V(A.lng,A.lat)},unproject:function(A){return new iA(A.y,A.x)},bounds:new cA([-180,-90],[180,90])},bt={R:6378137,R_MINOR:6356752314245179e-9,bounds:new cA([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(A){var t=Math.PI/180,n=this.R,o=A.lat*t,u=this.R_MINOR/n,l=Math.sqrt(1-u*u),g=l*Math.sin(o),Q=Math.tan(Math.PI/4-o/2)/Math.pow((1-g)/(1+g),l/2);return o=-n*Math.log(Math.max(Q,1e-10)),new V(A.lng*t*n,o)},unproject:function(A){for(var t=180/Math.PI,n=this.R,o=this.R_MINOR/n,u=Math.sqrt(1-o*o),l=Math.exp(-A.y/n),g=Math.PI/2-2*Math.atan(l),Q=0,m=.1,_;Q<15&&Math.abs(m)>1e-7;Q++)_=u*Math.sin(g),_=Math.pow((1-_)/(1+_),u/2),m=Math.PI/2-2*Math.atan(l*_)-g,g+=m;return new iA(g*t,A.x*t/n)}},nA={__proto__:null,LonLat:Wt,Mercator:bt,SphericalMercator:Vr},ss=E({},yt,{code:"EPSG:3395",projection:bt,transformation:function(){var A=.5/(Math.PI*bt.R);return re(A,.5,-A,.5)}()}),vr=E({},yt,{code:"EPSG:4326",projection:Wt,transformation:re(1/180,1,-1/180,.5)}),ji=E({},zA,{projection:Wt,transformation:re(1,0,-1,0),scale:function(A){return Math.pow(2,A)},zoom:function(A){return Math.log(A)/Math.LN2},distance:function(A,t){var n=t.lng-A.lng,o=t.lat-A.lat;return Math.sqrt(n*n+o*o)},infinite:!0});zA.Earth=yt,zA.EPSG3395=ss,zA.EPSG3857=Zr,zA.EPSG900913=Nn,zA.EPSG4326=vr,zA.Simple=ji;var xA=ye.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(A){return A.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(A){return A&&A.removeLayer(this),this},getPane:function(A){return this._map.getPane(A?this.options[A]||A:this.options.pane)},addInteractiveTarget:function(A){return this._map._targets[R(A)]=this,this},removeInteractiveTarget:function(A){return delete this._map._targets[R(A)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(A){var t=A.target;if(t.hasLayer(this)){if(this._map=t,this._zoomAnimated=t._zoomAnimated,this.getEvents){var n=this.getEvents();t.on(n,this),this.once("remove",function(){t.off(n,this)},this)}this.onAdd(t),this.fire("add"),t.fire("layeradd",{layer:this})}}});q.include({addLayer:function(A){if(!A._layerAdd)throw new Error("The provided object is not a Layer.");var t=R(A);return this._layers[t]?this:(this._layers[t]=A,A._mapToAdd=this,A.beforeAdd&&A.beforeAdd(this),this.whenReady(A._layerAdd,A),this)},removeLayer:function(A){var t=R(A);return this._layers[t]?(this._loaded&&A.onRemove(this),delete this._layers[t],this._loaded&&(this.fire("layerremove",{layer:A}),A.fire("remove")),A._map=A._mapToAdd=null,this):this},hasLayer:function(A){return R(A)in this._layers},eachLayer:function(A,t){for(var n in this._layers)A.call(t,this._layers[n]);return this},_addLayers:function(A){A=A?rt(A)?A:[A]:[];for(var t=0,n=A.length;t<n;t++)this.addLayer(A[t])},_addZoomLimit:function(A){(!isNaN(A.options.maxZoom)||!isNaN(A.options.minZoom))&&(this._zoomBoundLayers[R(A)]=A,this._updateZoomLevels())},_removeZoomLimit:function(A){var t=R(A);this._zoomBoundLayers[t]&&(delete this._zoomBoundLayers[t],this._updateZoomLevels())},_updateZoomLevels:function(){var A=1/0,t=-1/0,n=this._getZoomSpan();for(var o in this._zoomBoundLayers){var u=this._zoomBoundLayers[o].options;A=u.minZoom===void 0?A:Math.min(A,u.minZoom),t=u.maxZoom===void 0?t:Math.max(t,u.maxZoom)}this._layersMaxZoom=t===-1/0?void 0:t,this._layersMinZoom=A===1/0?void 0:A,n!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var KA=xA.extend({initialize:function(A,t){rA(this,t),this._layers={};var n,o;if(A)for(n=0,o=A.length;n<o;n++)this.addLayer(A[n])},addLayer:function(A){var t=this.getLayerId(A);return this._layers[t]=A,this._map&&this._map.addLayer(A),this},removeLayer:function(A){var t=A in this._layers?A:this.getLayerId(A);return this._map&&this._layers[t]&&this._map.removeLayer(this._layers[t]),delete this._layers[t],this},hasLayer:function(A){var t=typeof A=="number"?A:this.getLayerId(A);return t in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(A){var t=Array.prototype.slice.call(arguments,1),n,o;for(n in this._layers)o=this._layers[n],o[A]&&o[A].apply(o,t);return this},onAdd:function(A){this.eachLayer(A.addLayer,A)},onRemove:function(A){this.eachLayer(A.removeLayer,A)},eachLayer:function(A,t){for(var n in this._layers)A.call(t,this._layers[n]);return this},getLayer:function(A){return this._layers[A]},getLayers:function(){var A=[];return this.eachLayer(A.push,A),A},setZIndex:function(A){return this.invoke("setZIndex",A)},getLayerId:function(A){return R(A)}}),os=function(A,t){return new KA(A,t)},bA=KA.extend({addLayer:function(A){return this.hasLayer(A)?this:(A.addEventParent(this),KA.prototype.addLayer.call(this,A),this.fire("layeradd",{layer:A}))},removeLayer:function(A){return this.hasLayer(A)?(A in this._layers&&(A=this._layers[A]),A.removeEventParent(this),KA.prototype.removeLayer.call(this,A),this.fire("layerremove",{layer:A})):this},setStyle:function(A){return this.invoke("setStyle",A)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var A=new NA;for(var t in this._layers){var n=this._layers[t];A.extend(n.getBounds?n.getBounds():n.getLatLng())}return A}}),QA=function(A,t){return new bA(A,t)},Jt=pt.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(A){rA(this,A)},createIcon:function(A){return this._createIcon("icon",A)},createShadow:function(A){return this._createIcon("shadow",A)},_createIcon:function(A,t){var n=this._getIconUrl(A);if(!n){if(A==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var o=this._createImg(n,t&&t.tagName==="IMG"?t:null);return this._setIconStyles(o,A),(this.options.crossOrigin||this.options.crossOrigin==="")&&(o.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),o},_setIconStyles:function(A,t){var n=this.options,o=n[t+"Size"];typeof o=="number"&&(o=[o,o]);var u=N(o),l=N(t==="shadow"&&n.shadowAnchor||n.iconAnchor||u&&u.divideBy(2,!0));A.className="leaflet-marker-"+t+" "+(n.className||""),l&&(A.style.marginLeft=-l.x+"px",A.style.marginTop=-l.y+"px"),u&&(A.style.width=u.x+"px",A.style.height=u.y+"px")},_createImg:function(A,t){return t=t||document.createElement("img"),t.src=A,t},_getIconUrl:function(A){return K.retina&&this.options[A+"RetinaUrl"]||this.options[A+"Url"]}});function EA(A){return new Jt(A)}var Yt=Jt.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(A){return typeof Yt.imagePath!="string"&&(Yt.imagePath=this._detectIconPath()),(this.options.imagePath||Yt.imagePath)+Jt.prototype._getIconUrl.call(this,A)},_stripUrl:function(A){var t=function(n,o,u){var l=o.exec(n);return l&&l[u]};return A=t(A,/^url\((['"])?(.+)\1\)$/,2),A&&t(A,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var A=J("div","leaflet-default-icon-path",document.body),t=Et(A,"background-image")||Et(A,"backgroundImage");if(document.body.removeChild(A),t=this._stripUrl(t),t)return t;var n=document.querySelector('link[href$="leaflet.css"]');return n?n.href.substring(0,n.href.length-11-1):""}}),_t=ht.extend({initialize:function(A){this._marker=A},addHooks:function(){var A=this._marker._icon;this._draggable||(this._draggable=new xt(A,A,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),G(A,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&fA(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(A){var t=this._marker,n=t._map,o=this._marker.options.autoPanSpeed,u=this._marker.options.autoPanPadding,l=Zt(t._icon),g=n.getPixelBounds(),Q=n.getPixelOrigin(),m=RA(g.min._subtract(Q).add(u),g.max._subtract(Q).subtract(u));if(!m.contains(l)){var _=N((Math.max(m.max.x,l.x)-m.max.x)/(g.max.x-m.max.x)-(Math.min(m.min.x,l.x)-m.min.x)/(g.min.x-m.min.x),(Math.max(m.max.y,l.y)-m.max.y)/(g.max.y-m.max.y)-(Math.min(m.min.y,l.y)-m.min.y)/(g.min.y-m.min.y)).multiplyBy(o);n.panBy(_,{animate:!1}),this._draggable._newPos._add(_),this._draggable._startPos._add(_),pA(t._icon,this._draggable._newPos),this._onDrag(A),this._panRequest=PA(this._adjustPan.bind(this,A))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(A){this._marker.options.autoPan&&(OA(this._panRequest),this._panRequest=PA(this._adjustPan.bind(this,A)))},_onDrag:function(A){var t=this._marker,n=t._shadow,o=Zt(t._icon),u=t._map.layerPointToLatLng(o);n&&pA(n,o),t._latlng=u,A.latlng=u,A.oldLatLng=this._oldLatLng,t.fire("move",A).fire("drag",A)},_onDragEnd:function(A){OA(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",A)}}),Tt=xA.extend({options:{icon:new Yt,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(A,t){rA(this,t),this._latlng=Y(A)},onAdd:function(A){this._zoomAnimated=this._zoomAnimated&&A.options.markerZoomAnimation,this._zoomAnimated&&A.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(A){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&A.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(A){var t=this._latlng;return this._latlng=Y(A),this.update(),this.fire("move",{oldLatLng:t,latlng:this._latlng})},setZIndexOffset:function(A){return this.options.zIndexOffset=A,this.update()},getIcon:function(){return this.options.icon},setIcon:function(A){return this.options.icon=A,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var A=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(A)}return this},_initIcon:function(){var A=this.options,t="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),n=A.icon.createIcon(this._icon),o=!1;n!==this._icon&&(this._icon&&this._removeIcon(),o=!0,A.title&&(n.title=A.title),n.tagName==="IMG"&&(n.alt=A.alt||"")),G(n,t),A.keyboard&&(n.tabIndex="0",n.setAttribute("role","button")),this._icon=n,A.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&Z(n,"focus",this._panOnFocus,this);var u=A.icon.createShadow(this._shadow),l=!1;u!==this._shadow&&(this._removeShadow(),l=!0),u&&(G(u,t),u.alt=""),this._shadow=u,A.opacity<1&&this._updateOpacity(),o&&this.getPane().appendChild(this._icon),this._initInteraction(),u&&l&&this.getPane(A.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&aA(this._icon,"focus",this._panOnFocus,this),lA(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&lA(this._shadow),this._shadow=null},_setPos:function(A){this._icon&&pA(this._icon,A),this._shadow&&pA(this._shadow,A),this._zIndex=A.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(A){this._icon&&(this._icon.style.zIndex=this._zIndex+A)},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center).round();this._setPos(t)},_initInteraction:function(){if(this.options.interactive&&(G(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),_t)){var A=this.options.draggable;this.dragging&&(A=this.dragging.enabled(),this.dragging.disable()),this.dragging=new _t(this),A&&this.dragging.enable()}},setOpacity:function(A){return this.options.opacity=A,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var A=this.options.opacity;this._icon&&WA(this._icon,A),this._shadow&&WA(this._shadow,A)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var A=this._map;if(A){var t=this.options.icon.options,n=t.iconSize?N(t.iconSize):N(0,0),o=t.iconAnchor?N(t.iconAnchor):N(0,0);A.panInside(this._latlng,{paddingTopLeft:o,paddingBottomRight:n.subtract(o)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function uA(A,t){return new Tt(A,t)}var vt=xA.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(A){this._renderer=A.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(A){return rA(this,A),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&A&&Object.prototype.hasOwnProperty.call(A,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Re=vt.extend({options:{fill:!0,radius:10},initialize:function(A,t){rA(this,t),this._latlng=Y(A),this._radius=this.options.radius},setLatLng:function(A){var t=this._latlng;return this._latlng=Y(A),this.redraw(),this.fire("move",{oldLatLng:t,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(A){return this.options.radius=this._radius=A,this.redraw()},getRadius:function(){return this._radius},setStyle:function(A){var t=A&&A.radius||this._radius;return vt.prototype.setStyle.call(this,A),this.setRadius(t),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var A=this._radius,t=this._radiusY||A,n=this._clickTolerance(),o=[A+n,t+n];this._pxBounds=new cA(this._point.subtract(o),this._point.add(o))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(A){return A.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function $i(A,t){return new Re(A,t)}var Ur=Re.extend({initialize:function(A,t,n){if(typeof t=="number"&&(t=E({},n,{radius:t})),rA(this,t),this._latlng=Y(A),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(A){return this._mRadius=A,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var A=[this._radius,this._radiusY||this._radius];return new NA(this._map.layerPointToLatLng(this._point.subtract(A)),this._map.layerPointToLatLng(this._point.add(A)))},setStyle:vt.prototype.setStyle,_project:function(){var A=this._latlng.lng,t=this._latlng.lat,n=this._map,o=n.options.crs;if(o.distance===yt.distance){var u=Math.PI/180,l=this._mRadius/yt.R/u,g=n.project([t+l,A]),Q=n.project([t-l,A]),m=g.add(Q).divideBy(2),_=n.unproject(m).lat,F=Math.acos((Math.cos(l*u)-Math.sin(t*u)*Math.sin(_*u))/(Math.cos(t*u)*Math.cos(_*u)))/u;(isNaN(F)||F===0)&&(F=l/Math.cos(Math.PI/180*t)),this._point=m.subtract(n.getPixelOrigin()),this._radius=isNaN(F)?0:m.x-n.project([_,A-F]).x,this._radiusY=m.y-g.y}else{var D=o.unproject(o.project(this._latlng).subtract([this._mRadius,0]));this._point=n.latLngToLayerPoint(this._latlng),this._radius=this._point.x-n.latLngToLayerPoint(D).x}this._updateBounds()}});function Fr(A,t,n){return new Ur(A,t,n)}var ct=vt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(A,t){rA(this,t),this._setLatLngs(A)},getLatLngs:function(){return this._latlngs},setLatLngs:function(A){return this._setLatLngs(A),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(A){for(var t=1/0,n=null,o=Oe,u,l,g=0,Q=this._parts.length;g<Q;g++)for(var m=this._parts[g],_=1,F=m.length;_<F;_++){u=m[_-1],l=m[_];var D=o(A,u,l,!0);D<t&&(t=D,n=o(A,u,l))}return n&&(n.distance=Math.sqrt(t)),n},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return di(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(A,t){return t=t||this._defaultShape(),A=Y(A),t.push(A),this._bounds.extend(A),this.redraw()},_setLatLngs:function(A){this._bounds=new NA,this._latlngs=this._convertLatLngs(A)},_defaultShape:function(){return YA(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(A){for(var t=[],n=YA(A),o=0,u=A.length;o<u;o++)n?(t[o]=Y(A[o]),this._bounds.extend(t[o])):t[o]=this._convertLatLngs(A[o]);return t},_project:function(){var A=new cA;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,A),this._bounds.isValid()&&A.isValid()&&(this._rawPxBounds=A,this._updateBounds())},_updateBounds:function(){var A=this._clickTolerance(),t=new V(A,A);this._rawPxBounds&&(this._pxBounds=new cA([this._rawPxBounds.min.subtract(t),this._rawPxBounds.max.add(t)]))},_projectLatlngs:function(A,t,n){var o=A[0]instanceof iA,u=A.length,l,g;if(o){for(g=[],l=0;l<u;l++)g[l]=this._map.latLngToLayerPoint(A[l]),n.extend(g[l]);t.push(g)}else for(l=0;l<u;l++)this._projectLatlngs(A[l],t,n)},_clipPoints:function(){var A=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(A))){if(this.options.noClip){this._parts=this._rings;return}var t=this._parts,n,o,u,l,g,Q,m;for(n=0,u=0,l=this._rings.length;n<l;n++)for(m=this._rings[n],o=0,g=m.length;o<g-1;o++)Q=Yi(m[o],m[o+1],A,o,!0),Q&&(t[u]=t[u]||[],t[u].push(Q[0]),(Q[1]!==m[o+1]||o===g-2)&&(t[u].push(Q[1]),u++))}},_simplifyPoints:function(){for(var A=this._parts,t=this.options.smoothFactor,n=0,o=A.length;n<o;n++)A[n]=zi(A[n],t)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(A,t){var n,o,u,l,g,Q,m=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(A))return!1;for(n=0,l=this._parts.length;n<l;n++)for(Q=this._parts[n],o=0,g=Q.length,u=g-1;o<g;u=o++)if(!(!t&&o===0)&&Wi(A,Q[u],Q[o])<=m)return!0;return!1}});function An(A,t){return new ct(A,t)}ct._flat=_r;var HA=ct.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Xi(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(A){var t=ct.prototype._convertLatLngs.call(this,A),n=t.length;return n>=2&&t[0]instanceof iA&&t[0].equals(t[n-1])&&t.pop(),t},_setLatLngs:function(A){ct.prototype._setLatLngs.call(this,A),YA(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return YA(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var A=this._renderer._bounds,t=this.options.weight,n=new V(t,t);if(A=new cA(A.min.subtract(n),A.max.add(n)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(A))){if(this.options.noClip){this._parts=this._rings;return}for(var o=0,u=this._rings.length,l;o<u;o++)l=Zi(this._rings[o],A,!0),l.length&&this._parts.push(l)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(A){var t=!1,n,o,u,l,g,Q,m,_;if(!this._pxBounds||!this._pxBounds.contains(A))return!1;for(l=0,m=this._parts.length;l<m;l++)for(n=this._parts[l],g=0,_=n.length,Q=_-1;g<_;Q=g++)o=n[g],u=n[Q],o.y>A.y!=u.y>A.y&&A.x<(u.x-o.x)*(A.y-o.y)/(u.y-o.y)+o.x&&(t=!t);return t||ct.prototype._containsPoint.call(this,A,!0)}});function Kt(A,t){return new HA(A,t)}var TA=bA.extend({initialize:function(A,t){rA(this,t),this._layers={},A&&this.addData(A)},addData:function(A){var t=rt(A)?A:A.features,n,o,u;if(t){for(n=0,o=t.length;n<o;n++)u=t[n],(u.geometries||u.geometry||u.features||u.coordinates)&&this.addData(u);return this}var l=this.options;if(l.filter&&!l.filter(A))return this;var g=gA(A,l);return g?(g.feature=St(A),g.defaultOptions=g.options,this.resetStyle(g),l.onEachFeature&&l.onEachFeature(A,g),this.addLayer(g)):this},resetStyle:function(A){return A===void 0?this.eachLayer(this.resetStyle,this):(A.options=E({},A.defaultOptions),this._setLayerStyle(A,this.options.style),this)},setStyle:function(A){return this.eachLayer(function(t){this._setLayerStyle(t,A)},this)},_setLayerStyle:function(A,t){A.setStyle&&(typeof t=="function"&&(t=t(A.feature)),A.setStyle(t))}});function gA(A,t){var n=A.type==="Feature"?A.geometry:A,o=n?n.coordinates:null,u=[],l=t&&t.pointToLayer,g=t&&t.coordsToLatLng||yr,Q,m,_,F;if(!o&&!n)return null;switch(n.type){case"Point":return Q=g(o),Ut(l,A,Q,t);case"MultiPoint":for(_=0,F=o.length;_<F;_++)Q=g(o[_]),u.push(Ut(l,A,Q,t));return new bA(u);case"LineString":case"MultiLineString":return m=Ne(o,n.type==="LineString"?0:1,g),new ct(m,t);case"Polygon":case"MultiPolygon":return m=Ne(o,n.type==="Polygon"?1:2,g),new HA(m,t);case"GeometryCollection":for(_=0,F=n.geometries.length;_<F;_++){var D=gA({geometry:n.geometries[_],type:"Feature",properties:A.properties},t);D&&u.push(D)}return new bA(u);case"FeatureCollection":for(_=0,F=n.features.length;_<F;_++){var z=gA(n.features[_],t);z&&u.push(z)}return new bA(u);default:throw new Error("Invalid GeoJSON object.")}}function Ut(A,t,n,o){return A?A(t,n):new Tt(n,o&&o.markersInheritOptions&&o)}function yr(A){return new iA(A[1],A[0],A[2])}function Ne(A,t,n){for(var o=[],u=0,l=A.length,g;u<l;u++)g=t?Ne(A[u],t-1,n):(n||yr)(A[u]),o.push(g);return o}function Ge(A,t){return A=Y(A),A.alt!==void 0?[$(A.lng,t),$(A.lat,t),$(A.alt,t)]:[$(A.lng,t),$(A.lat,t)]}function ke(A,t,n,o){for(var u=[],l=0,g=A.length;l<g;l++)u.push(t?ke(A[l],YA(A[l])?0:t-1,n,o):Ge(A[l],o));return!t&&n&&u.length>0&&u.push(u[0].slice()),u}function we(A,t){return A.feature?E({},A.feature,{geometry:t}):St(t)}function St(A){return A.type==="Feature"||A.type==="FeatureCollection"?A:{type:"Feature",properties:{},geometry:A}}var st={toGeoJSON:function(A){return we(this,{type:"Point",coordinates:Ge(this.getLatLng(),A)})}};Tt.include(st),Ur.include(st),Re.include(st),ct.include({toGeoJSON:function(A){var t=!YA(this._latlngs),n=ke(this._latlngs,t?1:0,!1,A);return we(this,{type:(t?"Multi":"")+"LineString",coordinates:n})}}),HA.include({toGeoJSON:function(A){var t=!YA(this._latlngs),n=t&&!YA(this._latlngs[0]),o=ke(this._latlngs,n?2:t?1:0,!0,A);return t||(o=[o]),we(this,{type:(n?"Multi":"")+"Polygon",coordinates:o})}}),KA.include({toMultiPoint:function(A){var t=[];return this.eachLayer(function(n){t.push(n.toGeoJSON(A).geometry.coordinates)}),we(this,{type:"MultiPoint",coordinates:t})},toGeoJSON:function(A){var t=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(t==="MultiPoint")return this.toMultiPoint(A);var n=t==="GeometryCollection",o=[];return this.eachLayer(function(u){if(u.toGeoJSON){var l=u.toGeoJSON(A);if(n)o.push(l.geometry);else{var g=St(l);g.type==="FeatureCollection"?o.push.apply(o,g.features):o.push(g)}}}),n?we(this,{geometries:o,type:"GeometryCollection"}):{type:"FeatureCollection",features:o}}});function tn(A,t){return new TA(A,t)}var as=tn,qt=xA.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(A,t,n){this._url=A,this._bounds=wA(t),rA(this,n)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(G(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){lA(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(A){return this.options.opacity=A,this._image&&this._updateOpacity(),this},setStyle:function(A){return A.opacity&&this.setOpacity(A.opacity),this},bringToFront:function(){return this._map&&ue(this._image),this},bringToBack:function(){return this._map&&mt(this._image),this},setUrl:function(A){return this._url=A,this._image&&(this._image.src=A),this},setBounds:function(A){return this._bounds=wA(A),this._map&&this._reset(),this},getEvents:function(){var A={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},setZIndex:function(A){return this.options.zIndex=A,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var A=this._url.tagName==="IMG",t=this._image=A?this._url:J("img");if(G(t,"leaflet-image-layer"),this._zoomAnimated&&G(t,"leaflet-zoom-animated"),this.options.className&&G(t,this.options.className),t.onselectstart=sA,t.onmousemove=sA,t.onload=M(this.fire,this,"load"),t.onerror=M(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(t.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),A){this._url=t.src;return}t.src=this._url,t.alt=this.options.alt},_animateZoom:function(A){var t=this._map.getZoomScale(A.zoom),n=this._map._latLngBoundsToNewLayerBounds(this._bounds,A.zoom,A.center).min;Vt(this._image,n,t)},_reset:function(){var A=this._image,t=new cA(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),n=t.getSize();pA(A,t.min),A.style.width=n.x+"px",A.style.height=n.y+"px"},_updateOpacity:function(){WA(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var A=this.options.errorOverlayUrl;A&&this._url!==A&&(this._url=A,this._image.src=A)},getCenter:function(){return this._bounds.getCenter()}}),en=function(A,t,n){return new qt(A,t,n)},rn=qt.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var A=this._url.tagName==="VIDEO",t=this._image=A?this._url:J("video");if(G(t,"leaflet-image-layer"),this._zoomAnimated&&G(t,"leaflet-zoom-animated"),this.options.className&&G(t,this.options.className),t.onselectstart=sA,t.onmousemove=sA,t.onloadeddata=M(this.fire,this,"load"),A){for(var n=t.getElementsByTagName("source"),o=[],u=0;u<n.length;u++)o.push(n[u].src);this._url=n.length>0?o:[t.src];return}rt(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(t.style,"objectFit")&&(t.style.objectFit="fill"),t.autoplay=!!this.options.autoplay,t.loop=!!this.options.loop,t.muted=!!this.options.muted,t.playsInline=!!this.options.playsInline;for(var l=0;l<this._url.length;l++){var g=J("source");g.src=this._url[l],t.appendChild(g)}}});function us(A,t,n){return new rn(A,t,n)}var qA=qt.extend({_initImage:function(){var A=this._image=this._url;G(A,"leaflet-image-layer"),this._zoomAnimated&&G(A,"leaflet-zoom-animated"),this.options.className&&G(A,this.options.className),A.onselectstart=sA,A.onmousemove=sA}});function nn(A,t,n){return new qA(A,t,n)}var lt=xA.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(A,t){A&&(A instanceof iA||rt(A))?(this._latlng=Y(A),rA(this,t)):(rA(this,A),this._source=t),this.options.content&&(this._content=this.options.content)},openOn:function(A){return A=arguments.length?A:this._source._map,A.hasLayer(this)||A.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(A){return this._map?this.close():(arguments.length?this._source=A:A=this._source,this._prepareOpen(),this.openOn(A._map)),this},onAdd:function(A){this._zoomAnimated=A._zoomAnimated,this._container||this._initLayout(),A._fadeAnimated&&WA(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),A._fadeAnimated&&WA(this._container,1),this.bringToFront(),this.options.interactive&&(G(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(A){A._fadeAnimated?(WA(this._container,0),this._removeTimeout=setTimeout(M(lA,void 0,this._container),200)):lA(this._container),this.options.interactive&&(fA(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(A){return this._latlng=Y(A),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(A){return this._content=A,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var A={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&ue(this._container),this},bringToBack:function(){return this._map&&mt(this._container),this},_prepareOpen:function(A){var t=this._source;if(!t._map)return!1;if(t instanceof bA){t=null;var n=this._source._layers;for(var o in n)if(n[o]._map){t=n[o];break}if(!t)return!1;this._source=t}if(!A)if(t.getCenter)A=t.getCenter();else if(t.getLatLng)A=t.getLatLng();else if(t.getBounds)A=t.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(A),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var A=this._contentNode,t=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof t=="string")A.innerHTML=t;else{for(;A.hasChildNodes();)A.removeChild(A.firstChild);A.appendChild(t)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var A=this._map.latLngToLayerPoint(this._latlng),t=N(this.options.offset),n=this._getAnchor();this._zoomAnimated?pA(this._container,A.add(n)):t=t.add(A).add(n);var o=this._containerBottom=-t.y,u=this._containerLeft=-Math.round(this._containerWidth/2)+t.x;this._container.style.bottom=o+"px",this._container.style.left=u+"px"}},_getAnchor:function(){return[0,0]}});q.include({_initOverlay:function(A,t,n,o){var u=t;return u instanceof A||(u=new A(o).setContent(t)),n&&u.setLatLng(n),u}}),xA.include({_initOverlay:function(A,t,n,o){var u=n;return u instanceof A?(rA(u,o),u._source=this):(u=t&&!o?t:new A(o,this),u.setContent(n)),u}});var Er=lt.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(A){return A=arguments.length?A:this._source._map,!A.hasLayer(this)&&A._popup&&A._popup.options.autoClose&&A.removeLayer(A._popup),A._popup=this,lt.prototype.openOn.call(this,A)},onAdd:function(A){lt.prototype.onAdd.call(this,A),A.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof vt||this._source.on("preclick",It))},onRemove:function(A){lt.prototype.onRemove.call(this,A),A.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof vt||this._source.off("preclick",It))},getEvents:function(){var A=lt.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(A.preclick=this.close),this.options.keepInView&&(A.moveend=this._adjustPan),A},_initLayout:function(){var A="leaflet-popup",t=this._container=J("div",A+" "+(this.options.className||"")+" leaflet-zoom-animated"),n=this._wrapper=J("div",A+"-content-wrapper",t);if(this._contentNode=J("div",A+"-content",n),le(t),Qr(this._contentNode),Z(t,"contextmenu",It),this._tipContainer=J("div",A+"-tip-container",t),this._tip=J("div",A+"-tip",this._tipContainer),this.options.closeButton){var o=this._closeButton=J("a",A+"-close-button",t);o.setAttribute("role","button"),o.setAttribute("aria-label","Close popup"),o.href="#close",o.innerHTML='<span aria-hidden="true">&#215;</span>',Z(o,"click",function(u){FA(u),this.close()},this)}},_updateLayout:function(){var A=this._contentNode,t=A.style;t.width="",t.whiteSpace="nowrap";var n=A.offsetWidth;n=Math.min(n,this.options.maxWidth),n=Math.max(n,this.options.minWidth),t.width=n+1+"px",t.whiteSpace="",t.height="";var o=A.offsetHeight,u=this.options.maxHeight,l="leaflet-popup-scrolled";u&&o>u?(t.height=u+"px",G(A,l)):fA(A,l),this._containerWidth=this._container.offsetWidth},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center),n=this._getAnchor();pA(this._container,t.add(n))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var A=this._map,t=parseInt(Et(this._container,"marginBottom"),10)||0,n=this._container.offsetHeight+t,o=this._containerWidth,u=new V(this._containerLeft,-n-this._containerBottom);u._add(Zt(this._container));var l=A.layerPointToContainerPoint(u),g=N(this.options.autoPanPadding),Q=N(this.options.autoPanPaddingTopLeft||g),m=N(this.options.autoPanPaddingBottomRight||g),_=A.getSize(),F=0,D=0;l.x+o+m.x>_.x&&(F=l.x+o-_.x+m.x),l.x-F-Q.x<0&&(F=l.x-Q.x),l.y+n+m.y>_.y&&(D=l.y+n-_.y+m.y),l.y-D-Q.y<0&&(D=l.y-Q.y),(F||D)&&(this.options.keepInView&&(this._autopanning=!0),A.fire("autopanstart").panBy([F,D]))}},_getAnchor:function(){return N(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Hr=function(A,t){return new Er(A,t)};q.mergeOptions({closePopupOnClick:!0}),q.include({openPopup:function(A,t,n){return this._initOverlay(Er,A,t,n).openOn(this),this},closePopup:function(A){return A=arguments.length?A:this._popup,A&&A.close(),this}}),xA.include({bindPopup:function(A,t){return this._popup=this._initOverlay(Er,this._popup,A,t),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(A){return this._popup&&(this instanceof bA||(this._popup._source=this),this._popup._prepareOpen(A||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(A){return this._popup&&this._popup.setContent(A),this},getPopup:function(){return this._popup},_openPopup:function(A){if(!(!this._popup||!this._map)){Xt(A);var t=A.layer||A.target;if(this._popup._source===t&&!(t instanceof vt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(A.latlng);return}this._popup._source=t,this.openPopup(A.latlng)}},_movePopup:function(A){this._popup.setLatLng(A.latlng)},_onKeyPress:function(A){A.originalEvent.keyCode===13&&this._openPopup(A)}});var Ir=lt.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(A){lt.prototype.onAdd.call(this,A),this.setOpacity(this.options.opacity),A.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(A){lt.prototype.onRemove.call(this,A),A.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var A=lt.prototype.getEvents.call(this);return this.options.permanent||(A.preclick=this.close),A},_initLayout:function(){var A="leaflet-tooltip",t=A+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=J("div",t),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+R(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(A){var t,n,o=this._map,u=this._container,l=o.latLngToContainerPoint(o.getCenter()),g=o.layerPointToContainerPoint(A),Q=this.options.direction,m=u.offsetWidth,_=u.offsetHeight,F=N(this.options.offset),D=this._getAnchor();Q==="top"?(t=m/2,n=_):Q==="bottom"?(t=m/2,n=0):Q==="center"?(t=m/2,n=_/2):Q==="right"?(t=0,n=_/2):Q==="left"?(t=m,n=_/2):g.x<l.x?(Q="right",t=0,n=_/2):(Q="left",t=m+(F.x+D.x)*2,n=_/2),A=A.subtract(N(t,n,!0)).add(F).add(D),fA(u,"leaflet-tooltip-right"),fA(u,"leaflet-tooltip-left"),fA(u,"leaflet-tooltip-top"),fA(u,"leaflet-tooltip-bottom"),G(u,"leaflet-tooltip-"+Q),pA(u,A)},_updatePosition:function(){var A=this._map.latLngToLayerPoint(this._latlng);this._setPosition(A)},setOpacity:function(A){this.options.opacity=A,this._container&&WA(this._container,A)},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center);this._setPosition(t)},_getAnchor:function(){return N(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),sn=function(A,t){return new Ir(A,t)};q.include({openTooltip:function(A,t,n){return this._initOverlay(Ir,A,t,n).openOn(this),this},closeTooltip:function(A){return A.close(),this}}),xA.include({bindTooltip:function(A,t){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Ir,this._tooltip,A,t),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(A){if(!(!A&&this._tooltipHandlersAdded)){var t=A?"off":"on",n={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?n.add=this._openTooltip:(n.mouseover=this._openTooltip,n.mouseout=this.closeTooltip,n.click=this._openTooltip,this._map?this._addFocusListeners():n.add=this._addFocusListeners),this._tooltip.options.sticky&&(n.mousemove=this._moveTooltip),this[t](n),this._tooltipHandlersAdded=!A}},openTooltip:function(A){return this._tooltip&&(this instanceof bA||(this._tooltip._source=this),this._tooltip._prepareOpen(A)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(A){return this._tooltip&&this._tooltip.setContent(A),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(A){var t=typeof A.getElement=="function"&&A.getElement();t&&(Z(t,"focus",function(){this._tooltip._source=A,this.openTooltip()},this),Z(t,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(A){var t=typeof A.getElement=="function"&&A.getElement();t&&t.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(A){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var t=this;this._map.once("moveend",function(){t._openOnceFlag=!1,t._openTooltip(A)});return}this._tooltip._source=A.layer||A.target,this.openTooltip(this._tooltip.options.sticky?A.latlng:void 0)}},_moveTooltip:function(A){var t=A.latlng,n,o;this._tooltip.options.sticky&&A.originalEvent&&(n=this._map.mouseEventToContainerPoint(A.originalEvent),o=this._map.containerPointToLayerPoint(n),t=this._map.layerPointToLatLng(o)),this._tooltip.setLatLng(t)}});var wi=Jt.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(A){var t=A&&A.tagName==="DIV"?A:document.createElement("div"),n=this.options;if(n.html instanceof Element?(gr(t),t.appendChild(n.html)):t.innerHTML=n.html!==!1?n.html:"",n.bgPos){var o=N(n.bgPos);t.style.backgroundPosition=-o.x+"px "+-o.y+"px"}return this._setIconStyles(t,"icon"),t},createShadow:function(){return null}});function on(A){return new wi(A)}Jt.Default=Yt;var pe=xA.extend({options:{tileSize:256,opacity:1,updateWhenIdle:K.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(A){rA(this,A)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(A){A._addZoomLimit(this)},onRemove:function(A){this._removeAllTiles(),lA(this._container),A._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(ue(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(mt(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(A){return this.options.opacity=A,this._updateOpacity(),this},setZIndex:function(A){return this.options.zIndex=A,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var A=this._clampZoom(this._map.getZoom());A!==this._tileZoom&&(this._tileZoom=A,this._updateLevels()),this._update()}return this},getEvents:function(){var A={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=dt(this._onMoveEnd,this.options.updateInterval,this)),A.move=this._onMove),this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},createTile:function(){return document.createElement("div")},getTileSize:function(){var A=this.options.tileSize;return A instanceof V?A:new V(A,A)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(A){for(var t=this.getPane().children,n=-A(-1/0,1/0),o=0,u=t.length,l;o<u;o++)l=t[o].style.zIndex,t[o]!==this._container&&l&&(n=A(n,+l));isFinite(n)&&(this.options.zIndex=n+A(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!K.ielt9){WA(this._container,this.options.opacity);var A=+new Date,t=!1,n=!1;for(var o in this._tiles){var u=this._tiles[o];if(!(!u.current||!u.loaded)){var l=Math.min(1,(A-u.loaded)/200);WA(u.el,l),l<1?t=!0:(u.active?n=!0:this._onOpaqueTile(u),u.active=!0)}}n&&!this._noPrune&&this._pruneTiles(),t&&(OA(this._fadeFrame),this._fadeFrame=PA(this._updateOpacity,this))}},_onOpaqueTile:sA,_initContainer:function(){this._container||(this._container=J("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var A=this._tileZoom,t=this.options.maxZoom;if(A!==void 0){for(var n in this._levels)n=Number(n),this._levels[n].el.children.length||n===A?(this._levels[n].el.style.zIndex=t-Math.abs(A-n),this._onUpdateLevel(n)):(lA(this._levels[n].el),this._removeTilesAtZoom(n),this._onRemoveLevel(n),delete this._levels[n]);var o=this._levels[A],u=this._map;return o||(o=this._levels[A]={},o.el=J("div","leaflet-tile-container leaflet-zoom-animated",this._container),o.el.style.zIndex=t,o.origin=u.project(u.unproject(u.getPixelOrigin()),A).round(),o.zoom=A,this._setZoomTransform(o,u.getCenter(),u.getZoom()),sA(o.el.offsetWidth),this._onCreateLevel(o)),this._level=o,o}},_onUpdateLevel:sA,_onRemoveLevel:sA,_onCreateLevel:sA,_pruneTiles:function(){if(this._map){var A,t,n=this._map.getZoom();if(n>this.options.maxZoom||n<this.options.minZoom){this._removeAllTiles();return}for(A in this._tiles)t=this._tiles[A],t.retain=t.current;for(A in this._tiles)if(t=this._tiles[A],t.current&&!t.active){var o=t.coords;this._retainParent(o.x,o.y,o.z,o.z-5)||this._retainChildren(o.x,o.y,o.z,o.z+2)}for(A in this._tiles)this._tiles[A].retain||this._removeTile(A)}},_removeTilesAtZoom:function(A){for(var t in this._tiles)this._tiles[t].coords.z===A&&this._removeTile(t)},_removeAllTiles:function(){for(var A in this._tiles)this._removeTile(A)},_invalidateAll:function(){for(var A in this._levels)lA(this._levels[A].el),this._onRemoveLevel(Number(A)),delete this._levels[A];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(A,t,n,o){var u=Math.floor(A/2),l=Math.floor(t/2),g=n-1,Q=new V(+u,+l);Q.z=+g;var m=this._tileCoordsToKey(Q),_=this._tiles[m];return _&&_.active?(_.retain=!0,!0):(_&&_.loaded&&(_.retain=!0),g>o?this._retainParent(u,l,g,o):!1)},_retainChildren:function(A,t,n,o){for(var u=2*A;u<2*A+2;u++)for(var l=2*t;l<2*t+2;l++){var g=new V(u,l);g.z=n+1;var Q=this._tileCoordsToKey(g),m=this._tiles[Q];if(m&&m.active){m.retain=!0;continue}else m&&m.loaded&&(m.retain=!0);n+1<o&&this._retainChildren(u,l,n+1,o)}},_resetView:function(A){var t=A&&(A.pinch||A.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),t,t)},_animateZoom:function(A){this._setView(A.center,A.zoom,!0,A.noUpdate)},_clampZoom:function(A){var t=this.options;return t.minNativeZoom!==void 0&&A<t.minNativeZoom?t.minNativeZoom:t.maxNativeZoom!==void 0&&t.maxNativeZoom<A?t.maxNativeZoom:A},_setView:function(A,t,n,o){var u=Math.round(t);this.options.maxZoom!==void 0&&u>this.options.maxZoom||this.options.minZoom!==void 0&&u<this.options.minZoom?u=void 0:u=this._clampZoom(u);var l=this.options.updateWhenZooming&&u!==this._tileZoom;(!o||l)&&(this._tileZoom=u,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),u!==void 0&&this._update(A),n||this._pruneTiles(),this._noPrune=!!n),this._setZoomTransforms(A,t)},_setZoomTransforms:function(A,t){for(var n in this._levels)this._setZoomTransform(this._levels[n],A,t)},_setZoomTransform:function(A,t,n){var o=this._map.getZoomScale(n,A.zoom),u=A.origin.multiplyBy(o).subtract(this._map._getNewPixelOrigin(t,n)).round();K.any3d?Vt(A.el,u,o):pA(A.el,u)},_resetGrid:function(){var A=this._map,t=A.options.crs,n=this._tileSize=this.getTileSize(),o=this._tileZoom,u=this._map.getPixelWorldBounds(this._tileZoom);u&&(this._globalTileRange=this._pxBoundsToTileRange(u)),this._wrapX=t.wrapLng&&!this.options.noWrap&&[Math.floor(A.project([0,t.wrapLng[0]],o).x/n.x),Math.ceil(A.project([0,t.wrapLng[1]],o).x/n.y)],this._wrapY=t.wrapLat&&!this.options.noWrap&&[Math.floor(A.project([t.wrapLat[0],0],o).y/n.x),Math.ceil(A.project([t.wrapLat[1],0],o).y/n.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(A){var t=this._map,n=t._animatingZoom?Math.max(t._animateToZoom,t.getZoom()):t.getZoom(),o=t.getZoomScale(n,this._tileZoom),u=t.project(A,this._tileZoom).floor(),l=t.getSize().divideBy(o*2);return new cA(u.subtract(l),u.add(l))},_update:function(A){var t=this._map;if(t){var n=this._clampZoom(t.getZoom());if(A===void 0&&(A=t.getCenter()),this._tileZoom!==void 0){var o=this._getTiledPixelBounds(A),u=this._pxBoundsToTileRange(o),l=u.getCenter(),g=[],Q=this.options.keepBuffer,m=new cA(u.getBottomLeft().subtract([Q,-Q]),u.getTopRight().add([Q,-Q]));if(!(isFinite(u.min.x)&&isFinite(u.min.y)&&isFinite(u.max.x)&&isFinite(u.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var _ in this._tiles){var F=this._tiles[_].coords;(F.z!==this._tileZoom||!m.contains(new V(F.x,F.y)))&&(this._tiles[_].current=!1)}if(Math.abs(n-this._tileZoom)>1){this._setView(A,n);return}for(var D=u.min.y;D<=u.max.y;D++)for(var z=u.min.x;z<=u.max.x;z++){var SA=new V(z,D);if(SA.z=this._tileZoom,!!this._isValidTile(SA)){var _A=this._tiles[this._tileCoordsToKey(SA)];_A?_A.current=!0:g.push(SA)}}if(g.sort(function(kA,me){return kA.distanceTo(l)-me.distanceTo(l)}),g.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var $A=document.createDocumentFragment();for(z=0;z<g.length;z++)this._addTile(g[z],$A);this._level.el.appendChild($A)}}}},_isValidTile:function(A){var t=this._map.options.crs;if(!t.infinite){var n=this._globalTileRange;if(!t.wrapLng&&(A.x<n.min.x||A.x>n.max.x)||!t.wrapLat&&(A.y<n.min.y||A.y>n.max.y))return!1}if(!this.options.bounds)return!0;var o=this._tileCoordsToBounds(A);return wA(this.options.bounds).overlaps(o)},_keyToBounds:function(A){return this._tileCoordsToBounds(this._keyToTileCoords(A))},_tileCoordsToNwSe:function(A){var t=this._map,n=this.getTileSize(),o=A.scaleBy(n),u=o.add(n),l=t.unproject(o,A.z),g=t.unproject(u,A.z);return[l,g]},_tileCoordsToBounds:function(A){var t=this._tileCoordsToNwSe(A),n=new NA(t[0],t[1]);return this.options.noWrap||(n=this._map.wrapLatLngBounds(n)),n},_tileCoordsToKey:function(A){return A.x+":"+A.y+":"+A.z},_keyToTileCoords:function(A){var t=A.split(":"),n=new V(+t[0],+t[1]);return n.z=+t[2],n},_removeTile:function(A){var t=this._tiles[A];t&&(lA(t.el),delete this._tiles[A],this.fire("tileunload",{tile:t.el,coords:this._keyToTileCoords(A)}))},_initTile:function(A){G(A,"leaflet-tile");var t=this.getTileSize();A.style.width=t.x+"px",A.style.height=t.y+"px",A.onselectstart=sA,A.onmousemove=sA,K.ielt9&&this.options.opacity<1&&WA(A,this.options.opacity)},_addTile:function(A,t){var n=this._getTilePos(A),o=this._tileCoordsToKey(A),u=this.createTile(this._wrapCoords(A),M(this._tileReady,this,A));this._initTile(u),this.createTile.length<2&&PA(M(this._tileReady,this,A,null,u)),pA(u,n),this._tiles[o]={el:u,coords:A,current:!0},t.appendChild(u),this.fire("tileloadstart",{tile:u,coords:A})},_tileReady:function(A,t,n){t&&this.fire("tileerror",{error:t,tile:n,coords:A});var o=this._tileCoordsToKey(A);n=this._tiles[o],n&&(n.loaded=+new Date,this._map._fadeAnimated?(WA(n.el,0),OA(this._fadeFrame),this._fadeFrame=PA(this._updateOpacity,this)):(n.active=!0,this._pruneTiles()),t||(G(n.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:n.el,coords:A})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),K.ielt9||!this._map._fadeAnimated?PA(this._pruneTiles,this):setTimeout(M(this._pruneTiles,this),250)))},_getTilePos:function(A){return A.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(A){var t=new V(this._wrapX?Pt(A.x,this._wrapX):A.x,this._wrapY?Pt(A.y,this._wrapY):A.y);return t.z=A.z,t},_pxBoundsToTileRange:function(A){var t=this.getTileSize();return new cA(A.min.unscaleBy(t).floor(),A.max.unscaleBy(t).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var A in this._tiles)if(!this._tiles[A].loaded)return!1;return!0}});function an(A){return new pe(A)}var jt=pe.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(A,t){this._url=A,t=rA(this,t),t.detectRetina&&K.retina&&t.maxZoom>0?(t.tileSize=Math.floor(t.tileSize/2),t.zoomReverse?(t.zoomOffset--,t.minZoom=Math.min(t.maxZoom,t.minZoom+1)):(t.zoomOffset++,t.maxZoom=Math.max(t.minZoom,t.maxZoom-1)),t.minZoom=Math.max(0,t.minZoom)):t.zoomReverse?t.minZoom=Math.min(t.maxZoom,t.minZoom):t.maxZoom=Math.max(t.minZoom,t.maxZoom),typeof t.subdomains=="string"&&(t.subdomains=t.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(A,t){return this._url===A&&t===void 0&&(t=!0),this._url=A,t||this.redraw(),this},createTile:function(A,t){var n=document.createElement("img");return Z(n,"load",M(this._tileOnLoad,this,t,n)),Z(n,"error",M(this._tileOnError,this,t,n)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(n.referrerPolicy=this.options.referrerPolicy),n.alt="",n.src=this.getTileUrl(A),n},getTileUrl:function(A){var t={r:K.retina?"@2x":"",s:this._getSubdomain(A),x:A.x,y:A.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var n=this._globalTileRange.max.y-A.y;this.options.tms&&(t.y=n),t["-y"]=n}return VA(this._url,E(t,this.options))},_tileOnLoad:function(A,t){K.ielt9?setTimeout(M(A,this,null,t),0):A(null,t)},_tileOnError:function(A,t,n){var o=this.options.errorTileUrl;o&&t.getAttribute("src")!==o&&(t.src=o),A(n,t)},_onTileRemove:function(A){A.tile.onload=null},_getZoomForUrl:function(){var A=this._tileZoom,t=this.options.maxZoom,n=this.options.zoomReverse,o=this.options.zoomOffset;return n&&(A=t-A),A+o},_getSubdomain:function(A){var t=Math.abs(A.x+A.y)%this.options.subdomains.length;return this.options.subdomains[t]},_abortLoading:function(){var A,t;for(A in this._tiles)if(this._tiles[A].coords.z!==this._tileZoom&&(t=this._tiles[A].el,t.onload=sA,t.onerror=sA,!t.complete)){t.src=$e;var n=this._tiles[A].coords;lA(t),delete this._tiles[A],this.fire("tileabort",{tile:t,coords:n})}},_removeTile:function(A){var t=this._tiles[A];if(t)return t.el.setAttribute("src",$e),pe.prototype._removeTile.call(this,A)},_tileReady:function(A,t,n){if(!(!this._map||n&&n.getAttribute("src")===$e))return pe.prototype._tileReady.call(this,A,t,n)}});function pi(A,t){return new jt(A,t)}var Qi=jt.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(A,t){this._url=A;var n=E({},this.defaultWmsParams);for(var o in t)o in this.options||(n[o]=t[o]);t=rA(this,t);var u=t.detectRetina&&K.retina?2:1,l=this.getTileSize();n.width=l.x*u,n.height=l.y*u,this.wmsParams=n},onAdd:function(A){this._crs=this.options.crs||A.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var t=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[t]=this._crs.code,jt.prototype.onAdd.call(this,A)},getTileUrl:function(A){var t=this._tileCoordsToNwSe(A),n=this._crs,o=RA(n.project(t[0]),n.project(t[1])),u=o.min,l=o.max,g=(this._wmsVersion>=1.3&&this._crs===vr?[u.y,u.x,l.y,l.x]:[u.x,u.y,l.x,l.y]).join(","),Q=jt.prototype.getTileUrl.call(this,A);return Q+Fe(this.wmsParams,Q,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+g},setParams:function(A,t){return E(this.wmsParams,A),t||this.redraw(),this}});function hs(A,t){return new Qi(A,t)}jt.WMS=Qi,pi.wms=hs;var jA=xA.extend({options:{padding:.1},initialize:function(A){rA(this,A),R(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),G(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var A={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(A.zoomanim=this._onAnimZoom),A},_onAnimZoom:function(A){this._updateTransform(A.center,A.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(A,t){var n=this._map.getZoomScale(t,this._zoom),o=this._map.getSize().multiplyBy(.5+this.options.padding),u=this._map.project(this._center,t),l=o.multiplyBy(-n).add(u).subtract(this._map._getNewPixelOrigin(A,t));K.any3d?Vt(this._container,l,n):pA(this._container,l)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var A in this._layers)this._layers[A]._reset()},_onZoomEnd:function(){for(var A in this._layers)this._layers[A]._project()},_updatePaths:function(){for(var A in this._layers)this._layers[A]._update()},_update:function(){var A=this.options.padding,t=this._map.getSize(),n=this._map.containerPointToLayerPoint(t.multiplyBy(-A)).round();this._bounds=new cA(n,n.add(t.multiplyBy(1+A*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),un=jA.extend({options:{tolerance:0},getEvents:function(){var A=jA.prototype.getEvents.call(this);return A.viewprereset=this._onViewPreReset,A},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){jA.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var A=this._container=document.createElement("canvas");Z(A,"mousemove",this._onMouseMove,this),Z(A,"click dblclick mousedown mouseup contextmenu",this._onClick,this),Z(A,"mouseout",this._handleMouseOut,this),A._leaflet_disable_events=!0,this._ctx=A.getContext("2d")},_destroyContainer:function(){OA(this._redrawRequest),delete this._ctx,lA(this._container),aA(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var A;this._redrawBounds=null;for(var t in this._layers)A=this._layers[t],A._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){jA.prototype._update.call(this);var A=this._bounds,t=this._container,n=A.getSize(),o=K.retina?2:1;pA(t,A.min),t.width=o*n.x,t.height=o*n.y,t.style.width=n.x+"px",t.style.height=n.y+"px",K.retina&&this._ctx.scale(2,2),this._ctx.translate(-A.min.x,-A.min.y),this.fire("update")}},_reset:function(){jA.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(A){this._updateDashArray(A),this._layers[R(A)]=A;var t=A._order={layer:A,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=t),this._drawLast=t,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(A){this._requestRedraw(A)},_removePath:function(A){var t=A._order,n=t.next,o=t.prev;n?n.prev=o:this._drawLast=o,o?o.next=n:this._drawFirst=n,delete A._order,delete this._layers[R(A)],this._requestRedraw(A)},_updatePath:function(A){this._extendRedrawBounds(A),A._project(),A._update(),this._requestRedraw(A)},_updateStyle:function(A){this._updateDashArray(A),this._requestRedraw(A)},_updateDashArray:function(A){if(typeof A.options.dashArray=="string"){var t=A.options.dashArray.split(/[, ]+/),n=[],o,u;for(u=0;u<t.length;u++){if(o=Number(t[u]),isNaN(o))return;n.push(o)}A.options._dashArray=n}else A.options._dashArray=A.options.dashArray},_requestRedraw:function(A){this._map&&(this._extendRedrawBounds(A),this._redrawRequest=this._redrawRequest||PA(this._redraw,this))},_extendRedrawBounds:function(A){if(A._pxBounds){var t=(A.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new cA,this._redrawBounds.extend(A._pxBounds.min.subtract([t,t])),this._redrawBounds.extend(A._pxBounds.max.add([t,t]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var A=this._redrawBounds;if(A){var t=A.getSize();this._ctx.clearRect(A.min.x,A.min.y,t.x,t.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var A,t=this._redrawBounds;if(this._ctx.save(),t){var n=t.getSize();this._ctx.beginPath(),this._ctx.rect(t.min.x,t.min.y,n.x,n.y),this._ctx.clip()}this._drawing=!0;for(var o=this._drawFirst;o;o=o.next)A=o.layer,(!t||A._pxBounds&&A._pxBounds.intersects(t))&&A._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(A,t){if(this._drawing){var n,o,u,l,g=A._parts,Q=g.length,m=this._ctx;if(Q){for(m.beginPath(),n=0;n<Q;n++){for(o=0,u=g[n].length;o<u;o++)l=g[n][o],m[o?"lineTo":"moveTo"](l.x,l.y);t&&m.closePath()}this._fillStroke(m,A)}}},_updateCircle:function(A){if(!(!this._drawing||A._empty())){var t=A._point,n=this._ctx,o=Math.max(Math.round(A._radius),1),u=(Math.max(Math.round(A._radiusY),1)||o)/o;u!==1&&(n.save(),n.scale(1,u)),n.beginPath(),n.arc(t.x,t.y/u,o,0,Math.PI*2,!1),u!==1&&n.restore(),this._fillStroke(n,A)}},_fillStroke:function(A,t){var n=t.options;n.fill&&(A.globalAlpha=n.fillOpacity,A.fillStyle=n.fillColor||n.color,A.fill(n.fillRule||"evenodd")),n.stroke&&n.weight!==0&&(A.setLineDash&&A.setLineDash(t.options&&t.options._dashArray||[]),A.globalAlpha=n.opacity,A.lineWidth=n.weight,A.strokeStyle=n.color,A.lineCap=n.lineCap,A.lineJoin=n.lineJoin,A.stroke())},_onClick:function(A){for(var t=this._map.mouseEventToLayerPoint(A),n,o,u=this._drawFirst;u;u=u.next)n=u.layer,n.options.interactive&&n._containsPoint(t)&&(!(A.type==="click"||A.type==="preclick")||!this._map._draggableMoved(n))&&(o=n);this._fireEvent(o?[o]:!1,A)},_onMouseMove:function(A){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var t=this._map.mouseEventToLayerPoint(A);this._handleMouseHover(A,t)}},_handleMouseOut:function(A){var t=this._hoveredLayer;t&&(fA(this._container,"leaflet-interactive"),this._fireEvent([t],A,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(A,t){if(!this._mouseHoverThrottled){for(var n,o,u=this._drawFirst;u;u=u.next)n=u.layer,n.options.interactive&&n._containsPoint(t)&&(o=n);o!==this._hoveredLayer&&(this._handleMouseOut(A),o&&(G(this._container,"leaflet-interactive"),this._fireEvent([o],A,"mouseover"),this._hoveredLayer=o)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,A),this._mouseHoverThrottled=!0,setTimeout(M(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(A,t,n){this._map._fireDOMEvent(t,n||t.type,A)},_bringToFront:function(A){var t=A._order;if(t){var n=t.next,o=t.prev;if(n)n.prev=o;else return;o?o.next=n:n&&(this._drawFirst=n),t.prev=this._drawLast,this._drawLast.next=t,t.next=null,this._drawLast=t,this._requestRedraw(A)}},_bringToBack:function(A){var t=A._order;if(t){var n=t.next,o=t.prev;if(o)o.next=n;else return;n?n.prev=o:o&&(this._drawLast=o),t.prev=null,t.next=this._drawFirst,this._drawFirst.prev=t,this._drawFirst=t,this._requestRedraw(A)}}});function hn(A){return K.canvas?new un(A):null}var $t=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(A){return document.createElement("<lvml:"+A+' class="lvml">')}}catch{}return function(A){return document.createElement("<"+A+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),cs={_initContainer:function(){this._container=J("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(jA.prototype._update.call(this),this.fire("update"))},_initPath:function(A){var t=A._container=$t("shape");G(t,"leaflet-vml-shape "+(this.options.className||"")),t.coordsize="1 1",A._path=$t("path"),t.appendChild(A._path),this._updateStyle(A),this._layers[R(A)]=A},_addPath:function(A){var t=A._container;this._container.appendChild(t),A.options.interactive&&A.addInteractiveTarget(t)},_removePath:function(A){var t=A._container;lA(t),A.removeInteractiveTarget(t),delete this._layers[R(A)]},_updateStyle:function(A){var t=A._stroke,n=A._fill,o=A.options,u=A._container;u.stroked=!!o.stroke,u.filled=!!o.fill,o.stroke?(t||(t=A._stroke=$t("stroke")),u.appendChild(t),t.weight=o.weight+"px",t.color=o.color,t.opacity=o.opacity,o.dashArray?t.dashStyle=rt(o.dashArray)?o.dashArray.join(" "):o.dashArray.replace(/( *, *)/g," "):t.dashStyle="",t.endcap=o.lineCap.replace("butt","flat"),t.joinstyle=o.lineJoin):t&&(u.removeChild(t),A._stroke=null),o.fill?(n||(n=A._fill=$t("fill")),u.appendChild(n),n.color=o.fillColor||o.color,n.opacity=o.fillOpacity):n&&(u.removeChild(n),A._fill=null)},_updateCircle:function(A){var t=A._point.round(),n=Math.round(A._radius),o=Math.round(A._radiusY||n);this._setPath(A,A._empty()?"M0 0":"AL "+t.x+","+t.y+" "+n+","+o+" 0,"+65535*360)},_setPath:function(A,t){A._path.v=t},_bringToFront:function(A){ue(A._container)},_bringToBack:function(A){mt(A._container)}},Ve=K.vml?$t:Xr,Ze=jA.extend({_initContainer:function(){this._container=Ve("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Ve("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){lA(this._container),aA(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){jA.prototype._update.call(this);var A=this._bounds,t=A.getSize(),n=this._container;(!this._svgSize||!this._svgSize.equals(t))&&(this._svgSize=t,n.setAttribute("width",t.x),n.setAttribute("height",t.y)),pA(n,A.min),n.setAttribute("viewBox",[A.min.x,A.min.y,t.x,t.y].join(" ")),this.fire("update")}},_initPath:function(A){var t=A._path=Ve("path");A.options.className&&G(t,A.options.className),A.options.interactive&&G(t,"leaflet-interactive"),this._updateStyle(A),this._layers[R(A)]=A},_addPath:function(A){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(A._path),A.addInteractiveTarget(A._path)},_removePath:function(A){lA(A._path),A.removeInteractiveTarget(A._path),delete this._layers[R(A)]},_updatePath:function(A){A._project(),A._update()},_updateStyle:function(A){var t=A._path,n=A.options;t&&(n.stroke?(t.setAttribute("stroke",n.color),t.setAttribute("stroke-opacity",n.opacity),t.setAttribute("stroke-width",n.weight),t.setAttribute("stroke-linecap",n.lineCap),t.setAttribute("stroke-linejoin",n.lineJoin),n.dashArray?t.setAttribute("stroke-dasharray",n.dashArray):t.removeAttribute("stroke-dasharray"),n.dashOffset?t.setAttribute("stroke-dashoffset",n.dashOffset):t.removeAttribute("stroke-dashoffset")):t.setAttribute("stroke","none"),n.fill?(t.setAttribute("fill",n.fillColor||n.color),t.setAttribute("fill-opacity",n.fillOpacity),t.setAttribute("fill-rule",n.fillRule||"evenodd")):t.setAttribute("fill","none"))},_updatePoly:function(A,t){this._setPath(A,zr(A._parts,t))},_updateCircle:function(A){var t=A._point,n=Math.max(Math.round(A._radius),1),o=Math.max(Math.round(A._radiusY),1)||n,u="a"+n+","+o+" 0 1,0 ",l=A._empty()?"M0 0":"M"+(t.x-n)+","+t.y+u+n*2+",0 "+u+-n*2+",0 ";this._setPath(A,l)},_setPath:function(A,t){A._path.setAttribute("d",t)},_bringToFront:function(A){ue(A._path)},_bringToBack:function(A){mt(A._path)}});K.vml&&Ze.include(cs);function cn(A){return K.svg||K.vml?new Ze(A):null}q.include({getRenderer:function(A){var t=A.options.renderer||this._getPaneRenderer(A.options.pane)||this.options.renderer||this._renderer;return t||(t=this._renderer=this._createRenderer()),this.hasLayer(t)||this.addLayer(t),t},_getPaneRenderer:function(A){if(A==="overlayPane"||A===void 0)return!1;var t=this._paneRenderers[A];return t===void 0&&(t=this._createRenderer({pane:A}),this._paneRenderers[A]=t),t},_createRenderer:function(A){return this.options.preferCanvas&&hn(A)||cn(A)}});var ln=HA.extend({initialize:function(A,t){HA.prototype.initialize.call(this,this._boundsToLatLngs(A),t)},setBounds:function(A){return this.setLatLngs(this._boundsToLatLngs(A))},_boundsToLatLngs:function(A){return A=wA(A),[A.getSouthWest(),A.getNorthWest(),A.getNorthEast(),A.getSouthEast()]}});function ls(A,t){return new ln(A,t)}Ze.create=Ve,Ze.pointsToPath=zr,TA.geometryToLayer=gA,TA.coordsToLatLng=yr,TA.coordsToLatLngs=Ne,TA.latLngToCoords=Ge,TA.latLngsToCoords=ke,TA.getFeature=we,TA.asFeature=St,q.mergeOptions({boxZoom:!0});var Bn=ht.extend({initialize:function(A){this._map=A,this._container=A._container,this._pane=A._panes.overlayPane,this._resetStateTimeout=0,A.on("unload",this._destroy,this)},addHooks:function(){Z(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){aA(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){lA(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(A){if(!A.shiftKey||A.which!==1&&A.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),Ke(),ni(),this._startPoint=this._map.mouseEventToContainerPoint(A),Z(document,{contextmenu:Xt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(A){this._moved||(this._moved=!0,this._box=J("div","leaflet-zoom-box",this._container),G(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(A);var t=new cA(this._point,this._startPoint),n=t.getSize();pA(this._box,t.min),this._box.style.width=n.x+"px",this._box.style.height=n.y+"px"},_finish:function(){this._moved&&(lA(this._box),fA(this._container,"leaflet-crosshair")),Ht(),si(),aA(document,{contextmenu:Xt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(A){if(!(A.which!==1&&A.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(M(this._resetState,this),0);var t=new NA(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(t).fire("boxzoomend",{boxZoomBounds:t})}},_onKeyDown:function(A){A.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});q.addInitHook("addHandler","boxZoom",Bn),q.mergeOptions({doubleClickZoom:!0});var Qe=ht.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(A){var t=this._map,n=t.getZoom(),o=t.options.zoomDelta,u=A.originalEvent.shiftKey?n-o:n+o;t.options.doubleClickZoom==="center"?t.setZoom(u):t.setZoomAround(A.containerPoint,u)}});q.addInitHook("addHandler","doubleClickZoom",Qe),q.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var fn=ht.extend({addHooks:function(){if(!this._draggable){var A=this._map;this._draggable=new xt(A._mapPane,A._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),A.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),A.on("zoomend",this._onZoomEnd,this),A.whenReady(this._onZoomEnd,this))}G(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){fA(this._map._container,"leaflet-grab"),fA(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var A=this._map;if(A._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var t=wA(this._map.options.maxBounds);this._offsetLimit=RA(this._map.latLngToContainerPoint(t.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(t.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;A.fire("movestart").fire("dragstart"),A.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(A){if(this._map.options.inertia){var t=this._lastTime=+new Date,n=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(n),this._times.push(t),this._prunePositions(t)}this._map.fire("move",A).fire("drag",A)},_prunePositions:function(A){for(;this._positions.length>1&&A-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var A=this._map.getSize().divideBy(2),t=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=t.subtract(A).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(A,t){return A-(A-t)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var A=this._draggable._newPos.subtract(this._draggable._startPos),t=this._offsetLimit;A.x<t.min.x&&(A.x=this._viscousLimit(A.x,t.min.x)),A.y<t.min.y&&(A.y=this._viscousLimit(A.y,t.min.y)),A.x>t.max.x&&(A.x=this._viscousLimit(A.x,t.max.x)),A.y>t.max.y&&(A.y=this._viscousLimit(A.y,t.max.y)),this._draggable._newPos=this._draggable._startPos.add(A)}},_onPreDragWrap:function(){var A=this._worldWidth,t=Math.round(A/2),n=this._initialWorldOffset,o=this._draggable._newPos.x,u=(o-t+n)%A+t-n,l=(o+t+n)%A-t-n,g=Math.abs(u+n)<Math.abs(l+n)?u:l;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=g},_onDragEnd:function(A){var t=this._map,n=t.options,o=!n.inertia||A.noInertia||this._times.length<2;if(t.fire("dragend",A),o)t.fire("moveend");else{this._prunePositions(+new Date);var u=this._lastPos.subtract(this._positions[0]),l=(this._lastTime-this._times[0])/1e3,g=n.easeLinearity,Q=u.multiplyBy(g/l),m=Q.distanceTo([0,0]),_=Math.min(n.inertiaMaxSpeed,m),F=Q.multiplyBy(_/m),D=_/(n.inertiaDeceleration*g),z=F.multiplyBy(-D/2).round();!z.x&&!z.y?t.fire("moveend"):(z=t._limitOffset(z,t.options.maxBounds),PA(function(){t.panBy(z,{duration:D,easeLinearity:g,noMoveStart:!0,animate:!0})}))}}});q.addInitHook("addHandler","dragging",fn),q.mergeOptions({keyboard:!0,keyboardPanDelta:80});var gn=ht.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(A){this._map=A,this._setPanDelta(A.options.keyboardPanDelta),this._setZoomDelta(A.options.zoomDelta)},addHooks:function(){var A=this._map._container;A.tabIndex<=0&&(A.tabIndex="0"),Z(A,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),aA(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var A=document.body,t=document.documentElement,n=A.scrollTop||t.scrollTop,o=A.scrollLeft||t.scrollLeft;this._map._container.focus(),window.scrollTo(o,n)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(A){var t=this._panKeys={},n=this.keyCodes,o,u;for(o=0,u=n.left.length;o<u;o++)t[n.left[o]]=[-1*A,0];for(o=0,u=n.right.length;o<u;o++)t[n.right[o]]=[A,0];for(o=0,u=n.down.length;o<u;o++)t[n.down[o]]=[0,A];for(o=0,u=n.up.length;o<u;o++)t[n.up[o]]=[0,-1*A]},_setZoomDelta:function(A){var t=this._zoomKeys={},n=this.keyCodes,o,u;for(o=0,u=n.zoomIn.length;o<u;o++)t[n.zoomIn[o]]=A;for(o=0,u=n.zoomOut.length;o<u;o++)t[n.zoomOut[o]]=-A},_addHooks:function(){Z(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){aA(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(A){if(!(A.altKey||A.ctrlKey||A.metaKey)){var t=A.keyCode,n=this._map,o;if(t in this._panKeys){if(!n._panAnim||!n._panAnim._inProgress)if(o=this._panKeys[t],A.shiftKey&&(o=N(o).multiplyBy(3)),n.options.maxBounds&&(o=n._limitOffset(N(o),n.options.maxBounds)),n.options.worldCopyJump){var u=n.wrapLatLng(n.unproject(n.project(n.getCenter()).add(o)));n.panTo(u)}else n.panBy(o)}else if(t in this._zoomKeys)n.setZoom(n.getZoom()+(A.shiftKey?3:1)*this._zoomKeys[t]);else if(t===27&&n._popup&&n._popup.options.closeOnEscapeKey)n.closePopup();else return;Xt(A)}}});q.addInitHook("addHandler","keyboard",gn),q.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Xe=ht.extend({addHooks:function(){Z(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){aA(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(A){var t=ci(A),n=this._map.options.wheelDebounceTime;this._delta+=t,this._lastMousePos=this._map.mouseEventToContainerPoint(A),this._startTime||(this._startTime=+new Date);var o=Math.max(n-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(M(this._performZoom,this),o),Xt(A)},_performZoom:function(){var A=this._map,t=A.getZoom(),n=this._map.options.zoomSnap||0;A._stop();var o=this._delta/(this._map.options.wheelPxPerZoomLevel*4),u=4*Math.log(2/(1+Math.exp(-Math.abs(o))))/Math.LN2,l=n?Math.ceil(u/n)*n:u,g=A._limitZoom(t+(this._delta>0?l:-l))-t;this._delta=0,this._startTime=null,g&&(A.options.scrollWheelZoom==="center"?A.setZoom(t+g):A.setZoomAround(this._lastMousePos,t+g))}});q.addInitHook("addHandler","scrollWheelZoom",Xe);var Bs=600;q.mergeOptions({tapHold:K.touchNative&&K.safari&&K.mobile,tapTolerance:15});var dn=ht.extend({addHooks:function(){Z(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){aA(this._map._container,"touchstart",this._onDown,this)},_onDown:function(A){if(clearTimeout(this._holdTimeout),A.touches.length===1){var t=A.touches[0];this._startPos=this._newPos=new V(t.clientX,t.clientY),this._holdTimeout=setTimeout(M(function(){this._cancel(),this._isTapValid()&&(Z(document,"touchend",FA),Z(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",t))},this),Bs),Z(document,"touchend touchcancel contextmenu",this._cancel,this),Z(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function A(){aA(document,"touchend",FA),aA(document,"touchend touchcancel",A)},_cancel:function(){clearTimeout(this._holdTimeout),aA(document,"touchend touchcancel contextmenu",this._cancel,this),aA(document,"touchmove",this._onMove,this)},_onMove:function(A){var t=A.touches[0];this._newPos=new V(t.clientX,t.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(A,t){var n=new MouseEvent(A,{bubbles:!0,cancelable:!0,view:window,screenX:t.screenX,screenY:t.screenY,clientX:t.clientX,clientY:t.clientY});n._simulated=!0,t.target.dispatchEvent(n)}});q.addInitHook("addHandler","tapHold",dn),q.mergeOptions({touchZoom:K.touch,bounceAtZoomLimits:!0});var wn=ht.extend({addHooks:function(){G(this._map._container,"leaflet-touch-zoom"),Z(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){fA(this._map._container,"leaflet-touch-zoom"),aA(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(A){var t=this._map;if(!(!A.touches||A.touches.length!==2||t._animatingZoom||this._zooming)){var n=t.mouseEventToContainerPoint(A.touches[0]),o=t.mouseEventToContainerPoint(A.touches[1]);this._centerPoint=t.getSize()._divideBy(2),this._startLatLng=t.containerPointToLatLng(this._centerPoint),t.options.touchZoom!=="center"&&(this._pinchStartLatLng=t.containerPointToLatLng(n.add(o)._divideBy(2))),this._startDist=n.distanceTo(o),this._startZoom=t.getZoom(),this._moved=!1,this._zooming=!0,t._stop(),Z(document,"touchmove",this._onTouchMove,this),Z(document,"touchend touchcancel",this._onTouchEnd,this),FA(A)}},_onTouchMove:function(A){if(!(!A.touches||A.touches.length!==2||!this._zooming)){var t=this._map,n=t.mouseEventToContainerPoint(A.touches[0]),o=t.mouseEventToContainerPoint(A.touches[1]),u=n.distanceTo(o)/this._startDist;if(this._zoom=t.getScaleZoom(u,this._startZoom),!t.options.bounceAtZoomLimits&&(this._zoom<t.getMinZoom()&&u<1||this._zoom>t.getMaxZoom()&&u>1)&&(this._zoom=t._limitZoom(this._zoom)),t.options.touchZoom==="center"){if(this._center=this._startLatLng,u===1)return}else{var l=n._add(o)._divideBy(2)._subtract(this._centerPoint);if(u===1&&l.x===0&&l.y===0)return;this._center=t.unproject(t.project(this._pinchStartLatLng,this._zoom).subtract(l),this._zoom)}this._moved||(t._moveStart(!0,!1),this._moved=!0),OA(this._animRequest);var g=M(t._move,t,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=PA(g,this,!0),FA(A)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,OA(this._animRequest),aA(document,"touchmove",this._onTouchMove,this),aA(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});q.addInitHook("addHandler","touchZoom",wn),q.BoxZoom=Bn,q.DoubleClickZoom=Qe,q.Drag=fn,q.Keyboard=gn,q.ScrollWheelZoom=Xe,q.TapHold=dn,q.TouchZoom=wn,v.Bounds=cA,v.Browser=K,v.CRS=zA,v.Canvas=un,v.Circle=Ur,v.CircleMarker=Re,v.Class=pt,v.Control=nt,v.DivIcon=wi,v.DivOverlay=lt,v.DomEvent=qn,v.DomUtil=De,v.Draggable=xt,v.Evented=ye,v.FeatureGroup=bA,v.GeoJSON=TA,v.GridLayer=pe,v.Handler=ht,v.Icon=Jt,v.ImageOverlay=qt,v.LatLng=iA,v.LatLngBounds=NA,v.Layer=xA,v.LayerGroup=KA,v.LineUtil=qi,v.Map=q,v.Marker=Tt,v.Mixin=As,v.Path=vt,v.Point=V,v.PolyUtil=ts,v.Polygon=HA,v.Polyline=ct,v.Popup=Er,v.PosAnimation=yA,v.Projection=nA,v.Rectangle=ln,v.Renderer=jA,v.SVG=Ze,v.SVGOverlay=qA,v.TileLayer=jt,v.Tooltip=Ir,v.Transformation=er,v.Util=On,v.VideoOverlay=rn,v.bind=M,v.bounds=RA,v.canvas=hn,v.circle=Fr,v.circleMarker=$i,v.control=Pe,v.divIcon=on,v.extend=E,v.featureGroup=QA,v.geoJSON=tn,v.geoJson=as,v.gridLayer=an,v.icon=EA,v.imageOverlay=en,v.latLng=Y,v.latLngBounds=wA,v.layerGroup=os,v.map=Be,v.marker=uA,v.point=N,v.polygon=Kt,v.polyline=An,v.popup=Hr,v.rectangle=ls,v.setOptions=rA,v.stamp=R,v.svg=cn,v.svgOverlay=nn,v.tileLayer=pi,v.tooltip=sn,v.transformation=re,v.version=mA,v.videoOverlay=us;var fs=window.L;v.noConflict=function(){return window.L=fs,this},window.L=v})});var qh=(()=>{class v{constructor(E){this.http=E,this.MAPBOX_ACCESS_TOKEN=ea.mapboxAccessToken,this.MAPBOX_BASE_URL="https://api.mapbox.com/directions/v5/mapbox",this.currentRoute=null,this.isRealTimeActive=!1,this.realTimeInterval=null,this.lastUserPosition=null,this.routeUpdateCallback=null}getDirections(Pt,sA,$,Ot){return Xs(this,arguments,function*(E,j,M,XA,R="walking",dt={}){console.log(`\u{1F5FA}\uFE0F Mapbox: Calculating route from [${j}, ${E}] to [${XA}, ${M}] using ${R}`);try{let UA=this.convertToMapboxProfile(R),rA=`${E},${j};${M},${XA}`,Fe=new URLSearchParams(qo({access_token:this.MAPBOX_ACCESS_TOKEN,geometries:"geojson",overview:"full",steps:"true"},dt)),Ft=`${this.MAPBOX_BASE_URL}/${UA}/${rA}?${Fe.toString()}`;console.log(`\u{1F310} Mapbox request URL: ${Ft.substring(0,100)}...`);let VA=yield this.http.get(Ft).toPromise();if(console.log("\u{1F4E1} Mapbox response:",VA),VA&&VA.routes&&VA.routes.length>0)return console.log("\u2705 Successfully got route from Mapbox"),console.log(`\u{1F4CF} Distance: ${(VA.routes[0].distance/1e3).toFixed(2)} km, Duration: ${Math.round(VA.routes[0].duration/60)} min`),VA;throw new Error("No routes found in Mapbox response")}catch(UA){throw console.error("\u274C Mapbox routing error:",UA),UA.status&&console.error(`HTTP Status: ${UA.status}`),UA.error&&console.error("Error details:",UA.error),UA}})}convertToMapboxProfile(E){switch(E.toLowerCase()){case"walking":case"foot":case"foot-walking":return"walking";case"cycling":case"bicycle":case"bike":case"cycling-regular":return"cycling";case"driving":case"car":case"driving-car":return"driving";default:return"walking"}}convertTravelModeToProfile(E){switch(E.toLowerCase()){case"walking":case"foot":case"foot-walking":return"walking";case"cycling":case"bicycle":case"bike":case"cycling-regular":return"cycling";case"driving":case"car":case"driving-car":return"driving";default:return"walking"}}convertToGeoJSON(E){return{type:"Feature",geometry:E.geometry,properties:{distance:E.distance,duration:E.duration}}}getRouteSummary(E){let j=(E.distance/1e3).toFixed(2),M=Math.round(E.duration/60);return{distance:`${j} km`,duration:`${M} min`,distanceText:`${j} km`,durationText:`${M} min`}}calculateDistance(E,j,M,XA){let dt=E*Math.PI/180,Pt=M*Math.PI/180,sA=(M-E)*Math.PI/180,$=(XA-j)*Math.PI/180,Ot=Math.sin(sA/2)*Math.sin(sA/2)+Math.cos(dt)*Math.cos(Pt)*Math.sin($/2)*Math.sin($/2);return 6371e3*(2*Math.atan2(Math.sqrt(Ot),Math.sqrt(1-Ot)))}estimateDuration(E,j){let M={walking:1.4,cycling:4.2,driving:13.9},XA=M[j]||M.walking;return E/XA}formatDuration(E){let j=Math.floor(E/3600),M=Math.floor(E%3600/60);return j>0?`${j}h ${M}m`:`${M}m`}formatDistance(E){return E>=1e3?`${(E/1e3).toFixed(1)} km`:`${Math.round(E)} m`}testConnection(){return Xs(this,null,function*(){try{console.log("\u{1F9EA} Testing Mapbox API connection...");let E=yield this.getDirections(123.8854,10.3157,123.8954,10.3257,"walking");return E&&E.routes&&E.routes.length>0?{success:!0,message:"Mapbox API connection successful!",details:{routes:E.routes.length,distance:E.routes[0].distance,duration:E.routes[0].duration}}:{success:!1,message:"Mapbox API returned empty response",details:E}}catch(E){return console.error("\u274C Mapbox connection test failed:",E),{success:!1,message:`Mapbox API connection failed: ${E.message||E}`,details:{status:E.status,error:E.error}}}})}static{this.\u0275fac=function(j){return new(j||v)(Aa(ta))}}static{this.\u0275prov=$o({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})();export{qh as a,Xh as b,zh as c};
