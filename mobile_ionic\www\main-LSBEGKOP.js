import{a as K,b as o1,c as a1}from"./chunk-XICXROI7.js";import{a as v1}from"./chunk-WB4DBDSO.js";import{a as n1,b as l1}from"./chunk-TTLEF7YW.js";import{a as r1}from"./chunk-BCZAIQLD.js";import{a as c1}from"./chunk-KE2FHCRG.js";import{a as e1}from"./chunk-G7IUXWVS.js";import"./chunk-2GT6F2KJ.js";import"./chunk-ICWJVXBH.js";import{$ as _,Ba as $,D as l,Da as G,E as p,Ea as X,Fa as Q,Ga as J,H,I as c,M as i,Mb as t1,N as n,O as v,R as F,S as C,T as h,_ as r,aa as u,ba as E,c as S,cc as i1,fa as M,g as j,h as O,hc as z,i as w,l as x,m as b,n as m,nb as Y,oa as I,q as L,qa as U,s as q,sa as T,t as D,tb as Z,wa as V,xa as R,y as P,ya as N,yb as s1,za as W}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-P4YSCN2K.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-7D2EH4XU.js";import"./chunk-MMIXVVWR.js";import"./chunk-SV7S5NYR.js";import{h as g}from"./chunk-B7O3QC5Z.js";var A,l2=function(){if(typeof window>"u")return new Map;if(!A){var o=window;o.Ionicons=o.Ionicons||{},A=o.Ionicons.map=o.Ionicons.map||new Map}return A},y=function(o){Object.keys(o).forEach(function(e){g1(e,o[e]);var a=e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();e!==a&&g1(a,o[e])})},g1=function(o,e){var a=l2(),s=a.get(o);s===void 0?a.set(o,e):s!==e&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(o,'". Ensure that multiple icons are not mapped to the same icon name.'))};var w1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 166.05L256 288l5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 6z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 367.91a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var h1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M268 112l144 144-144 144M392 256H100' class='ionicon-fill-none'/></svg>";var p1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M388 288a76 76 0 1076 76 76.24 76.24 0 00-76-76zM124 288a76 76 0 1076 76 76.24 76.24 0 00-76-76z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 360v-86l-64-42 80-88 40 72h56' class='ionicon-fill-none ionicon-stroke-width'/><path d='M320 136a31.89 31.89 0 0032-32.1A31.55 31.55 0 00320.2 72a32 32 0 10-.2 64z'/></svg>";var d1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M451 374c-15.88-16-54.34-39.35-73-48.76-24.3-12.24-26.3-13.24-45.4.95-12.74 9.47-21.21 17.93-36.12 14.75s-47.31-21.11-75.68-49.39-47.34-61.62-50.53-76.48 5.41-23.23 14.79-36c13.22-18 12.22-21 .92-45.3-8.81-18.9-32.84-57-48.9-72.8C119.9 44 119.9 47 108.83 51.6A160.15 160.15 0 0083 65.37C67 76 58.12 84.83 51.91 98.1s-9 44.38 23.07 102.64 54.57 88.05 101.14 134.49S258.5 406.64 310.85 436c64.76 36.27 89.6 29.2 102.91 23s22.18-15 32.83-31a159.09 159.09 0 0013.8-25.8C465 391.17 468 391.17 451 374z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var x1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 224l37.78-88.15C123.93 121.5 139.6 112 157.11 112h197.78c17.51 0 33.18 9.5 39.33 23.85L432 224M80 224h352v144H80zM112 368v32H80v-32M432 368v32h-32v-32' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='144' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='368' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var m1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>";var u1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",M1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 256c0-114.87-93.13-208-208-208S48 141.13 48 256s93.13 208 208 208 208-93.13 208-208zm-100.69-28.69l-96 96a16 16 0 01-22.62 0l-96-96a16 16 0 0122.62-22.62L256 289.37l84.69-84.68a16 16 0 0122.62 22.62z'/></svg>",z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M352 216l-96 96-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var f1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>";var k1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 328l144-144 144 144' class='ionicon-fill-none'/></svg>";var B1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>";var L1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M368 368L144 144M368 144L144 368' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var H1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 240c-8.89-89.54-71-144-144-144-69 0-113.44 48.2-128 96-60 6-112 43.59-112 112 0 66 54 112 120 112h260c55 0 100-27.44 100-88 0-59.82-53-85.76-96-88z' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var C1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 448a15.92 15.92 0 01-11.31-4.69l-352-352a16 16 0 0122.62-22.62l352 352A16 16 0 01432 448zM255.66 384c-41.49 0-81.5-12.28-118.92-36.5-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 00.14-2.94L93.5 161.38a2 2 0 00-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0075.8-12.58 2 2 0 00.77-3.31l-21.58-21.58a4 4 0 00-3.83-1 204.8 204.8 0 01-51.16 6.47zM490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 00-74.89 12.83 2 2 0 00-.75 3.31l21.55 21.55a4 4 0 003.88 1 192.82 192.82 0 0150.21-6.69c40.69 0 80.58 12.43 118.55 37 34.71 22.4 65.74 53.88 89.76 91a.13.13 0 010 .16 310.72 310.72 0 01-64.12 72.73 2 2 0 00-.15 2.95l19.9 19.89a2 2 0 002.7.13 343.49 343.49 0 0068.64-78.48 32.2 32.2 0 00-.1-34.78z'/><path d='M256 160a95.88 95.88 0 00-21.37 2.4 2 2 0 00-1 3.38l112.59 112.56a2 2 0 003.38-1A96 96 0 00256 160zM165.78 233.66a2 2 0 00-3.38 1 96 96 0 00115 115 2 2 0 001-3.38z'/></svg>";var V1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 00-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 000-17.47C428.89 172.28 347.8 112 255.66 112z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='256' r='80' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var A1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 80a176 176 0 10176 176A176 176 0 00256 80z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 202.29s.84-17.5 19.57-32.57C230.68 160.77 244 158.18 256 158c10.93-.14 20.69 1.67 26.53 4.45 10 4.76 29.47 16.38 29.47 41.09 0 26-17 37.81-36.37 50.8S251 281.43 251 296' stroke-linecap='round' stroke-miterlimit='10' stroke-width='28' class='ionicon-fill-none'/><circle cx='250' cy='348' r='20'/></svg>";var y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 212v236a16 16 0 0016 16h96V328a24 24 0 0124-24h80a24 24 0 0124 24v136h96a16 16 0 0016-16V212' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M480 256L266.89 52c-5-5.28-16.69-5.34-21.78 0L32 256M400 179V64h-48v69' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var S1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M220 220h32v116' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M208 340h88' class='ionicon-fill-none ionicon-stroke-width'/><path d='M248 130a26 26 0 1026 26 26 26 0 00-26-26z'/></svg>";var j1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40' class='ionicon-fill-none'/><path d='M256 112a144 144 0 10144 144 144 144 0 00-144-144z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",O1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40M256 112a144 144 0 10144 144 144 144 0 00-144-144zM416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",b1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='192' r='32'/><path d='M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z'/></svg>",q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48c-79.5 0-144 61.39-144 137 0 87 96 224.87 131.25 272.49a15.77 15.77 0 0025.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='192' r='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var D1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M336 208v-95a80 80 0 00-160 0v95' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='208' width='320' height='272' rx='48' ry='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var P1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><rect x='48' y='96' width='416' height='320' rx='40' ry='40' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M112 160l144 112 144-112' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var F1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M313.27 124.64L198.73 51.36a32 32 0 00-29.28.35L56.51 127.49A16 16 0 0048 141.63v295.8a16 16 0 0023.49 14.14l97.82-63.79a32 32 0 0129.5-.24l111.86 73a32 32 0 0029.27-.11l115.43-75.94a16 16 0 008.63-14.2V74.57a16 16 0 00-23.49-14.14l-98 63.86a32 32 0 01-29.24.35zM328 128v336M184 48v336' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var _1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var E1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M272 464a16 16 0 01-16-16.42V264.13a8 8 0 00-8-8H64.41a16.31 16.31 0 01-15.49-10.65 16 16 0 018.41-19.87l384-176.15a16 16 0 0121.22 21.19l-176 384A16 16 0 01272 464z'/></svg>";var I1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 64L64 240.14h200a8 8 0 018 8V448z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var U1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M427.68 351.43C402 320 383.87 304 383.87 217.35 383.87 138 343.35 109.73 310 96c-4.43-1.82-8.6-6-9.95-10.55C294.2 65.54 277.8 48 256 48s-38.21 17.55-44 37.47c-1.35 4.6-5.52 8.71-9.95 10.53-33.39 13.75-73.87 41.92-73.87 121.35C128.13 304 110 320 84.32 351.43 73.68 364.45 83 384 101.61 384h308.88c18.51 0 27.77-19.61 17.19-32.57zM320 384v16a64 64 0 01-128 0v-16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var T1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M402 168c-2.93 40.67-33.1 72-66 72s-63.12-31.32-66-72c-3-42.31 26.37-72 66-72s69 30.46 66 72z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M336 304c-65.17 0-127.84 32.37-143.54 95.41-2.08 8.34 3.15 16.59 11.72 16.59h263.65c8.57 0 13.77-8.25 11.72-16.59C463.85 335.36 401.18 304 336 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 185.94c-2.34 32.48-26.72 58.06-53 58.06s-50.7-25.57-53-58.06C91.61 152.15 115.34 128 147 128s55.39 24.77 53 57.94z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M206 306c-18.05-8.27-37.93-11.45-59-11.45-52 0-102.1 25.85-114.65 76.2-1.65 6.66 2.53 13.25 9.37 13.25H154' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var R1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M344 144c-3.92 52.87-44 96-88 96s-84.15-43.12-88-96c-4-55 35-96 88-96s92 42 88 96z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 304c-87 0-175.3 48-191.64 138.6C62.39 453.52 68.57 464 80 464h352c11.44 0 17.62-10.48 15.65-21.4C431.3 352 343 304 256 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var N1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M320 146s24.36-12-64-12a160 160 0 10160 160' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 58l80 80-80 80' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var W1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M456.69 421.39L362.6 327.3a173.81 173.81 0 0034.84-104.58C397.44 126.38 319.06 48 222.72 48S48 126.38 48 222.72s78.38 174.72 174.72 174.72A173.81 173.81 0 00327.3 362.6l94.09 94.09a25 25 0 0035.3-35.3zM97.92 222.72a124.8 124.8 0 11124.8 124.8 124.95 124.95 0 01-124.8-124.8z'/></svg>";var $1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var G1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M262.29 192.31a64 64 0 1057.4 57.4 64.13 64.13 0 00-57.4-57.4zM416.39 256a154.34 154.34 0 01-1.53 20.79l45.21 35.46a10.81 10.81 0 012.45 13.75l-42.77 74a10.81 10.81 0 01-13.14 4.59l-44.9-18.08a16.11 16.11 0 00-15.17 1.75A164.48 164.48 0 01325 400.8a15.94 15.94 0 00-8.82 12.14l-6.73 47.89a11.08 11.08 0 01-10.68 9.17h-85.54a11.11 11.11 0 01-10.69-8.87l-6.72-47.82a16.07 16.07 0 00-9-12.22 155.3 155.3 0 01-21.46-12.57 16 16 0 00-15.11-1.71l-44.89 18.07a10.81 10.81 0 01-13.14-4.58l-42.77-74a10.8 10.8 0 012.45-13.75l38.21-30a16.05 16.05 0 006-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 00-6.07-13.94l-38.19-30A10.81 10.81 0 0149.48 186l42.77-74a10.81 10.81 0 0113.14-4.59l44.9 18.08a16.11 16.11 0 0015.17-1.75A164.48 164.48 0 01187 111.2a15.94 15.94 0 008.82-12.14l6.73-47.89A11.08 11.08 0 01213.23 42h85.54a11.11 11.11 0 0110.69 8.87l6.72 47.82a16.07 16.07 0 009 12.22 155.3 155.3 0 0121.46 12.57 16 16 0 0015.11 1.71l44.89-18.07a10.81 10.81 0 0113.14 4.58l42.77 74a10.8 10.8 0 01-2.45 13.75l-38.21 30a16.05 16.05 0 00-6.05 14.08c.33 4.14.55 8.3.55 12.47z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var X1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M120 352l-24 48M136 432l-16 32M400 352l-24 48M416 432l-16 32M208 304l-16 96h48v80l80-112h-48l16-64M404.33 152.89H392.2C384.71 84.85 326.14 32 256 32a136.39 136.39 0 00-128.63 90.67h-4.57c-49.94 0-90.8 40.8-90.8 90.66h0C32 263.2 72.86 304 122.8 304h281.53C446 304 480 270 480 228.44h0c0-41.55-34-75.55-75.67-75.55z' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 128v144h96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var J1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M314.21 482.32l-56.77-114.74-44.89-57.39a72.82 72.82 0 01-10.13-37.05V144h15.67a40.22 40.22 0 0140.23 40.22v183.36M127.9 293.05v-74.52S165.16 144 202.42 144M370.1 274.42L304 231M170.53 478.36L224 400' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='258.32' cy='69.48' r='37.26' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M85.57 446.25h340.86a32 32 0 0028.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0028.17 47.17z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 195.39l5.74 122 5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 5.95z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 397.25a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var Z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 320c0 88.37-55.63 144-144 144s-144-55.63-144-144c0-94.83 103.23-222.85 134.89-259.88a12 12 0 0118.23 0C296.77 97.15 400 225.17 400 320z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M344 328a72 72 0 01-72 72' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";function f(){y({"thunderstorm-outline":X1,"water-outline":Z1,"cloud-outline":H1,"chevron-up":k1,"chevron-back-outline":m1,"chevron-down":u1,"chevron-down-circle":M1,"chevron-down-circle-outline":z1,"chevron-down-outline":f1,"arrow-forward-outline":h1,"walk-outline":J1,"bicycle-outline":p1,"car-outline":x1,"navigate-outline":I1,navigate:E1,"location-outline":q1,location:b1,"locate-outline":j1,locate:O1,"map-outline":F1,"time-outline":Q1,"information-circle-outline":S1,"help-circle-outline":A1,"alert-circle-outline":w1,"warning-outline":Y1,"call-outline":d1,"people-outline":T1,"person-outline":R1,"mail-outline":P1,"close-outline":L1,"close-circle":B1,"search-outline":$1,search:W1,"refresh-outline":N1,"menu-outline":_1,"settings-outline":G1,"home-outline":y1,"notifications-outline":U1,"lock-closed-outline":D1,"eye-outline":V1,"eye-off-outline":C1})}function e2(o,e){if(o&1&&(i(0,"div",8),v(1,"ion-icon",14),i(2,"span",10),r(3,"Cached Data:"),n(),i(4,"span",15),r(5),n()()),o&2){let a=h(2);l(5),E(" ",a.offlineDataSummary.evacuationCenters," centers, ",a.formatCacheSize(a.offlineDataSummary.cacheSize)," ")}}function c2(o,e){if(o&1&&(i(0,"div",8),v(1,"ion-icon",16),i(2,"span",10),r(3,"Location:"),n(),i(4,"span",11),r(5),n()()),o&2){let a=h(2);l(4),c("ngClass",a.offlineDataSummary.userLocation?"cached":"not-cached"),l(),u(" ",a.offlineDataSummary.userLocation?"Cached":"Not cached"," ")}}function r2(o,e){if(o&1&&(i(0,"div",8),v(1,"ion-icon",17),i(2,"span",10),r(3,"Emergency Contacts:"),n(),i(4,"span",15),r(5),n()()),o&2){let a=h(2);l(5),u("",a.offlineDataSummary.emergencyContacts," contacts")}}function v2(o,e){if(o&1){let a=F();i(0,"div",18)(1,"ion-button",19),C("click",function(){q(a);let t=h(2);return D(t.clearOfflineData())}),v(2,"ion-icon",20),r(3," Clear Cache "),n()()}}function g2(o,e){if(o&1&&(i(0,"div",6)(1,"div",7)(2,"div",8),v(3,"ion-icon",9),i(4,"span",10),r(5,"Network:"),n(),i(6,"span",11),r(7),n()(),H(8,e2,6,2,"div",12)(9,c2,6,2,"div",12)(10,r2,6,1,"div",12)(11,v2,4,0,"div",13),n()()),o&2){let a=h();c("@slideDown",void 0),l(6),c("ngClass",a.isOnline?"online":"offline"),l(),u(" ",a.isOnline?"Connected":"Disconnected"," "),l(),c("ngIf",a.offlineDataSummary),l(),c("ngIf",a.offlineDataSummary),l(),c("ngIf",a.offlineDataSummary&&a.offlineDataSummary.emergencyContacts>0),l(),c("ngIf",a.hasOfflineData)}}var o2=(()=>{class o{constructor(){this.isOnline=navigator.onLine,this.hasOfflineData=!1,this.offlineDataSummary=null,this.showDetails=!1,this.offlineStorage=b(l1),this.onOnline=()=>{this.isOnline=!0,console.log("\u{1F4F6} Network status: ONLINE")},this.onOffline=()=>{this.isOnline=!1,console.log("\u{1F4F5} Network status: OFFLINE")}}ngOnInit(){this.setupNetworkListeners(),this.setupOfflineDataListener(),this.loadOfflineDataSummary()}ngOnDestroy(){this.onlineSubscription?.unsubscribe(),this.offlineDataSubscription?.unsubscribe(),window.removeEventListener("online",this.onOnline),window.removeEventListener("offline",this.onOffline)}setupNetworkListeners(){window.addEventListener("online",this.onOnline),window.addEventListener("offline",this.onOffline)}setupOfflineDataListener(){this.offlineDataSubscription=this.offlineStorage.offlineDataAvailable$.subscribe(a=>{this.hasOfflineData=a,a&&this.loadOfflineDataSummary()})}loadOfflineDataSummary(){return g(this,null,function*(){try{this.offlineDataSummary=yield this.offlineStorage.getOfflineDataSummary()}catch(a){console.error("Failed to load offline data summary:",a)}})}toggleDetails(){this.showDetails=!this.showDetails,this.showDetails&&this.loadOfflineDataSummary()}getStatusText(){return this.isOnline?this.hasOfflineData?"Online (Cached data available)":"Online":this.hasOfflineData?"Offline (Using cached data)":"Offline (No cached data)"}getStatusColor(){return this.isOnline?"success":this.hasOfflineData?"warning":"danger"}getStatusIcon(){return this.isOnline?"wifi":this.hasOfflineData?"cloud-offline":"cloud-offline-outline"}clearOfflineData(){return g(this,null,function*(){try{yield this.offlineStorage.clearAll(),this.hasOfflineData=!1,this.offlineDataSummary=null,console.log("\u{1F9F9} Cleared all offline data")}catch(a){console.error("Failed to clear offline data:",a)}})}formatCacheSize(a){return a<1024?`${a} KB`:`${(a/1024).toFixed(1)} MB`}static{this.\u0275fac=function(s){return new(s||o)}}static{this.\u0275cmp=m({type:o,selectors:[["app-offline-status"]],standalone:!0,features:[M],decls:7,vars:5,consts:[[1,"offline-status-banner",3,"ngClass"],[1,"status-content",3,"click"],[1,"status-icon",3,"name"],[1,"status-text"],[1,"toggle-icon",3,"name"],["class","status-details",4,"ngIf"],[1,"status-details"],[1,"details-content"],[1,"detail-item"],["name","wifi",1,"detail-icon"],[1,"detail-label"],[1,"detail-value",3,"ngClass"],["class","detail-item",4,"ngIf"],["class","action-buttons",4,"ngIf"],["name","archive",1,"detail-icon"],[1,"detail-value"],["name","location",1,"detail-icon"],["name","call",1,"detail-icon"],[1,"action-buttons"],["size","small","fill","clear","color","danger",3,"click"],["name","trash","slot","start"]],template:function(s,t){s&1&&(i(0,"div",0)(1,"div",1),C("click",function(){return t.toggleDetails()}),v(2,"ion-icon",2),i(3,"span",3),r(4),n(),v(5,"ion-icon",4),n(),H(6,g2,12,7,"div",5),n()),s&2&&(c("ngClass","status-"+t.getStatusColor()),l(2),c("name",t.getStatusIcon()),l(2),_(t.getStatusText()),l(),c("name",t.showDetails?"chevron-up":"chevron-down"),l(),c("ngIf",t.showDetails))},dependencies:[T,I,U,z,s1,t1],styles:[".offline-status-banner[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:9999;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-bottom:1px solid rgba(255,255,255,.1);transition:all .3s ease}.offline-status-banner.status-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745e6,#198754e6);color:#fff}.offline-status-banner.status-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107e6,#ff8f00e6);color:#212529}.offline-status-banner.status-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545e6,#b02a37e6);color:#fff}.status-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:8px 16px;cursor:pointer;-webkit-user-select:none;user-select:none}.status-content[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:8px}.status-content[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{flex:1;text-align:center;font-size:14px;font-weight:500}.status-content[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%]{font-size:16px;margin-left:8px;transition:transform .3s ease}.status-details[_ngcontent-%COMP%]{border-top:1px solid rgba(255,255,255,.2);background:#0000001a;overflow:hidden}.details-content[_ngcontent-%COMP%]{padding:12px 16px}.detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:8px;font-size:13px}.detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.detail-item[_ngcontent-%COMP%]   .detail-icon[_ngcontent-%COMP%]{font-size:14px;margin-right:8px;opacity:.8;width:16px}.detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{font-weight:500;margin-right:8px;min-width:80px}.detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{flex:1}.detail-item[_ngcontent-%COMP%]   .detail-value.online[_ngcontent-%COMP%]{color:#28a745;font-weight:500}.detail-item[_ngcontent-%COMP%]   .detail-value.offline[_ngcontent-%COMP%]{color:#dc3545;font-weight:500}.detail-item[_ngcontent-%COMP%]   .detail-value.cached[_ngcontent-%COMP%]{color:#28a745}.detail-item[_ngcontent-%COMP%]   .detail-value.not-cached[_ngcontent-%COMP%]{color:#6c757d}.action-buttons[_ngcontent-%COMP%]{margin-top:12px;padding-top:8px;border-top:1px solid rgba(255,255,255,.1);display:flex;justify-content:center}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: currentColor;font-size:12px}@keyframes _ngcontent-%COMP%_slideDown{0%{max-height:0;opacity:0}to{max-height:200px;opacity:1}}.status-details[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideDown .3s ease-out}@media (max-width: 768px){.status-content[_ngcontent-%COMP%]{padding:6px 12px}.status-content[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:13px}.details-content[_ngcontent-%COMP%]{padding:10px 12px}.detail-item[_ngcontent-%COMP%]{font-size:12px}.detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{min-width:70px}}@media (prefers-color-scheme: dark){.offline-status-banner.status-warning[_ngcontent-%COMP%]{color:#fff}.status-details[_ngcontent-%COMP%]{background:#ffffff0d}}"]})}}return o})();var a2=(()=>{class o{constructor(a,s,t,d){this.platform=a,this.fcmService=s,this.emergencyOverlay=t,this.emergencyContacts=d;try{f()}catch(n2){console.log("Error registering icons:",n2)}this.initializeApp()}initializeApp(){this.platform.ready().then(()=>{try{console.log("App initialization started"),this.checkForNotificationInUrl(),this.initializeFCM(),this.initializeEmergencyContacts(),console.log("App initialization completed successfully")}catch(a){console.error("Error during app initialization:",a)}}).catch(a=>{console.error("Error in platform.ready():",a)})}initializeFCM(){return g(this,null,function*(){try{console.log("Initializing FCM..."),yield this.fcmService.initializeFCM(),console.log("FCM initialized successfully"),setTimeout(()=>{this.fcmService.retryTokenRegistration()},2e3)}catch(a){console.error("Error initializing FCM:",a)}})}initializeEmergencyContacts(){return g(this,null,function*(){try{console.log("Initializing emergency contacts..."),yield this.emergencyContacts.initializeEmergencyContacts(),console.log("Emergency contacts initialized successfully")}catch(a){console.error("Error initializing emergency contacts:",a)}})}checkForNotificationInUrl(){try{let a=new URL(window.location.href),s=a.searchParams.get("notification");if(s)try{let t=JSON.parse(decodeURIComponent(s));console.log("Notification received from URL:",t),a.searchParams.delete("notification"),window.history.replaceState({},document.title,a.toString())}catch(t){console.error("Error parsing notification from URL:",t)}}catch(a){console.error("Error checking for notification in URL:",a)}}static{this.\u0275fac=function(s){return new(s||o)(p(Y),p(r1),p(c1),p(v1))}}static{this.\u0275cmp=m({type:o,selectors:[["app-root"]],standalone:!0,features:[M],decls:3,vars:0,template:function(s,t){s&1&&(i(0,"ion-app"),v(1,"app-offline-status")(2,"ion-router-outlet"),n())},dependencies:[o1,K,o2]})}}return o})();var s2=[{path:"",redirectTo:"/intro",pathMatch:"full"},{path:"intro",loadComponent:()=>import("./chunk-INQGR6VG.js").then(o=>o.IntroPage)},{path:"login",loadComponent:()=>import("./chunk-4WYMHFJ4.js").then(o=>o.LoginPage)},{path:"register",loadComponent:()=>import("./chunk-6QJ6E6SX.js").then(o=>o.RegisterPage)},{path:"welcome",loadComponent:()=>import("./chunk-RYLPRCGJ.js").then(o=>o.WelcomePage)},{path:"onboarding-2",loadComponent:()=>import("./chunk-6JUX7HEY.js").then(o=>o.Onboarding2Page)},{path:"onboarding-3",loadComponent:()=>import("./chunk-V7HRHHRZ.js").then(o=>o.Onboarding3Page)},{path:"onboarding-4",loadComponent:()=>import("./chunk-N742WJGS.js").then(o=>o.Onboarding4Page)},{path:"earthquake-map",loadComponent:()=>import("./chunk-35XOTFT7.js").then(o=>o.EarthquakeMapPage)},{path:"typhoon-map",loadComponent:()=>import("./chunk-X6YPNIS4.js").then(o=>o.TyphoonMapPage)},{path:"flood-map",loadComponent:()=>import("./chunk-YSHQM4JL.js").then(o=>o.FloodMapPage)},{path:"fire-map",loadComponent:()=>import("./chunk-CKYYM2AS.js").then(o=>o.FireMapPage)},{path:"landslide-map",loadComponent:()=>import("./chunk-NJT3H4TT.js").then(o=>o.LandslideMapPage)},{path:"all-maps",loadComponent:()=>import("./chunk-2JYUOIS3.js").then(o=>o.AllMapsPage)},{path:"offline-data",loadComponent:()=>import("./chunk-5AR33SU4.js").then(o=>o.OfflineDataPage)},{path:"ors-test",loadChildren:()=>import("./chunk-5KE5PSGV.js").then(o=>o.OrsTestPageModule)},{path:"real-time-demo",loadComponent:()=>import("./chunk-5DIZHXSW.js").then(o=>o.RealTimeDemoPage)},{path:"tabs",loadComponent:()=>import("./chunk-RL76RPQE.js").then(o=>o.TabsPage),children:[{path:"home",loadComponent:()=>import("./chunk-FVMZXJ4W.js").then(o=>o.HomePage)},{path:"search",loadComponent:()=>import("./chunk-2EDC65QK.js").then(o=>o.SearchPage)},{path:"map",loadComponent:()=>import("./chunk-SPGXZV4V.js").then(o=>o.MapPage)},{path:"profile",loadComponent:()=>import("./chunk-HSU7RAT6.js").then(o=>o.ProfilePage)},{path:"earthquake-map",loadComponent:()=>import("./chunk-35XOTFT7.js").then(o=>o.EarthquakeMapPage)},{path:"typhoon-map",loadComponent:()=>import("./chunk-X6YPNIS4.js").then(o=>o.TyphoonMapPage)},{path:"flood-map",loadComponent:()=>import("./chunk-YSHQM4JL.js").then(o=>o.FloodMapPage)},{path:"fire-map",loadComponent:()=>import("./chunk-CKYYM2AS.js").then(o=>o.FireMapPage)},{path:"landslide-map",loadComponent:()=>import("./chunk-NJT3H4TT.js").then(o=>o.LandslideMapPage)},{path:"all-maps",loadComponent:()=>import("./chunk-2JYUOIS3.js").then(o=>o.AllMapsPage)},{path:"offline-data",loadComponent:()=>import("./chunk-5AR33SU4.js").then(o=>o.OfflineDataPage)},{path:"",redirectTo:"home",pathMatch:"full"}]}];var B=(()=>{class o{constructor(a){this.alertCtrl=a}handleError(a){let s="An unknown error occurred";return a.error instanceof ErrorEvent?(s=`Error: ${a.error.message}`,console.error("Client-side error:",a.error.message)):(s=`Error Code: ${a.status}
Message: ${a.message}`,console.error(`Server-side error: Status: ${a.status}, Body: ${JSON.stringify(a.error)}`)),console.error("HTTP error:",a),S(()=>a)}showErrorAlert(a,s){return g(this,null,function*(){yield(yield this.alertCtrl.create({header:a,message:s,buttons:["OK"]})).present()})}handleAppError(a,s){console.error(`Error in ${s||"Unknown component"}:`,a),a&&a.isCritical&&this.showErrorAlert("Application Error","An unexpected error occurred. Please restart the application.")}static{this.\u0275fac=function(s){return new(s||o)(x(i1))}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();var t2=(()=>{class o{constructor(a){this.errorHandlerService=a}intercept(a,s){return s.handle(a).pipe(O(1),j(t=>(console.log("HTTP Error Interceptor caught an error:",t),this.errorHandlerService.handleError(t))))}static{this.\u0275fac=function(s){return new(s||o)(x(B))}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac})}}return o})();var i2=(()=>{class o{constructor(){}intercept(a,s){let t=localStorage.getItem("token");if(t){let d=a.clone({headers:a.headers.set("Authorization",`Bearer ${t}`)});return s.handle(d)}return s.handle(a)}static{this.\u0275fac=function(s){return new(s||o)}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac})}}return o})();f();e1.production&&void 0;W(a2,{providers:[{provide:$,useClass:Z},{provide:P,useClass:B},{provide:V,useClass:i2,multi:!0},{provide:V,useClass:t2,multi:!0},a1({}),L(z.forRoot()),L(n1.forRoot()),X(s2,J(),Q(G)),R(N())]}).catch(o=>console.log(o));
