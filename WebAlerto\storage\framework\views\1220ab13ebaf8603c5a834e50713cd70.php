<?php $__env->startSection('content'); ?>

<?php
    $loggedInUser = auth()->user();
    $loggedInUserRole = $loggedInUser ? ($loggedInUser->role ?? 'No Role Attribute') : 'Not Logged In';
?>



<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class=" flex-cflexol lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <?php if(auth()->user()->hasRole('super_admin')): ?>
                            <i class="fas fa-users text-white text-2xl"></i>
                        <?php else: ?>
                            <i class="fas fa-users text-white text-2xl"></i>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if(auth()->user()->hasRole('super_admin')): ?>
                            <h1 class="text-3xl font-bold text-gray-900">CDRRMC Users</h1>
                            <p class="text-gray-600 mt-1">Manage fellow CDRRMC users in <?php echo e(auth()->user()->city); ?></p>
                        <?php else: ?>
                            <h1 class="text-3xl font-bold text-gray-900">Barangay Users</h1>
                            <p class="text-gray-600 mt-1">Manage users from <?php echo e(auth()->user()->barangay); ?></p>
                            <?php if(auth()->user()->isChairman()): ?>
                                <div class="mt-2 flex items-center gap-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-crown mr-1"></i>
                                        Chairman Authority
                                    </span>
                                    <span class="text-xs text-gray-500">Enhanced user management capabilities</span>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Chairman Management Button -->
            <?php if(auth()->user()->isChairman()): ?>
                <div class="flex justify-end">
                    <button onclick="showNewUserRequestModal()"
                            class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-medium shadow-lg transition-all duration-200">
                        <i class="fas fa-user-plus"></i>
                        Request New User Registration
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <!-- Header -->
            <div class="flex items-center gap-3 mb-6">
                <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                    <i class="fas fa-search text-white text-xl"></i>
                </div>
                <div>
                    <?php if(auth()->user()->hasRole('super_admin')): ?>
                        <h2 class="text-xl font-bold text-gray-900">Search & Filter</h2>
                        <p class="text-sm text-gray-600 mt-1">Search and filter CDRRMC users</p>
                    <?php else: ?>
                        <h2 class="text-xl font-bold text-gray-900">Find Barangay Users</h2>
                        <p class="text-sm text-gray-600 mt-1">Search users from <?php echo e(auth()->user()->barangay); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Search Bar - Top, Full Width -->
            <div class="mb-6">
                <div class="relative">
                    <?php if(auth()->user()->hasRole('super_admin')): ?>
                        <input type="text" name="search" id="searchInput"
                               class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400 pl-12"
                               placeholder="Search by name or email..." value="<?php echo e($searchQuery ?? ''); ?>">
                    <?php else: ?>
                        <input type="text" name="search" id="searchInput"
                               class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400 pl-12"
                               placeholder="Search by name or email..." value="<?php echo e($searchQuery ?? ''); ?>">
                    <?php endif; ?>
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Controls -->
            <form action="<?php echo e(route('components.user-management')); ?>" method="GET">
                <input type="hidden" name="search" value="<?php echo e($searchQuery ?? ''); ?>">
                <div class="grid grid-cols-1 gap-4">
                    <?php if(auth()->user()->hasRole('super_admin')): ?>
                        <!-- Barangay Filter -->
                        <div class="relative">
                            <label for="barangay" class="block text-sm font-semibold text-sky-600 mb-2">Filter by Barangay</label>
                            <select name="barangay" id="barangay"
                                    class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 px-4 text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400"
                                    onchange="this.form.submit()">
                                <option value="">All Barangays</option>
                                <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($barangay); ?>" <?php echo e($selectedBarangay == $barangay ? 'selected' : ''); ?>>
                                        <?php echo e($barangay); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Clear Search Button -->
        <?php if($searchQuery): ?>
            <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
                <div class="flex justify-start">
                    <a href="<?php echo e(route('components.user-management')); ?>" class="inline-flex items-center justify-center px-4 py-2 bg-gray-500 text-white rounded-xl font-semibold shadow-lg transition-all hover:bg-gray-600">
                        <i class="fas fa-times"></i> Clear Search
                    </a>
                </div>
            </div>
        <?php endif; ?>


        <!-- Mobile User Table -->

        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID no.</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                            <?php if(auth()->user()->hasRole('super_admin')): ?>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Mobile Number</th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Barangay</th>
                            <?php else: ?>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-briefcase text-sky-600"></i>
                                        Position & Role
                                    </div>
                                </th>
                            <?php endif; ?>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="userTableBody">
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($user->id); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                                            <?php if($userType === 'mobile'): ?>
                                                <?php echo e($user->full_name ? strtoupper(substr($user->full_name, 0, 1)) : '?'); ?>

                                            <?php else: ?>
                                                <?php echo e($user->first_name ? strtoupper(substr($user->first_name, 0, 1)) : '?'); ?>

                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php if($userType === 'mobile'): ?>
                                                    <?php echo e($user->full_name); ?>

                                                <?php else: ?>
                                                    <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>

                                                <?php endif; ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php if($userType === 'mobile'): ?>
                                                    Mobile User
                                                <?php else: ?>
                                                    <?php echo e($user->email); ?>

                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <?php if($userType === 'mobile'): ?>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600"><?php echo e($user->mobile_number); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600"><?php echo e($user->barangay); ?></td>
                                <?php else: ?>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600"><?php echo e($user->email); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($user->position ?? 'Not Set'); ?></div>
                                        <div class="text-sm text-gray-600">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php echo e($user->role === 'super_admin' ? 'bg-purple-100 text-purple-800' :
                                                   ($user->role === 'chairman' ? 'bg-yellow-100 text-yellow-800' : 'bg-sky-100 text-sky-800')); ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $user->role))); ?>

                                            </span>
                                        </div>
                                    </td>
                                <?php endif; ?>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                    <div class="flex justify-center space-x-2">
                                        <?php if($userType === 'mobile'): ?>
                                            <button onclick="showMobileUserDetails(<?php echo e($user->id); ?>, '<?php echo e($user->full_name); ?>', '<?php echo e($user->mobile_number); ?>', '<?php echo e($user->barangay); ?>')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg text-xs font-semibold shadow-sm transition-all duration-200">
                                                <i class="fas fa-eye mr-1"></i> View
                                            </button>
                                        <?php else: ?>
                                            <?php if($user->id !== auth()->id()): ?>
                                                <button onclick="showBarangayUserDetails(<?php echo e($user->id); ?>, '<?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>', '<?php echo e($user->email); ?>', '<?php echo e($user->role); ?>', '<?php echo e($user->position); ?>')"
                                                        class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg text-xs font-semibold shadow-sm transition-all duration-200">
                                                    <i class="fas fa-eye mr-1"></i> View
                                                </button>
                                            <?php else: ?>
                                                <div class="flex items-center justify-center">
                                                    <span class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-lg text-xs font-semibold border border-green-200">
                                                        <i class="fas fa-user-check mr-1"></i> Current User
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                <?php if($userType === 'mobile'): ?>
                                    No mobile users found
                                <?php else: ?>
                                    No barangay users found
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($users->hasPages()): ?>
        <div class="mt-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 px-6 py-4">
                <?php echo e($users->links()); ?>

            </div>
        </div>
        <?php endif; ?>
        </div>
    </div>
</div>

<!-- Mobile User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-lg mx-4 border border-gray-200 max-h-[90vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-5 rounded-t-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-white/20 backdrop-blur-sm rounded-xl shadow-lg">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold text-white" id="modalTitle">User Profile</h3>
                        <p class="text-sky-100 text-sm mt-1" id="modalSubtitle">User information</p>
                    </div>
                </div>
                <button onclick="closeUserDetailsModal()" class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-y-auto">
            <div id="userDetailsContent" class="space-y-4"></div>

            <!-- Only show Deactivate Form if Officer -->
            <?php if(auth()->check() && (auth()->user()->role === 'super_admin' || auth()->user()->role === 'officer')): ?>
                <div id="deactivateButtonContainer"></div>
            <?php endif; ?>

            <!-- Chairman Request Actions -->
            <?php if(auth()->user()->isChairman()): ?>
                <div id="chairmanActionsContainer"></div>
            <?php endif; ?>
        </div>
    </div>
</div>
<!-- Chairman Request Modal -->
<?php if(auth()->user()->isChairman()): ?>
<div id="requestModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-yellow-500 to-orange-500 px-6 py-5">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-crown text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 id="requestModalTitle" class="text-xl font-bold text-white">Chairman Request</h3>
                        <p class="text-yellow-100 text-sm">User Management Action</p>
                    </div>
                </div>
                <button onclick="closeRequestModal()" class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="requestForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="requestUserId" name="target_user_id">
                <input type="hidden" id="requestAction" name="action_type">

                <!-- User Info Section -->
                <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-sky-50 rounded-lg border border-blue-200">
                    <div class="flex items-center gap-3 mb-2">
                        <i class="fas fa-user-circle text-blue-600 text-lg"></i>
                        <span class="text-sm font-medium text-blue-800">Target User</span>
                    </div>
                    <p class="text-gray-700 font-semibold" id="requestUserName"></p>
                    <p class="text-sm text-gray-600 mt-1">
                        This request will be sent to the SuperAdmin for review and approval.
                    </p>
                </div>

                <!-- Reason Section -->
                <div class="mb-6">
                    <label for="requestReason" class="block text-sm font-semibold text-gray-700 mb-3">
                        <i class="fas fa-edit mr-2 text-yellow-600"></i>
                        Reason for Request *
                    </label>
                    <textarea id="requestReason" name="reason" rows="4" required
                              class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors"
                              placeholder="Please provide a detailed and professional reason for this management request. Include specific incidents, policy violations, or circumstances that justify this action..."></textarea>
                    <div class="flex justify-between items-center mt-2">
                        <p class="text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Minimum 10 characters required
                        </p>
                        <span id="charCount" class="text-xs text-gray-400">0/500</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button type="button" onclick="closeRequestModal()"
                            class="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="button" onclick="submitRequest()" id="submitRequestBtn"
                            class="flex-1 px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-paper-plane mr-2"></i>Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Character Counter Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('requestReason');
    const charCount = document.getElementById('charCount');

    if (textarea && charCount) {
        textarea.addEventListener('input', function() {
            const length = this.value.length;
            charCount.textContent = `${length}/500`;

            if (length > 500) {
                charCount.classList.add('text-red-500');
                charCount.classList.remove('text-gray-400');
            } else {
                charCount.classList.remove('text-red-500');
                charCount.classList.add('text-gray-400');
            }
        });
    }
});
</script>

<!-- New User Registration Request Modal -->
<div id="newUserRequestModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-5">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-user-plus text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white">Request New User Registration</h3>
                        <p class="text-green-100 text-sm">Add new officer to your barangay</p>
                    </div>
                </div>
                <button onclick="closeNewUserRequestModal()" class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <form id="newUserRequestForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="request_type" value="registration">
                <input type="hidden" name="action_type" value="registration">

                <!-- Information Section -->
                <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-sky-50 rounded-lg border border-blue-200">
                    <div class="flex items-center gap-3 mb-2">
                        <i class="fas fa-info-circle text-blue-600 text-lg"></i>
                        <span class="text-sm font-medium text-blue-800">Registration Request</span>
                    </div>
                    <p class="text-sm text-gray-600">
                        Submit a request to the SuperAdmin to register a new officer account for your barangay.
                        All information will be reviewed before account creation.
                    </p>
                </div>

                <!-- User Information Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label for="newUserFirstName" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-green-600"></i>First Name *
                        </label>
                        <input type="text" id="newUserFirstName" name="first_name" required
                               class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                               placeholder="Enter first name">
                    </div>
                    <div>
                        <label for="newUserLastName" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-green-600"></i>Last Name *
                        </label>
                        <input type="text" id="newUserLastName" name="last_name" required
                               class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                               placeholder="Enter last name">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="newUserEmail" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-green-600"></i>Email Address *
                    </label>
                    <input type="email" id="newUserEmail" name="email" required
                           class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                           placeholder="Enter email address">
                </div>

                <div class="mb-6">
                    <label for="newUserPosition" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-briefcase mr-2 text-green-600"></i>Position *
                    </label>
                    <select id="newUserPosition" name="position" required
                            class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                        <option value="">Select Position</option>
                        <option value="Officer">Officer</option>
                        <option value="Secretary">Secretary</option>
                        <option value="Treasurer">Treasurer</option>
                        <option value="Kagawad">Kagawad</option>
                        <option value="SK Chairman">SK Chairman</option>
                        <option value="Assistant">Assistant</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="mb-6" id="customPositionDiv" style="display: none;">
                    <label for="customPosition" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-edit mr-2 text-green-600"></i>Custom Position *
                    </label>
                    <input type="text" id="customPosition" name="custom_position"
                           class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                           placeholder="Enter custom position">
                </div>

                <!-- Reason Section -->
                <div class="mb-6">
                    <label for="newUserReason" class="block text-sm font-semibold text-gray-700 mb-3">
                        <i class="fas fa-edit mr-2 text-green-600"></i>
                        Reason for Registration Request *
                    </label>
                    <textarea id="newUserReason" name="reason" rows="4" required
                              class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                              placeholder="Please provide a detailed reason for requesting this new user registration. Include their role, responsibilities, and why they need system access..."></textarea>
                    <div class="flex justify-between items-center mt-2">
                        <p class="text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Minimum 20 characters required
                        </p>
                        <span id="newUserCharCount" class="text-xs text-gray-400">0/500</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button type="button" onclick="closeNewUserRequestModal()"
                            class="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="button" onclick="submitNewUserRequest()" id="submitNewUserRequestBtn"
                            class="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-paper-plane mr-2"></i>Submit Registration Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- New User Request Modal Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for new user reason
    const newUserTextarea = document.getElementById('newUserReason');
    const newUserCharCount = document.getElementById('newUserCharCount');

    if (newUserTextarea && newUserCharCount) {
        newUserTextarea.addEventListener('input', function() {
            const length = this.value.length;
            newUserCharCount.textContent = `${length}/500`;

            if (length > 500) {
                newUserCharCount.classList.add('text-red-500');
                newUserCharCount.classList.remove('text-gray-400');
            } else {
                newUserCharCount.classList.remove('text-red-500');
                newUserCharCount.classList.add('text-gray-400');
            }
        });
    }

    // Handle custom position toggle
    const positionSelect = document.getElementById('newUserPosition');
    const customPositionDiv = document.getElementById('customPositionDiv');
    const customPositionInput = document.getElementById('customPosition');

    if (positionSelect && customPositionDiv && customPositionInput) {
        positionSelect.addEventListener('change', function() {
            if (this.value === 'Other') {
                customPositionDiv.style.display = 'block';
                customPositionInput.required = true;
            } else {
                customPositionDiv.style.display = 'none';
                customPositionInput.required = false;
                customPositionInput.value = '';
            }
        });
    }
});
</script>
<?php endif; ?>





<!-- Success and Error Flash Messages -->
<?php if(session('success')): ?>
    <div id="successAlert" class="fixed top-5 right-5 bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-4 rounded-xl shadow-lg z-50 flex items-center gap-3">
        <i class="fas fa-check-circle text-xl"></i>
        <?php echo e(session('success')); ?>

    </div>
    <script>
        setTimeout(function() {
            const successAlert = document.getElementById('successAlert');
            if (successAlert) {
                successAlert.style.opacity = '0';
                successAlert.style.transition = 'opacity 0.5s ease-in-out';
                setTimeout(() => successAlert.remove(), 500);
            }
        }, 3000);
    </script>
<?php endif; ?>

<?php if(session('error')): ?>
    <div id="errorAlert" class="fixed top-5 right-5 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-lg z-50 flex items-center gap-3">
        <i class="fas fa-exclamation-circle text-xl"></i>
        <?php echo e(session('error')); ?>

    </div>
    <script>
        setTimeout(function() {
            const errorAlert = document.getElementById('errorAlert');
            if (errorAlert) {
                errorAlert.style.opacity = '0';
                errorAlert.style.transition = 'opacity 0.5s ease-in-out';
                setTimeout(() => errorAlert.remove(), 500);
            }
        }, 3000);
    </script>
<?php endif; ?>

<!-- Scripts -->
<script>
// Set JavaScript variable based on authenticated user's role
const userRole = <?php echo json_encode(auth()->check() ? auth()->user()->role : null, 15, 512) ?>;

// Live Search (local, no reload)
document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('searchInput');
    const tableRows = document.querySelectorAll('#userTableBody tr');

    searchInput.addEventListener('input', function () {
        const query = this.value.toLowerCase().trim();
        tableRows.forEach(row => {
            const nameCell = row.querySelector('td:nth-child(2)');
            const emailCell = row.querySelector('td:nth-child(3)');
            const name = nameCell ? nameCell.textContent.toLowerCase() : '';
            const email = emailCell ? emailCell.textContent.toLowerCase() : '';
            if (name.includes(query) || email.includes(query)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});

// Show Mobile User Details
function showMobileUserDetails(id, fullName, mobileNumber, barangay) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');
    const modalTitle = document.getElementById('modalTitle');
    const modalSubtitle = document.getElementById('modalSubtitle');

    modalTitle.textContent = 'Mobile User Profile';
    modalSubtitle.textContent = 'Mobile app user information';

    content.innerHTML = `
        <div class="space-y-4">
            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-user text-sky-600 mr-2"></i>Name
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${fullName}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-phone text-sky-600 mr-2"></i>Mobile Number
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${mobileNumber}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-map-marker-alt text-sky-600 mr-2"></i>Barangay
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${barangay}</div>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

// Show Barangay User Details
function showBarangayUserDetails(id, fullName, email, role, position) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');
    const modalTitle = document.getElementById('modalTitle');
    const modalSubtitle = document.getElementById('modalSubtitle');

    modalTitle.textContent = 'Barangay User Profile';
    modalSubtitle.textContent = 'Barangay staff information';

    content.innerHTML = `
        <div class="space-y-4">
            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-user text-sky-600 mr-2"></i>Name
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${fullName}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-envelope text-sky-600 mr-2"></i>Email
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${email}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-user-tag text-sky-600 mr-2"></i>Role
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
            </div>

            ${position ? `
            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-briefcase text-sky-600 mr-2"></i>Position
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${position}</div>
            </div>
            ` : ''}
        </div>
    `;

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Add Chairman actions if applicable
    <?php if(auth()->user()->isChairman()): ?>
        addChairmanActions(id, fullName, role, position);
    <?php endif; ?>
}

// Close User Details Modal
function closeUserDetailsModal() {
    const modal = document.getElementById('userDetailsModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}



// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const userDetailsModal = document.getElementById('userDetailsModal');

    if (event.target === userDetailsModal) {
        closeUserDetailsModal();
    }
});

// Real-time search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            searchTimeout = setTimeout(() => {
                const url = new URL(window.location.href);
                if (query) {
                    url.searchParams.set('search', query);
                } else {
                    url.searchParams.delete('search');
                }
                window.location.href = url.toString();
            }, 500);
        });
    }
});

<?php if(auth()->user()->isChairman()): ?>
// Chairman-specific functions
function addChairmanActions(userId, name, role, position) {
    const container = document.getElementById('chairmanActionsContainer');

    // Debug logging
    console.log('Chairman Actions Debug:', {
        userId: userId,
        name: name,
        role: role,
        position: position,
        positionLower: position.toLowerCase(),
        isOfficer: role === 'officer',
        isNotChairman: position.toLowerCase() !== 'chairman',
        shouldShow: role === 'officer' && position.toLowerCase() !== 'chairman'
    });

    // Only show actions for non-Chairman officers in same barangay
    if (role === 'officer' && position.toLowerCase() !== 'chairman') {
        container.innerHTML = `
            <div class="mt-6 pt-6 border-t-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-crown text-white text-sm"></i>
                    </div>
                    <h4 class="ml-3 text-lg font-bold text-yellow-700">
                        Chairman Authority
                    </h4>
                </div>

                <p class="text-sm text-gray-600 mb-4">
                    <i class="fas fa-info-circle mr-2 text-yellow-600"></i>
                    As Chairman, you can request management actions for this officer. All requests require SuperAdmin approval.
                </p>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button onclick="showRequestModal(${userId}, '${name}', 'deactivate')"
                            class="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-pause text-lg"></i>
                        <span>Request Deactivation</span>
                    </button>
                    <button onclick="showRequestModal(${userId}, '${name}', 'delete')"
                            class="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-trash text-lg"></i>
                        <span>Request Deletion</span>
                    </button>
                </div>

                <div class="mt-4 p-3 bg-white/70 rounded-lg border border-yellow-200">
                    <p class="text-xs text-gray-600 flex items-center">
                        <i class="fas fa-shield-alt mr-2 text-yellow-600"></i>
                        <strong>Note:</strong> These actions will send a formal request to the SuperAdmin for review and approval.
                    </p>
                </div>
            </div>
        `;
    } else {
        container.innerHTML = '';
    }
}

function showRequestModal(userId, userName, actionType) {
    const modal = document.getElementById('requestModal');
    const form = document.getElementById('requestForm');
    const title = document.getElementById('requestModalTitle');
    const userNameSpan = document.getElementById('requestUserName');
    const actionInput = document.getElementById('requestAction');
    const userIdInput = document.getElementById('requestUserId');
    const textarea = document.getElementById('requestReason');

    // Set modal title and content based on action type
    const actionText = actionType === 'deactivate' ? 'Deactivation' : 'Deletion';
    const actionIcon = actionType === 'deactivate' ? 'fas fa-pause' : 'fas fa-trash';
    const actionColor = actionType === 'deactivate' ? 'text-yellow-600' : 'text-red-600';

    title.innerHTML = `<i class="${actionIcon} mr-2"></i>Request ${actionText}`;
    userNameSpan.innerHTML = `<i class="fas fa-user mr-2 ${actionColor}"></i>${userName}`;
    actionInput.value = actionType;
    userIdInput.value = userId;

    // Clear previous form data
    textarea.value = '';
    const charCount = document.getElementById('charCount');
    if (charCount) {
        charCount.textContent = '0/500';
        charCount.classList.remove('text-red-500');
        charCount.classList.add('text-gray-400');
    }

    // Set placeholder text based on action type
    if (actionType === 'deactivate') {
        textarea.placeholder = "Please provide a detailed reason for requesting deactivation. Include specific incidents, policy violations, or circumstances that justify temporarily suspending this user's access...";
    } else {
        textarea.placeholder = "Please provide a detailed reason for requesting deletion. Include serious violations, misconduct, or circumstances that justify permanently removing this user from the system...";
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Focus on textarea for better UX
    setTimeout(() => {
        textarea.focus();
    }, 100);
}

function closeRequestModal() {
    const modal = document.getElementById('requestModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');

    // Reset form
    document.getElementById('requestForm').reset();
}

function submitRequest() {
    const form = document.getElementById('requestForm');
    const textarea = document.getElementById('requestReason');
    const submitBtn = document.getElementById('submitRequestBtn');
    const actionType = document.getElementById('requestAction').value;
    const userName = document.getElementById('requestUserName').textContent.replace(/.*\s/, ''); // Remove icon

    // Validate form
    if (!textarea.value.trim()) {
        showValidationError('Please provide a reason for this request.');
        return;
    }

    if (textarea.value.trim().length < 10) {
        showValidationError('Reason must be at least 10 characters long.');
        return;
    }

    if (textarea.value.length > 500) {
        showValidationError('Reason cannot exceed 500 characters.');
        return;
    }

    // Show confirmation dialog
    const actionText = actionType === 'deactivate' ? 'deactivation' : 'deletion';
    if (!confirm(`Are you sure you want to request ${actionText} for ${userName}? This action will be sent to the SuperAdmin for approval.`)) {
        return;
    }

    const formData = new FormData(form);

    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting Request...';

    fetch('<?php echo e(route("user-management.chairman-requests.store")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeRequestModal();
            closeUserDetailsModal();

            // Show enhanced success message
            showSuccessMessage(data.message, actionType);
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        showErrorMessage('Error: ' + error.message);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Submit Request';
    });
}

function showValidationError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md';
    alertDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <i class="fas fa-exclamation-triangle text-xl"></i>
            <div>
                <div class="font-semibold">Validation Error</div>
                <div class="text-sm">${message}</div>
            </div>
        </div>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 4000);
}

function showSuccessMessage(message, actionType) {
    const actionIcon = actionType === 'deactivate' ? 'fas fa-pause' : 'fas fa-trash';
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md';
    alertDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <div class="flex-shrink-0 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <i class="${actionIcon} text-lg"></i>
            </div>
            <div>
                <div class="font-semibold">Request Submitted Successfully!</div>
                <div class="text-sm text-green-100">${message}</div>
            </div>
        </div>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 6000);
}

function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md';
    alertDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <i class="fas fa-exclamation-circle text-xl"></i>
            <div>
                <div class="font-semibold">Request Failed</div>
                <div class="text-sm">${message}</div>
            </div>
        </div>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// New User Registration Request Functions
function showNewUserRequestModal() {
    const modal = document.getElementById('newUserRequestModal');
    const form = document.getElementById('newUserRequestForm');

    // Reset form
    form.reset();
    const charCount = document.getElementById('newUserCharCount');
    if (charCount) {
        charCount.textContent = '0/500';
        charCount.classList.remove('text-red-500');
        charCount.classList.add('text-gray-400');
    }

    // Hide custom position div
    const customPositionDiv = document.getElementById('customPositionDiv');
    if (customPositionDiv) {
        customPositionDiv.style.display = 'none';
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Focus on first input
    setTimeout(() => {
        document.getElementById('newUserFirstName').focus();
    }, 100);
}

function closeNewUserRequestModal() {
    const modal = document.getElementById('newUserRequestModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');

    // Reset form
    document.getElementById('newUserRequestForm').reset();
}

function submitNewUserRequest() {
    const form = document.getElementById('newUserRequestForm');
    const submitBtn = document.getElementById('submitNewUserRequestBtn');

    // Get form data
    const firstName = document.getElementById('newUserFirstName').value.trim();
    const lastName = document.getElementById('newUserLastName').value.trim();
    const email = document.getElementById('newUserEmail').value.trim();
    const position = document.getElementById('newUserPosition').value;
    const customPosition = document.getElementById('customPosition').value.trim();
    const reason = document.getElementById('newUserReason').value.trim();

    // Validate form
    if (!firstName) {
        showValidationError('Please enter the first name.');
        return;
    }

    if (!lastName) {
        showValidationError('Please enter the last name.');
        return;
    }

    if (!email) {
        showValidationError('Please enter the email address.');
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showValidationError('Please enter a valid email address.');
        return;
    }

    if (!position) {
        showValidationError('Please select a position.');
        return;
    }

    if (position === 'Other' && !customPosition) {
        showValidationError('Please enter the custom position.');
        return;
    }

    if (!reason) {
        showValidationError('Please provide a reason for this registration request.');
        return;
    }

    if (reason.length < 20) {
        showValidationError('Reason must be at least 20 characters long.');
        return;
    }

    if (reason.length > 500) {
        showValidationError('Reason cannot exceed 500 characters.');
        return;
    }

    // Show confirmation dialog
    const fullName = `${firstName} ${lastName}`;
    const finalPosition = position === 'Other' ? customPosition : position;
    if (!confirm(`Are you sure you want to request registration for ${fullName} as ${finalPosition}? This request will be sent to the SuperAdmin for approval.`)) {
        return;
    }

    const formData = new FormData(form);

    // Add final position to form data
    if (position === 'Other') {
        formData.set('position', customPosition);
    }

    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting Request...';

    fetch('<?php echo e(route("user-management.chairman-requests.store")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeNewUserRequestModal();

            // Show enhanced success message
            showNewUserSuccessMessage(data.message, fullName, finalPosition);
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        showErrorMessage('Error: ' + error.message);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Submit Registration Request';
    });
}

function showNewUserSuccessMessage(message, userName, position) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md';
    alertDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <div class="flex-shrink-0 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <i class="fas fa-user-plus text-lg"></i>
            </div>
            <div>
                <div class="font-semibold">Registration Request Submitted!</div>
                <div class="text-sm text-green-100">Request for ${userName} (${position}) has been sent to SuperAdmin for approval.</div>
            </div>
        </div>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 7000);
}
<?php endif; ?>

</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel\WebAlerto\resources\views/components/user_management/user-management.blade.php ENDPATH**/ ?>