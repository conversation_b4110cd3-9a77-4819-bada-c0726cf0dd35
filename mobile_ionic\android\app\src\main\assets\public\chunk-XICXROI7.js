import{$a as K,Aa as It,Ca as bt,E as V,F as at,G as ct,Ia as ee,Ja as X,<PERSON> as P,<PERSON> as Et,Ma as St,Na as jt,Oa as Tt,P as lt,Pa as ie,Q as dt,Qa as re,Ra as se,Sa as Ee,Ta as At,U as qe,Ua as Ft,V as Ve,Va as Mt,Wa as Pt,X as pt,Xa as ae,Y as ut,Ya as ce,Z as mt,Za as le,_a as de,ab as pe,b as Ne,bb as ve,cb as Rt,db as Bt,eb as Se,fa as Ge,fb as ue,i as _e,ka as ft,la as ht,m as J,ma as gt,n as He,na as vt,ob as Ue,p as st,pb as je,qb as zt,r as Ce,rb as Ot,sb as Lt,u as We,ub as Te,v as De,x as Ze,z as Ye}from"./chunk-Q5Y64KIB.js";import{a as me,c as $t,d as Ae,g as Nt,h as Qe,i as _t}from"./chunk-P4YSCN2K.js";import{a as d,g as xt,h as we,i as ke}from"./chunk-4EMV5IOT.js";import{a as yt,b as _,d as Xe,e as h,f as Z,i as g,j as H}from"./chunk-XQ4KGEVA.js";import{a as Ke}from"./chunk-TZQIROCS.js";import{b as oe,c as Dt,d as wt,f as B,g as q,i as ge,k as kt}from"./chunk-XTVTS2NW.js";import{c as xe}from"./chunk-NMYJD6OP.js";import{a as M,e as U,f as Ct}from"./chunk-C5RQ2IC2.js";import{a as Je}from"./chunk-7D2EH4XU.js";import{a as Ht}from"./chunk-MMIXVVWR.js";import{a as he}from"./chunk-SV7S5NYR.js";import{a as Le,b as $e,h as b}from"./chunk-B7O3QC5Z.js";var Q=()=>{let n;return{lock:()=>b(void 0,null,function*(){let t=n,o;return n=new Promise(i=>o=i),t!==void 0&&(yield t),o})}};var fn=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",hn=fn,gn=H(class extends _{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=g(this,"ionNavWillLoad",7),this.ionNavWillChange=g(this,"ionNavWillChange",3),this.ionNavDidChange=g(this,"ionNavDidChange",3),this.lockController=Q(),this.gestureOrAnimationInProgress=!1,this.mode=P(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return b(this,null,function*(){let e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-T4QCNDSH.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>e(),t=>{var o;return(o=this.ani)===null||o===void 0?void 0:o.progressStep(t)},(t,o,i)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(t)},{oneTimeCallback:!0});let r=t?-.001:.001;t?r+=ee([0,0],[.32,.72],[0,1],[1,1],o)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=ee([0,0],[1,0],[.68,.28],[1,1],o)[0]),this.ani.progressEnd(t?1:0,r,i)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(e,t,o){return b(this,null,function*(){let i=yield this.lockController.lock(),r=!1;try{r=yield this.transition(e,t,o)}catch(s){Ct("[ion-router-outlet] - Exception in commit:",s)}return i(),r})}setRouteId(e,t,o,i){return b(this,null,function*(){return{changed:yield this.setRoot(e,t,{duration:o==="root"?0:void 0,direction:o==="back"?"back":"forward",animationBuilder:i}),element:this.activeEl}})}getRouteId(){return b(this,null,function*(){let e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0})}setRoot(e,t,o){return b(this,null,function*(){if(this.activeComponent===e&&kt(t,this.activeParams))return!1;let i=this.activeEl,r=yield re(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,yield this.commit(r,i,o),yield se(this.delegate,i),!0})}transition(i,r){return b(this,arguments,function*(e,t,o={}){if(t===e)return!1;this.ionNavWillChange.emit();let{el:s,mode:c}=this,a=this.animated&&M.getBoolean("animated",!0),l=o.animationBuilder||this.animation||M.get("navAnimation");return yield xt(Object.assign(Object.assign({mode:c,animated:a,enteringEl:e,leavingEl:t,baseEl:s,deepWait:oe(s),progressCallback:o.progressAnimation?p=>{p!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,p.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),p.progressEnd(0,0,0)):this.ani=p}:void 0},o),{animationBuilder:l})),this.ionNavDidChange.emit(),!0})}render(){return h("slot",{key:"e34e02b5154172c8d5cdd187b6ea58119b6946eb"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return hn}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function vn(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(e=>{switch(e){case"ion-router-outlet":customElements.get(e)||customElements.define(e,gn);break}})}var Wt=vn;var In=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",bn=In,yn=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",Cn=yn,Dn=H(class extends _{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=g(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(e){this.emitTap(e)}emitTap(e){this.stopPropagation&&(e.preventDefault(),e.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let e=P(this);return h(Z,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[e]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:bn,md:Cn}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function fe(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(e=>{switch(e){case"ion-backdrop":customElements.get(e)||customElements.define(e,Dn);break}})}var Ie=function(n){return n.Dark="DARK",n.Light="LIGHT",n.Default="DEFAULT",n}(Ie||{}),ot={getEngine(){let n=Ht();if(n?.isPluginAvailable("StatusBar"))return n.Plugins.StatusBar},setStyle(n){let e=this.getEngine();e&&e.setStyle(n)},getStyle:function(){return b(this,null,function*(){let n=this.getEngine();if(!n)return Ie.Default;let{style:e}=yield n.getInfo();return e})}},et=(n,e)=>{if(e===1)return 0;let t=1/(1-e),o=-(e*t);return n*t+o},Vt=()=>{!he||he.innerWidth>=768||ot.setStyle({style:Ie.Dark})},tt=(n=Ie.Default)=>{!he||he.innerWidth>=768||ot.setStyle({style:n})},Gt=(n,e)=>b(void 0,null,function*(){typeof n.canDismiss!="function"||!(yield n.canDismiss(void 0,ve))||(e.isRunning()?e.onFinish(()=>{n.dismiss(void 0,"handler")},{oneTimeCallback:!0}):n.dismiss(void 0,"handler"))}),nt=n=>.00255275*2.71828**(-14.9619*n)-1.00255*2.71828**(-.0380968*n)+1,Fe={MIN_PRESENTING_SCALE:.915},wn=(n,e,t,o)=>{let r=n.offsetHeight,s=!1,c=!1,a=null,l=null,p=.2,u=!0,v=0,m=()=>a&&me(a)?a.scrollY:!0,j=xe({el:n,gestureName:"modalSwipeToClose",gesturePriority:Rt,direction:"y",threshold:10,canStart:D=>{let S=D.event.target;return S===null||!S.closest?!0:(a=Ae(S),a?(me(a)?l=B(a).querySelector(".inner-scroll"):l=a,!!!a.querySelector("ion-refresher")&&l.scrollTop===0):S.closest("ion-footer")===null)},onStart:D=>{let{deltaY:S}=D;u=m(),c=n.canDismiss!==void 0&&n.canDismiss!==!0,S>0&&a&&Qe(a),e.progressStart(!0,s?1:0)},onMove:D=>{let{deltaY:S}=D;S>0&&a&&Qe(a);let F=D.deltaY/r,I=F>=0&&c,T=I?p:.9999,A=I?nt(F/T):F,x=ge(1e-4,A,T);e.progressStep(x),x>=.5&&v<.5?tt(t):x<.5&&v>=.5&&Vt(),v=x},onEnd:D=>{let S=D.velocityY,F=D.deltaY/r,I=F>=0&&c,T=I?p:.9999,A=I?nt(F/T):F,x=ge(1e-4,A,T),Y=(D.deltaY+S*1e3)/r,z=!I&&Y>=.5,N=z?-.001:.001;z?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),N+=ee([0,0],[.32,.72],[0,1],[1,1],x)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),N+=ee([0,0],[1,0],[.68,.28],[1,1],x)[0]);let G=Zt(z?F*r:(1-x)*r,S);s=z,j.enable(!1),a&&_t(a,u),e.onFinish(()=>{z||j.enable(!0)}).progressEnd(z?1:0,N,G),I&&x>T/4?Gt(n,e):z&&o()}});return j},Zt=(n,e)=>ge(400,n/Math.abs(e*1.1),500),Xt=n=>{let{currentBreakpoint:e,backdropBreakpoint:t,expandToScroll:o}=n,i=t===void 0||t<e,r=i?`calc(var(--backdrop-opacity) * ${e})`:"0",s=d("backdropAnimation").fromTo("opacity",0,r);i&&s.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let c=d("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),a=o?void 0:d("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:c,backdropAnimation:s,contentAnimation:a}},Kt=n=>{let{currentBreakpoint:e,backdropBreakpoint:t}=n,o=`calc(var(--backdrop-opacity) * ${et(e,t)})`,i=[{offset:0,opacity:o},{offset:1,opacity:0}],r=[{offset:0,opacity:o},{offset:t,opacity:0},{offset:1,opacity:0}],s=d("backdropAnimation").keyframes(t!==0?r:i);return{wrapperAnimation:d("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:s}},kn=()=>{let n=d().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=d().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:n,wrapperAnimation:e,contentAnimation:void 0}},Yt=(n,e)=>{let{presentingEl:t,currentBreakpoint:o,expandToScroll:i}=e,r=B(n),{wrapperAnimation:s,backdropAnimation:c,contentAnimation:a}=o!==void 0?Xt(e):kn();c.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!i&&a?.addElement(n.querySelector(".ion-page"));let l=d("entering-base").addElement(n).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([s]).beforeAddWrite(()=>{if(i)return;let p=n.querySelector("ion-footer"),u=n.shadowRoot.querySelector("ion-footer");if(p&&!u){let v=p.clientHeight,m=p.cloneNode(!0);n.shadowRoot.appendChild(m),p.style.setProperty("display","none"),p.setAttribute("aria-hidden","true"),n.querySelector(".ion-page").style.setProperty("padding-bottom",`${v}px`)}});if(a&&l.addAnimation(a),t){let p=window.innerWidth<768,u=t.tagName==="ION-MODAL"&&t.presentingElement!==void 0,v=B(t),m=d().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),C=document.body;if(p){let E=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",f=u?"-10px":E,y=Fe.MIN_PRESENTING_SCALE,j=`translateY(${f}) scale(${y})`;m.afterStyles({transform:j}).beforeAddWrite(()=>C.style.setProperty("background-color","black")).addElement(t).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:j,borderRadius:"10px 10px 0 0"}]),l.addAnimation(m)}else if(l.addAnimation(c),!u)s.fromTo("opacity","0","1");else{let f=`translateY(-10px) scale(${u?Fe.MIN_PRESENTING_SCALE:1})`;m.afterStyles({transform:f}).addElement(v.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:f}]);let y=d().afterStyles({transform:f}).addElement(v.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:f}]);l.addAnimation([m,y])}}else l.addAnimation(c);return l},xn=()=>{let n=d().fromTo("opacity","var(--backdrop-opacity)",0),e=d().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:n,wrapperAnimation:e}},qt=(n,e,t=500)=>{let{presentingEl:o,currentBreakpoint:i,expandToScroll:r}=e,s=B(n),{wrapperAnimation:c,backdropAnimation:a}=i!==void 0?Kt(e):xn();a.addElement(s.querySelector("ion-backdrop")),c.addElement(s.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let l=d("leaving-base").addElement(n).easing("cubic-bezier(0.32,0.72,0,1)").duration(t).addAnimation(c).beforeAddWrite(()=>{if(r)return;let p=n.querySelector("ion-footer");if(p){let u=n.shadowRoot.querySelector("ion-footer");p.style.removeProperty("display"),p.removeAttribute("aria-hidden"),u.style.setProperty("display","none"),u.setAttribute("aria-hidden","true"),n.querySelector(".ion-page").style.removeProperty("padding-bottom")}});if(o){let p=window.innerWidth<768,u=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,v=B(o),m=d().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(E=>{if(E!==1)return;o.style.setProperty("overflow",""),Array.from(C.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(y=>y.presentingElement!==void 0).length<=1&&C.style.setProperty("background-color","")}),C=document.body;if(p){let E=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",f=u?"-10px":E,y=Fe.MIN_PRESENTING_SCALE,j=`translateY(${f}) scale(${y})`;m.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:j,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),l.addAnimation(m)}else if(l.addAnimation(a),!u)c.fromTo("opacity","1","0");else{let f=`translateY(-10px) scale(${u?Fe.MIN_PRESENTING_SCALE:1})`;m.addElement(v.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:f},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let y=d().addElement(v.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:f},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);l.addAnimation([m,y])}}else l.addAnimation(a);return l},En=()=>{let n=d().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=d().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:n,wrapperAnimation:e,contentAnimation:void 0}},Sn=(n,e)=>{let{currentBreakpoint:t,expandToScroll:o}=e,i=B(n),{wrapperAnimation:r,backdropAnimation:s,contentAnimation:c}=t!==void 0?Xt(e):En();s.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),o&&c?.addElement(n.querySelector(".ion-page"));let a=d().addElement(n).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([s,r]).beforeAddWrite(()=>{if(o)return;let l=n.querySelector("ion-footer"),p=n.shadowRoot.querySelector("ion-footer");if(l&&!p){let u=l.clientHeight,v=l.cloneNode(!0);n.shadowRoot.appendChild(v),l.style.setProperty("display","none"),l.setAttribute("aria-hidden","true"),n.querySelector(".ion-page").style.setProperty("padding-bottom",`${u}px`)}});return c&&a.addAnimation(c),a},jn=()=>{let n=d().fromTo("opacity","var(--backdrop-opacity)",0),e=d().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:n,wrapperAnimation:e}},Tn=(n,e)=>{let{currentBreakpoint:t,expandToScroll:o}=e,i=B(n),{wrapperAnimation:r,backdropAnimation:s}=t!==void 0?Kt(e):jn();return s.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),d().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([s,r]).beforeAddWrite(()=>{if(o)return;let a=n.querySelector("ion-footer");if(a){let l=n.shadowRoot.querySelector("ion-footer");a.style.removeProperty("display"),a.removeAttribute("aria-hidden"),l.style.setProperty("display","none"),l.setAttribute("aria-hidden","true"),n.querySelector(".ion-page").style.removeProperty("padding-bottom")}})},An=(n,e,t,o,i,r,s=[],c,a,l,p)=>{let u=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],v=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-i,opacity:0},{offset:1,opacity:0}],m={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:i!==0?v:u,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},C=n.querySelector("ion-content"),E=t.clientHeight,f=o,y=0,j=!1,D=null,S=.95,F=s[s.length-1],I=s[0],T=r.childAnimations.find(k=>k.id==="wrapperAnimation"),A=r.childAnimations.find(k=>k.id==="backdropAnimation"),x=r.childAnimations.find(k=>k.id==="contentAnimation"),Y=()=>{n.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),n.classList.remove(ue)},z=()=>{n.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),n.classList.add(ue)},N=k=>{let w=n.querySelector("ion-footer");if(!w)return;let R=t.nextElementSibling,O=k==="original"?R:w,W=k==="original"?w:R;W.style.removeProperty("display"),W.removeAttribute("aria-hidden");let $=n.querySelector(".ion-page");if(k==="original")$.style.removeProperty("padding-bottom");else{let L=W.clientHeight;$.style.setProperty("padding-bottom",`${L}px`)}O.style.setProperty("display","none"),O.setAttribute("aria-hidden","true")};T&&A&&(T.keyframes([...m.WRAPPER_KEYFRAMES]),A.keyframes([...m.BACKDROP_KEYFRAMES]),x?.keyframes([...m.CONTENT_KEYFRAMES]),r.progressStart(!0,1-f),f>i?Y():z()),C&&f!==F&&c&&(C.scrollY=!1);let G=k=>{let w=Ae(k.event.target);if(f=a(),!c&&w)return(me(w)?B(w).querySelector(".inner-scroll"):w).scrollTop===0;if(f===1&&w){let R=me(w)?B(w).querySelector(".inner-scroll"):w;return!!!w.querySelector("ion-refresher")&&R.scrollTop===0}return!0},te=k=>{if(j=n.canDismiss!==void 0&&n.canDismiss!==!0&&I===0,!c){let w=Ae(k.event.target);D=w&&me(w)?B(w).querySelector(".inner-scroll"):w}c||N("original"),k.deltaY>0&&C&&(C.scrollY=!1),q(()=>{n.focus()}),r.progressStart(!0,1-f)},Be=k=>{if(!c&&k.deltaY<=0&&D)return;k.deltaY>0&&C&&(C.scrollY=!1);let w=1-f,R=s.length>1?1-s[1]:void 0,O=w+k.deltaY/E,W=R!==void 0&&O>=R&&j,$=W?S:.9999,L=W&&R!==void 0?R+nt((O-R)/($-R)):O;y=ge(1e-4,L,$),r.progressStep(y)},ze=k=>{if(!c&&k.deltaY<=0&&D&&D.scrollTop>0)return;let w=k.velocityY,R=(k.deltaY+w*350)/E,O=f-R,W=s.reduce(($,L)=>Math.abs(L-O)<Math.abs($-O)?L:$);be({breakpoint:W,breakpointOffset:y,canDismiss:j,animated:!0})},be=k=>{let{breakpoint:w,canDismiss:R,breakpointOffset:O,animated:W}=k,$=R&&w===0,L=$?f:w,ye=L!==0;return f=0,T&&A&&(T.keyframes([{offset:0,transform:`translateY(${O*100}%)`},{offset:1,transform:`translateY(${(1-L)*100}%)`}]),A.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${et(1-O,i)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${et(L,i)})`}]),x&&x.keyframes([{offset:0,maxHeight:`${(1-O)*100}%`},{offset:1,maxHeight:`${L*100}%`}]),r.progressStep(0)),ne.enable(!1),!c&&ye&&N("cloned"),$?Gt(n,r):ye||l(),C&&(L===s[s.length-1]||!c)&&(C.scrollY=!0),new Promise(Oe=>{r.onFinish(()=>{ye?T&&A?q(()=>{T.keyframes([...m.WRAPPER_KEYFRAMES]),A.keyframes([...m.BACKDROP_KEYFRAMES]),x?.keyframes([...m.CONTENT_KEYFRAMES]),r.progressStart(!0,1-L),f=L,p(f),f>i?Y():z(),ne.enable(!0),Oe()}):(ne.enable(!0),Oe()):Oe()},{oneTimeCallback:!0}).progressEnd(1,0,W?500:0)})},ne=xe({el:t,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:G,onStart:te,onMove:Be,onEnd:ze});return{gesture:ne,moveSheetToBreakpoint:be}},Fn=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer ion-toolbar:first-of-type{padding-top:6px}',Mn=Fn,Pn=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',Rn=Pn,Ut=H(class extends _{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=g(this,"ionModalDidPresent",7),this.willPresent=g(this,"ionModalWillPresent",7),this.willDismiss=g(this,"ionModalWillDismiss",7),this.didDismiss=g(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=g(this,"ionBreakpointDidChange",7),this.didPresentShorthand=g(this,"didPresent",7),this.willPresentShorthand=g(this,"willPresent",7),this.willDismissShorthand=g(this,"willDismiss",7),this.didDismissShorthand=g(this,"didDismiss",7),this.ionMount=g(this,"ionMount",7),this.lockController=Q(),this.triggerController=Se(),this.coreDelegate=Ee(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:t}=this;t!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,pe)},this.onLifecycle=e=>{let t=this.usersElement,o=Bn[e.type];if(t&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.presented=!1,this.hasController=!1,this.overlayIndex=void 0,this.delegate=void 0,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.breakpoints=void 0,this.expandToScroll=!0,this.initialBreakpoint=void 0,this.backdropBreakpoint=0,this.handle=void 0,this.handleBehavior="none",this.component=void 0,this.componentProps=void 0,this.cssClass=void 0,this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.presentingElement=void 0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:o}=this;e&&o.addClickListener(t,e)}breakpointsChanged(e){e!==void 0&&(this.sortedBreakpoints=e.sort((t,o)=>t-o))}connectedCallback(){let{el:e}=this;ae(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var e;let{breakpoints:t,initialBreakpoint:o,el:i,htmlAttributes:r}=this,s=this.isSheetModal=t!==void 0&&o!==void 0,c=["aria-label","role"];this.inheritedAttributes=Dt(i,c),r!==void 0&&c.forEach(a=>{r[a]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[a]:r[a]}),delete r[a])}),s&&(this.currentBreakpoint=this.initialBreakpoint),t!==void 0&&o!==void 0&&!t.includes(o)&&U("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((e=this.htmlAttributes)===null||e===void 0)&&e.id||ce(this.el)}componentDidLoad(){this.isOpen===!0&&q(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,o=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}checkCanDismiss(e,t){return b(this,null,function*(){let{canDismiss:o}=this;return typeof o=="function"?o(e,t):o})}present(){return b(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{presentingElement:t,el:o}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield re(r,o,this.component,["ion-page"],this.componentProps,i),oe(o)?yield ke(this.usersElement):this.keepContentsMounted||(yield we()),Xe(()=>this.el.classList.add("show-modal"));let s=t!==void 0;s&&P(this)==="ios"&&(this.statusBarStyle=yield ot.getStyle(),Vt()),yield le(this,"modalEnter",Yt,Sn,{presentingEl:t,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),q(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(Je,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():s&&this.initSwipeToClose(),e()})}initSwipeToClose(){var e;if(P(this)!=="ios")return;let{el:t}=this,o=this.leaveAnimation||M.get("modalLeave",qt),i=this.animation=o(t,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!$t(t)){Nt(t);return}let s=(e=this.statusBarStyle)!==null&&e!==void 0?e:Ie.Default;this.gesture=wn(t,i,s,()=>{this.gestureAnimationDismissing=!0,tt(this.statusBarStyle),this.animation.onFinish(()=>b(this,null,function*(){yield this.dismiss(void 0,ve),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:o}=this;if(!e||t===void 0)return;let i=this.enterAnimation||M.get("modalEnter",Yt),r=this.animation=i(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:o,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:s,moveSheetToBreakpoint:c}=An(this.el,this.backdropEl,e,t,o,r,this.sortedBreakpoints,this.expandToScroll,()=>{var a;return(a=this.currentBreakpoint)!==null&&a!==void 0?a:0},()=>this.sheetOnDismiss(),a=>{this.currentBreakpoint!==a&&(this.currentBreakpoint=a,this.ionBreakpointDidChange.emit({breakpoint:a}))});this.gesture=s,this.moveSheetToBreakpoint=c,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>b(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,ve),this.gestureAnimationDismissing=!1}))}dismiss(e,t){return b(this,null,function*(){var o;if(this.gestureAnimationDismissing&&t!==ve)return!1;let i=yield this.lockController.lock();if(t!=="handler"&&!(yield this.checkCanDismiss(e,t)))return i(),!1;let{presentingElement:r}=this;r!==void 0&&P(this)==="ios"&&tt(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(Je,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let c=yield de(this,e,t,"modalLeave",qt,Tn,{presentingEl:r,currentBreakpoint:(o=this.currentBreakpoint)!==null&&o!==void 0?o:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(c){let{delegate:a}=this.getDelegate();yield se(a,this.usersElement),Xe(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,i(),c})}onDidDismiss(){return K(this.el,"ionModalDidDismiss")}onWillDismiss(){return K(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){return b(this,null,function*(){if(!this.isSheetModal){U("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(e)){U(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:t,moveSheetToBreakpoint:o,canDismiss:i,breakpoints:r,animated:s}=this;t!==e&&o&&(this.sheetTransition=o({breakpoint:e,breakpointOffset:1-t,canDismiss:i!==void 0&&i!==!0&&r[0]===0,animated:s}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return b(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return b(this,null,function*(){let{breakpoints:e,currentBreakpoint:t}=this;if(!e||t==null)return!1;let o=e.filter(c=>c!==0),r=(o.indexOf(t)+1)%o.length,s=o[r];return yield this.setCurrentBreakpoint(s),!0})}render(){let{handle:e,isSheetModal:t,presentingElement:o,htmlAttributes:i,handleBehavior:r,inheritedAttributes:s,focusTrap:c,expandToScroll:a}=this,l=e!==!1&&t,p=P(this),u=o!==void 0&&p==="ios",v=r==="cycle";return h(Z,Object.assign({key:"0991b2e4e32da511e59fb1463b47e4ac1b86d1ca","no-router":!0,tabindex:"-1"},i,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[p]:!0,"modal-default":!u&&!t,"modal-card":u,"modal-sheet":t,"modal-no-expand-scroll":t&&!a,"overlay-hidden":!0,[ue]:c===!1},ie(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),h("ion-backdrop",{key:"ca9453ffe1021fb252ad9460676cfabb5633f00f",ref:m=>this.backdropEl=m,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),p==="ios"&&h("div",{key:"9f8da446a7b0f3b26aec856e13f6d6d131a7e37b",class:"modal-shadow"}),h("div",Object.assign({key:"9d08bf600571849c97b58f66df40b496a358d1e1",role:"dialog"},s,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:m=>this.wrapperEl=m}),l&&h("button",{key:"f8bf0d1126e5376519101225d9965727121ee042",class:"modal-handle",tabIndex:v?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:v?this.onHandleClick:void 0,part:"handle"}),h("slot",{key:"6d52849df98f2c6c8fbc03996a931ea6a39a512b"})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:Mn,md:Rn}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16],leaveAnimation:[16],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16],htmlAttributes:[16],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),Bn={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function Qt(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(e=>{switch(e){case"ion-modal":customElements.get(e)||customElements.define(e,Ut);break;case"ion-backdrop":customElements.get(e)||fe();break}})}var Jt=Qt;var zn=n=>{if(!n)return{arrowWidth:0,arrowHeight:0};let{width:e,height:t}=n.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}},tn=(n,e,t)=>{let o=e.getBoundingClientRect(),i=o.height,r=o.width;return n==="cover"&&t&&(r=t.getBoundingClientRect().width),{contentWidth:r,contentHeight:i}},On=(n,e,t,o)=>{let i=[],s=B(o).querySelector(".popover-content");switch(e){case"hover":i=[{eventName:"mouseenter",callback:c=>{document.elementFromPoint(c.clientX,c.clientY)!==n&&t.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:c=>{if(c.target.closest("[data-ion-popover-trigger]")===n){c.stopPropagation();return}t.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:c,callback:a})=>s.addEventListener(c,a)),()=>{i.forEach(({eventName:c,callback:a})=>s.removeEventListener(c,a))}},Ln=(n,e,t)=>{let o=[];switch(e){case"hover":let i;o=[{eventName:"mouseenter",callback:r=>b(void 0,null,function*(){r.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{q(()=>{t.presentFromTrigger(r),i=void 0})},100)})},{eventName:"mouseleave",callback:r=>{i&&clearTimeout(i);let s=r.relatedTarget;s&&s.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"context-menu":o=[{eventName:"contextmenu",callback:r=>{r.preventDefault(),t.presentFromTrigger(r)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"click":default:o=[{eventName:"click",callback:r=>t.presentFromTrigger(r)},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break}return o.forEach(({eventName:i,callback:r})=>n.addEventListener(i,r)),n.setAttribute("data-ion-popover-trigger","true"),()=>{o.forEach(({eventName:i,callback:r})=>n.removeEventListener(i,r)),n.removeAttribute("data-ion-popover-trigger")}},nn=(n,e)=>!e||e.tagName!=="ION-ITEM"?-1:n.findIndex(t=>t===e),$n=(n,e)=>{let t=nn(n,e);return n[t+1]},Nn=(n,e)=>{let t=nn(n,e);return n[t-1]},Me=n=>{let t=B(n).querySelector("button");t&&q(()=>t.focus())},_n=n=>n.hasAttribute("data-ion-popover-trigger"),Hn=n=>{let e=t=>b(void 0,null,function*(){var o;let i=document.activeElement,r=[],s=(o=t.target)===null||o===void 0?void 0:o.tagName;if(!(s!=="ION-POPOVER"&&s!=="ION-ITEM")){try{r=Array.from(n.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(t.key){case"ArrowLeft":(yield n.getParentPopover())&&n.dismiss(void 0,void 0,!1);break;case"ArrowDown":t.preventDefault();let a=$n(r,i);a!==void 0&&Me(a);break;case"ArrowUp":t.preventDefault();let l=Nn(r,i);l!==void 0&&Me(l);break;case"Home":t.preventDefault();let p=r[0];p!==void 0&&Me(p);break;case"End":t.preventDefault();let u=r[r.length-1];u!==void 0&&Me(u);break;case"ArrowRight":case" ":case"Enter":if(i&&_n(i)){let v=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(v)}break}}});return n.addEventListener("keydown",e),()=>n.removeEventListener("keydown",e)},on=(n,e,t,o,i,r,s,c,a,l,p)=>{var u;let v={top:0,left:0,width:0,height:0};switch(r){case"event":if(!p)return a;let F=p;v={top:F.clientY,left:F.clientX,width:1,height:1};break;case"trigger":default:let I=p,T=l||((u=I?.detail)===null||u===void 0?void 0:u.ionShadowTarget)||I?.target;if(!T)return a;let A=T.getBoundingClientRect();v={top:A.top,left:A.left,width:A.width,height:A.height};break}let m=Yn(s,v,e,t,o,i,n),C=qn(c,s,v,e,t),E=m.top+C.top,f=m.left+C.left,{arrowTop:y,arrowLeft:j}=Zn(s,o,i,E,f,e,t,n),{originX:D,originY:S}=Wn(s,c,n);return{top:E,left:f,referenceCoordinates:v,arrowTop:y,arrowLeft:j,originX:D,originY:S}},Wn=(n,e,t)=>{switch(n){case"top":return{originX:en(e),originY:"bottom"};case"bottom":return{originX:en(e),originY:"top"};case"left":return{originX:"right",originY:Pe(e)};case"right":return{originX:"left",originY:Pe(e)};case"start":return{originX:t?"left":"right",originY:Pe(e)};case"end":return{originX:t?"right":"left",originY:Pe(e)}}},en=n=>{switch(n){case"start":return"left";case"center":return"center";case"end":return"right"}},Pe=n=>{switch(n){case"start":return"top";case"center":return"center";case"end":return"bottom"}},Zn=(n,e,t,o,i,r,s,c)=>{let a={arrowTop:o+s/2-e/2,arrowLeft:i+r-e/2},l={arrowTop:o+s/2-e/2,arrowLeft:i-e*1.5};switch(n){case"top":return{arrowTop:o+s,arrowLeft:i+r/2-e/2};case"bottom":return{arrowTop:o-t,arrowLeft:i+r/2-e/2};case"left":return a;case"right":return l;case"start":return c?l:a;case"end":return c?a:l;default:return{arrowTop:0,arrowLeft:0}}},Yn=(n,e,t,o,i,r,s)=>{let c={top:e.top,left:e.left-t-i},a={top:e.top,left:e.left+e.width+i};switch(n){case"top":return{top:e.top-o-r,left:e.left};case"right":return a;case"bottom":return{top:e.top+e.height+r,left:e.left};case"left":return c;case"start":return s?a:c;case"end":return s?c:a}},qn=(n,e,t,o,i)=>{switch(n){case"center":return Gn(e,t,o,i);case"end":return Vn(e,t,o,i);case"start":default:return{top:0,left:0}}},Vn=(n,e,t,o)=>{switch(n){case"start":case"end":case"left":case"right":return{top:-(o-e.height),left:0};case"top":case"bottom":default:return{top:0,left:-(t-e.width)}}},Gn=(n,e,t,o)=>{switch(n){case"start":case"end":case"left":case"right":return{top:-(o/2-e.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(t/2-e.width/2)}}},rn=(n,e,t,o,i,r,s,c,a,l,p,u,v=0,m=0,C=0)=>{let E=v,f=m,y=t,j=e,D,S=l,F=p,I=!1,T=!1,A=u?u.top+u.height:r/2-c/2,x=u?u.height:0,Y=!1;return y<o+a?(y=o,I=!0,S="left"):s+o+y+a>i&&(T=!0,y=i-s-o,S="right"),A+x+c>r&&(n==="top"||n==="bottom")&&(A-c>0?(j=Math.max(12,A-c-x-(C-1)),E=j+c,F="bottom",Y=!0):D=o),{top:j,left:y,bottom:D,originX:S,originY:F,checkSafeAreaLeft:I,checkSafeAreaRight:T,arrowTop:E,arrowLeft:f,addPopoverBottomClass:Y}},Xn=(n,e=!1,t,o)=>!(!t&&!o||n!=="top"&&n!=="bottom"&&e),Kn=5,Un=(n,e)=>{var t;let{event:o,size:i,trigger:r,reference:s,side:c,align:a}=e,l=n.ownerDocument,p=l.dir==="rtl",u=l.defaultView.innerWidth,v=l.defaultView.innerHeight,m=B(n),C=m.querySelector(".popover-content"),E=m.querySelector(".popover-arrow"),f=r||((t=o?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||o?.target,{contentWidth:y,contentHeight:j}=tn(i,C,f),{arrowWidth:D,arrowHeight:S}=zn(E),F={top:v/2-j/2,left:u/2-y/2,originX:p?"right":"left",originY:"top"},I=on(p,y,j,D,S,s,c,a,F,r,o),T=i==="cover"?0:Kn,A=i==="cover"?0:25,{originX:x,originY:Y,top:z,left:N,bottom:G,checkSafeAreaLeft:te,checkSafeAreaRight:Be,arrowTop:ze,arrowLeft:be,addPopoverBottomClass:ne}=rn(c,I.top,I.left,T,u,v,y,j,A,I.originX,I.originY,I.referenceCoordinates,I.arrowTop,I.arrowLeft,S),k=d(),w=d(),R=d();return w.addElement(m.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),R.addElement(m.querySelector(".popover-arrow")).addElement(m.querySelector(".popover-content")).fromTo("opacity",.01,1),k.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&n.style.setProperty("--width",`${y}px`),ne&&n.classList.add("popover-bottom"),G!==void 0&&C.style.setProperty("bottom",`${G}px`);let O=" + var(--ion-safe-area-left, 0)",W=" - var(--ion-safe-area-right, 0)",$=`${N}px`;if(te&&($=`${N}px${O}`),Be&&($=`${N}px${W}`),C.style.setProperty("top",`calc(${z}px + var(--offset-y, 0))`),C.style.setProperty("left",`calc(${$} + var(--offset-x, 0))`),C.style.setProperty("transform-origin",`${Y} ${x}`),E!==null){let L=I.top!==z||I.left!==N;Xn(c,L,o,r)?(E.style.setProperty("top",`calc(${ze}px + var(--offset-y, 0))`),E.style.setProperty("left",`calc(${be}px + var(--offset-x, 0))`)):E.style.setProperty("display","none")}}).addAnimation([w,R])},Qn=n=>{let e=B(n),t=e.querySelector(".popover-content"),o=e.querySelector(".popover-arrow"),i=d(),r=d(),s=d();return r.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{n.style.removeProperty("--width"),n.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),o&&(o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("display"))}).duration(300).addAnimation([r,s])},Jn=12,eo=(n,e)=>{var t;let{event:o,size:i,trigger:r,reference:s,side:c,align:a}=e,l=n.ownerDocument,p=l.dir==="rtl",u=l.defaultView.innerWidth,v=l.defaultView.innerHeight,m=B(n),C=m.querySelector(".popover-content"),E=r||((t=o?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||o?.target,{contentWidth:f,contentHeight:y}=tn(i,C,E),j={top:v/2-y/2,left:u/2-f/2,originX:p?"right":"left",originY:"top"},D=on(p,f,y,0,0,s,c,a,j,r,o),S=i==="cover"?0:Jn,{originX:F,originY:I,top:T,left:A,bottom:x}=rn(c,D.top,D.left,S,u,v,f,y,0,D.originX,D.originY,D.referenceCoordinates),Y=d(),z=d(),N=d(),G=d(),te=d();return z.addElement(m.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),N.addElement(m.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),G.addElement(C).beforeStyles({top:`calc(${T}px + var(--offset-y, 0px))`,left:`calc(${A}px + var(--offset-x, 0px))`,"transform-origin":`${I} ${F}`}).beforeAddWrite(()=>{x!==void 0&&C.style.setProperty("bottom",`${x}px`)}).fromTo("transform","scale(0.8)","scale(1)"),te.addElement(m.querySelector(".popover-viewport")).fromTo("opacity",.01,1),Y.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&n.style.setProperty("--width",`${f}px`),I==="bottom"&&n.classList.add("popover-bottom")}).addAnimation([z,N,G,te])},to=n=>{let e=B(n),t=e.querySelector(".popover-content"),o=d(),i=d(),r=d();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),r.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),o.easing("ease").afterAddWrite(()=>{n.style.removeProperty("--width"),n.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,r])},no=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',oo=no,io=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",ro=io,sn=H(class extends _{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=g(this,"ionPopoverDidPresent",7),this.willPresent=g(this,"ionPopoverWillPresent",7),this.willDismiss=g(this,"ionPopoverWillDismiss",7),this.didDismiss=g(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=g(this,"didPresent",7),this.willPresentShorthand=g(this,"willPresent",7),this.willDismissShorthand=g(this,"willDismiss",7),this.didDismissShorthand=g(this,"didDismiss",7),this.ionMount=g(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=Ee(),this.lockController=Q(),this.inline=!1,this.focusDescendantOnPresent=!1,this.onBackdropTap=()=>{this.dismiss(void 0,pe)},this.onLifecycle=e=>{let t=this.usersElement,o=so[e.type];if(t&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:e,triggerAction:t,el:o,destroyTriggerInteraction:i}=this;if(i&&i(),e===void 0)return;let r=this.triggerEl=e!==void 0?document.getElementById(e):null;if(!r){U(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=Ln(r,t,o)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=Hn(t)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:e,parentPopover:t,triggerAction:o,triggerEl:i,el:r}=this;!t||!i||(e&&e(),this.destroyDismissInteraction=On(i,o,r,t))},this.presented=!1,this.hasController=!1,this.delegate=void 0,this.overlayIndex=void 0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.component=void 0,this.componentProps=void 0,this.keyboardClose=!0,this.cssClass=void 0,this.backdropDismiss=!0,this.event=void 0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.triggerAction="click",this.trigger=void 0,this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.alignment=void 0,this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:e,el:t}=this;ae(t),e()}disconnectedCallback(){let{destroyTriggerInteraction:e}=this;e&&e()}componentWillLoad(){var e,t;let{el:o}=this,i=(t=(e=this.htmlAttributes)===null||e===void 0?void 0:e.id)!==null&&t!==void 0?t:ce(o);this.parentPopover=o.closest(`ion-popover:not(#${i})`),this.alignment===void 0&&(this.alignment=P(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:e,isOpen:t}=this;t===!0&&q(()=>this.present()),e&&wt(e,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(e,t=!1){return b(this,null,function*(){this.focusDescendantOnPresent=t,yield this.present(e),this.focusDescendantOnPresent=!1})}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,o=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}present(e){return b(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{el:o}=this,{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield re(r,o,this.component,["popover-viewport"],this.componentProps,i),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),oe(o)?yield ke(this.usersElement):this.keepContentsMounted||(yield we()),yield le(this,"popoverEnter",Un,eo,{event:e||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&At(o),t()})}dismiss(e,t,o=!0){return b(this,null,function*(){let i=yield this.lockController.lock(),{destroyKeyboardInteraction:r,destroyDismissInteraction:s}=this;o&&this.parentPopover&&this.parentPopover.dismiss(e,t,o);let c=yield de(this,e,t,"popoverLeave",Qn,to,this.event);if(c){r&&(r(),this.destroyKeyboardInteraction=void 0),s&&(s(),this.destroyDismissInteraction=void 0);let{delegate:a}=this.getDelegate();yield se(a,this.usersElement)}return i(),c})}getParentPopover(){return b(this,null,function*(){return this.parentPopover})}onDidDismiss(){return K(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return K(this.el,"ionPopoverWillDismiss")}render(){let e=P(this),{onLifecycle:t,parentPopover:o,dismissOnSelect:i,side:r,arrow:s,htmlAttributes:c,focusTrap:a}=this,l=X("desktop"),p=s&&!o;return h(Z,Object.assign({key:"ff24e8d9677711248a36994cce568e74ba151499","aria-modal":"true","no-router":!0,tabindex:"-1"},c,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},ie(this.cssClass)),{[e]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":l,[`popover-side-${r}`]:!0,[ue]:a===!1,"popover-nested":!!o}),onIonPopoverDidPresent:t,onIonPopoverWillPresent:t,onIonPopoverWillDismiss:t,onIonPopoverDidDismiss:t,onIonBackdropTap:this.onBackdropTap}),!o&&h("ion-backdrop",{key:"aca68b4002a08b0e563a976a867141162c20f8b4",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),h("div",{key:"62d21d1eab5c6d675d49932559ffb161747e5fec",class:"popover-wrapper ion-overlay-wrapper",onClick:i?()=>this.dismiss():void 0},p&&h("div",{key:"1b46cc77d5302637fc979353483bb5fd780fd1d3",class:"popover-arrow",part:"arrow"}),h("div",{key:"a5657bff26e46d1959b71eb0992e7dc8fcae86f1",class:"popover-content",part:"content"},h("slot",{key:"e1a98007226a46b51109e7004c4d338ca1bc0f9e"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:oo,md:ro}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16],leaveAnimation:[16],component:[1],componentProps:[16],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),so={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function an(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(e=>{switch(e){case"ion-popover":customElements.get(e)||customElements.define(e,sn);break;case"ion-backdrop":customElements.get(e)||fe();break}})}var cn=an;var ao="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",co=ao,lo=H(class extends _{constructor(){super(),this.__registerHost()}componentDidLoad(){yt.isBrowser&&uo(()=>b(this,null,function*(){let e=X(window,"hybrid");if(M.getBoolean("_testing")||import("./chunk-7QBWVQV5.js").then(i=>i.startTapClick(M)),M.getBoolean("statusTap",e)&&import("./chunk-LJ5LGSIK.js").then(i=>i.startStatusTap()),M.getBoolean("inputShims",po())){let i=X(window,"ios")?"ios":"android";import("./chunk-XUBDSMFY.js").then(r=>r.startInputShims(M,i))}let t=yield import("./chunk-TLBWJ3YQ.js"),o=e||Ke();M.getBoolean("hardwareBackButton",o)?t.startHardwareBackButton():(Ke()&&U("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),t.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-DRCUPUCR.js").then(i=>i.startKeyboardAssist(window)),import("./chunk-QIZXFP6Y.js").then(i=>this.focusVisible=i.startFocusVisible())}))}setFocus(e){return b(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(e)})}render(){let e=P(this);return h(Z,{key:"03aa892f986330078d112b1e8b010df98fa7e39e",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":M.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return co}},[0,"ion-app",{setFocus:[64]}]),po=()=>!!(X(window,"ios")&&X(window,"mobile")||X(window,"android")&&X(window,"mobileweb")),uo=n=>{"requestIdleCallback"in window?window.requestIdleCallback(n):setTimeout(n,32)};function mo(){if(typeof customElements>"u")return;["ion-app"].forEach(e=>{switch(e){case"ion-app":customElements.get(e)||customElements.define(e,lo);break}})}var ln=mo;var fo={bubbles:{dur:1e3,circles:9,fn:(n,e,t)=>{let o=`${n*e/t-n}ms`,i=2*Math.PI*e/t;return{r:5,style:{top:`${32*Math.sin(i)}%`,left:`${32*Math.cos(i)}%`,"animation-delay":o}}}},circles:{dur:1e3,circles:8,fn:(n,e,t)=>{let o=e/t,i=`${n*o-n}ms`,r=2*Math.PI*o;return{r:5,style:{top:`${32*Math.sin(r)}%`,left:`${32*Math.cos(r)}%`,"animation-delay":i}}}},circular:{dur:1400,elmDuration:!0,circles:1,fn:()=>({r:20,cx:48,cy:48,fill:"none",viewBox:"24 24 48 48",transform:"translate(0,0)",style:{}})},crescent:{dur:750,circles:1,fn:()=>({r:26,style:{}})},dots:{dur:750,circles:3,fn:(n,e)=>{let t=-(110*e)+"ms";return{r:6,style:{left:`${32-32*e}%`,"animation-delay":t}}}},lines:{dur:1e3,lines:8,fn:(n,e,t)=>{let o=`rotate(${360/t*e+(e<t/2?180:-180)}deg)`,i=`${n*e/t-n}ms`;return{y1:14,y2:26,style:{transform:o,"animation-delay":i}}}},"lines-small":{dur:1e3,lines:8,fn:(n,e,t)=>{let o=`rotate(${360/t*e+(e<t/2?180:-180)}deg)`,i=`${n*e/t-n}ms`;return{y1:12,y2:20,style:{transform:o,"animation-delay":i}}}},"lines-sharp":{dur:1e3,lines:12,fn:(n,e,t)=>{let o=`rotate(${30*e+(e<6?180:-180)}deg)`,i=`${n*e/t-n}ms`;return{y1:17,y2:29,style:{transform:o,"animation-delay":i}}}},"lines-sharp-small":{dur:1e3,lines:12,fn:(n,e,t)=>{let o=`rotate(${30*e+(e<6?180:-180)}deg)`,i=`${n*e/t-n}ms`;return{y1:12,y2:20,style:{transform:o,"animation-delay":i}}}}},dn=fo,ho=":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}",go=ho,vo=H(class extends _{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.color=void 0,this.duration=void 0,this.name=void 0,this.paused=!1}getName(){let e=this.name||M.get("spinner"),t=P(this);return e||(t==="ios"?"lines":"circular")}render(){var e;let t=this,o=P(t),i=t.getName(),r=(e=dn[i])!==null&&e!==void 0?e:dn.lines,s=typeof t.duration=="number"&&t.duration>10?t.duration:r.dur,c=[];if(r.circles!==void 0)for(let a=0;a<r.circles;a++)c.push(Io(r,s,a,r.circles));else if(r.lines!==void 0)for(let a=0;a<r.lines;a++)c.push(bo(r,s,a,r.lines));return h(Z,{key:"e0dfa8a3ee2a0469eb31323f506750bd77d65797",class:Tt(t.color,{[o]:!0,[`spinner-${i}`]:!0,"spinner-paused":t.paused||M.getBoolean("_testing")}),role:"progressbar",style:r.elmDuration?{animationDuration:s+"ms"}:{}},c)}static get style(){return go}},[1,"ion-spinner",{color:[513],duration:[2],name:[1],paused:[4]}]),Io=(n,e,t,o)=>{let i=n.fn(e,t,o);return i.style["animation-duration"]=e+"ms",h("svg",{viewBox:i.viewBox||"0 0 64 64",style:i.style},h("circle",{transform:i.transform||"translate(32,32)",cx:i.cx,cy:i.cy,r:i.r,style:n.elmDuration?{animationDuration:e+"ms"}:{}}))},bo=(n,e,t,o)=>{let i=n.fn(e,t,o);return i.style["animation-duration"]=e+"ms",h("svg",{viewBox:i.viewBox||"0 0 64 64",style:i.style},h("line",{transform:"translate(32,32)",y1:i.y1,y2:i.y2}))};function pn(){if(typeof customElements>"u")return;["ion-spinner"].forEach(e=>{switch(e){case"ion-spinner":customElements.get(e)||customElements.define(e,vo);break}})}var yo=n=>{let e=d(),t=d(),o=d();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),o.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,o])},Co=n=>{let e=d(),t=d(),o=d();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),o.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,o])},Do=n=>{let e=d(),t=d(),o=d();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),o.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,o])},wo=n=>{let e=d(),t=d(),o=d();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),o.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,o])},ko=".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",xo=ko,Eo=".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}",So=Eo,jo=H(class extends _{constructor(){super(),this.__registerHost(),this.didPresent=g(this,"ionLoadingDidPresent",7),this.willPresent=g(this,"ionLoadingWillPresent",7),this.willDismiss=g(this,"ionLoadingWillDismiss",7),this.didDismiss=g(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=g(this,"didPresent",7),this.willPresentShorthand=g(this,"willPresent",7),this.willDismissShorthand=g(this,"willDismiss",7),this.didDismissShorthand=g(this,"didDismiss",7),this.delegateController=Bt(this),this.lockController=Q(),this.triggerController=Se(),this.customHTMLEnabled=M.get("innerHTMLTemplatesEnabled",jt),this.presented=!1,this.onBackdropTap=()=>{this.dismiss(void 0,pe)},this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.message=void 0,this.cssClass=void 0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.spinner=void 0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:o}=this;e&&o.addClickListener(t,e)}connectedCallback(){ae(this.el),this.triggerChanged()}componentWillLoad(){var e;if(this.spinner===void 0){let t=P(this);this.spinner=M.get("loadingSpinner",M.get("spinner",t==="ios"?"lines":"crescent"))}!((e=this.htmlAttributes)===null||e===void 0)&&e.id||ce(this.el)}componentDidLoad(){this.isOpen===!0&&q(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){return b(this,null,function*(){let e=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield le(this,"loadingEnter",yo,Do),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration+10)),e()})}dismiss(e,t){return b(this,null,function*(){let o=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let i=yield de(this,e,t,"loadingLeave",Co,wo);return i&&this.delegateController.removeViewFromDom(),o(),i})}onDidDismiss(){return K(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return K(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(e){let{customHTMLEnabled:t,message:o}=this;return t?h("div",{class:"loading-content",id:e,innerHTML:St(o)}):h("div",{class:"loading-content",id:e},o)}render(){let{message:e,spinner:t,htmlAttributes:o,overlayIndex:i}=this,r=P(this),s=`loading-${i}-msg`;return h(Z,Object.assign({key:"d6066c8b56b1fe4b597a243a7dab191ef0d21286",role:"dialog","aria-modal":"true","aria-labelledby":e!==void 0?s:null,tabindex:"-1"},o,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},ie(this.cssClass)),{[r]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),h("ion-backdrop",{key:"2431eda00a2a3f510f5dfc39b7c7d47c056dfa3d",visible:this.showBackdrop,tappable:this.backdropDismiss}),h("div",{key:"cf210aaf5e754e4eccdb49cf7ead4647b3f9b2d1",tabindex:"0","aria-hidden":"true"}),h("div",{key:"fa9375143d391656d70e181d25b952c77c2fc6ec",class:"loading-wrapper ion-overlay-wrapper"},t&&h("div",{key:"8e4a4ed994f7f62df86b03696ac95162df41f52d",class:"loading-spinner"},h("ion-spinner",{key:"e5b323c272d365853ba92bd211e390b4fd4751d2",name:t,"aria-hidden":"true"})),e!==void 0&&this.renderLoadingMessage(s)),h("div",{key:"cae35ec8c34800477bff3ebcec8010e632158233",tabindex:"0","aria-hidden":"true"}))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:xo,md:So}}},[34,"ion-loading",{overlayIndex:[2,"overlay-index"],delegate:[16],hasController:[4,"has-controller"],keyboardClose:[4,"keyboard-close"],enterAnimation:[16],leaveAnimation:[16],message:[1],cssClass:[1,"css-class"],duration:[2],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],spinner:[1025],translucent:[4],animated:[4],htmlAttributes:[16],isOpen:[4,"is-open"],trigger:[1],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]);function To(){if(typeof customElements>"u")return;["ion-loading","ion-backdrop","ion-spinner"].forEach(e=>{switch(e){case"ion-loading":customElements.get(e)||customElements.define(e,jo);break;case"ion-backdrop":customElements.get(e)||fe();break;case"ion-spinner":customElements.get(e)||pn();break}})}var un=To;var Fo=["outletContent"],mn=["*"];var Ms=(()=>{let n=class Re extends Ot{parentOutlet;outletContent;constructor(t,o,i,r,s,c,a,l){super(t,o,i,r,s,c,a,l),this.parentOutlet=l}static \u0275fac=function(o){return new(o||Re)(We("name"),We("tabs"),V(vt),V(Ye),V(bt),V(Ze),V(It),V(Re,12))};static \u0275cmp=He({type:Re,selectors:[["ion-router-outlet"]],viewQuery:function(o,i){if(o&1&&pt(Fo,7,at),o&2){let r;ut(r=mt())&&(i.outletContent=r.first)}},standalone:!0,features:[ct,Ge],ngContentSelectors:mn,decls:3,vars:0,consts:[["outletContent",""]],template:function(o,i){o&1&&(qe(),lt(0,null,0),Ve(2),dt())},encapsulation:2})};return n=Ne([zt({defineCustomElementFn:Wt})],n),n})();var Mo=(n,e)=>{let t=n.prototype;e.forEach(o=>{Object.defineProperty(t,o,{get(){return this.el[o]},set(i){this.z.runOutsideAngular(()=>this.el[o]=i)},configurable:!0})})},Po=(n,e)=>{let t=n.prototype;e.forEach(o=>{t[o]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[o].apply(this.el,i))}})};function Ro(n){return function(t){let{defineCustomElementFn:o,inputs:i,methods:r}=n;return o!==void 0&&o(),i&&Mo(t,i),r&&Po(t,r),t}}var Ps=(()=>{let n=class it{z;el;constructor(t,o,i){this.z=i,t.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||it)(V(ht),V(Ye),V(Ze))};static \u0275cmp=He({type:it,selectors:[["ion-app"]],standalone:!0,features:[Ge],ngContentSelectors:mn,decls:1,vars:0,template:function(o,i){o&1&&(qe(),Ve(0))},encapsulation:2,changeDetection:0})};return n=Ne([Ro({defineCustomElementFn:ln,methods:["setFocus"]})],n),n})();var Bo=(()=>{class n extends Te{angularDelegate=J(je);injector=J(De);environmentInjector=J(Ce);constructor(){super(Mt),Jt()}create(t){return super.create($e(Le({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(o){return new(o||n)};static \u0275prov=_e({token:n,factory:n.\u0275fac})}return n})(),rt=class extends Te{angularDelegate=J(je);injector=J(De);environmentInjector=J(Ce);constructor(){super(Pt),cn()}create(e){return super.create($e(Le({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},Rs=(n={})=>st([{provide:Ue,useValue:n},{provide:ft,useFactory:zo,multi:!0,deps:[Ue,gt]},Lt(),je,Bo,rt]),zo=(n,e)=>()=>{e.documentElement.classList.add("ion-ce"),Et(n)};var Bs=(()=>{class n extends Te{constructor(){super(Ft),un()}static \u0275fac=function(o){return new(o||n)};static \u0275prov=_e({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();export{Ms as a,Ps as b,Rs as c,Bs as d};
