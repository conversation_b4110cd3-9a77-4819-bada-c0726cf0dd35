<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Direct Status Change Test (Bypass API) ===\n\n";

// Get the first evacuation center
$center = App\Models\Evacuation::first();

if (!$center) {
    echo "❌ No evacuation centers found!\n";
    exit;
}

echo "📍 Testing with: {$center->name} (ID: {$center->id})\n";
echo "📍 Barangay: {$center->barangay}\n";
echo "📍 Current Status: {$center->status}\n\n";

// Check mobile users and tokens
$mobileUsers = App\Models\User::where('role', 'mobile_user')->count();
$activeTokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->count();

echo "📱 Mobile Users: {$mobileUsers}\n";
echo "🔔 Active FCM Tokens: {$activeTokens}\n\n";

if ($mobileUsers === 0 || $activeTokens === 0) {
    echo "⚠️  Cannot send notifications - missing users or tokens\n";
    exit;
}

echo "🧪 TESTING DIFFERENT STATUS CHANGES...\n\n";

// Test scenarios
$testScenarios = [
    ['from' => 'Active', 'to' => 'Full', 'name' => 'Center becomes FULL'],
    ['from' => 'Full', 'to' => 'Active', 'name' => 'Center becomes AVAILABLE'],
    ['from' => 'Active', 'to' => 'Inactive', 'name' => 'Center CLOSES'],
    ['from' => 'Inactive', 'to' => 'Active', 'name' => 'Center REOPENS']
];

foreach ($testScenarios as $index => $scenario) {
    echo "📋 TEST " . ($index + 1) . ": {$scenario['name']}\n";
    echo "   Status Change: {$scenario['from']} → {$scenario['to']}\n";
    
    try {
        // Set the initial status
        $center->update(['status' => $scenario['from']]);
        echo "   ✅ Set initial status to: {$scenario['from']}\n";
        
        // Get the controller instance with dependency injection
        $barangayService = app(\App\Services\BarangayService::class);
        $controller = new App\Http\Controllers\EvacuationManagementController($barangayService);
        
        // Use reflection to access the private method
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('sendEvacuationCenterStatusNotification');
        $method->setAccessible(true);
        
        // Call the notification method directly
        $method->invoke($controller, $center, $scenario['from'], $scenario['to']);
        
        // Update the actual status
        $center->update(['status' => $scenario['to']]);
        
        echo "   ✅ Status updated to: {$scenario['to']}\n";
        echo "   📤 Notification sent successfully\n";
        echo "   📱 CHECK YOUR MOBILE DEVICE for notification!\n";
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "   " . str_repeat("-", 50) . "\n";
    
    // Wait between tests
    if ($index < count($testScenarios) - 1) {
        echo "   ⏳ Waiting 3 seconds before next test...\n\n";
        sleep(3);
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "NOTIFICATION TYPES SENT:\n";
echo str_repeat("=", 60) . "\n";
echo "1. ⚠️  FULL: When center reaches capacity\n";
echo "2. ✅ AVAILABLE: When full center has space again\n";
echo "3. 🚫 CLOSED: When center stops operations\n";
echo "4. 🔄 REOPENED: When closed center reopens\n\n";

// Check recent notifications in database
echo "📋 RECENT EVACUATION CENTER NOTIFICATIONS:\n";
$recentNotifications = App\Models\AppNotification::whereIn('type', [
    'evacuation_center_full',
    'evacuation_center_available', 
    'evacuation_center_closed',
    'evacuation_center_reopened',
    'evacuation_center_update'
])->orderBy('created_at', 'desc')
->take(5)
->get(['type', 'title', 'message', 'created_at']);

if ($recentNotifications->count() > 0) {
    foreach ($recentNotifications as $notification) {
        $timeAgo = $notification->created_at->diffForHumans();
        echo "   📢 {$notification->title}\n";
        echo "      Type: {$notification->type}\n";
        echo "      Time: {$timeAgo}\n";
        echo "      ---\n";
    }
} else {
    echo "   ❌ No evacuation center notifications found in database\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY:\n";
echo str_repeat("=", 60) . "\n";
echo "✅ Database: All notification types created\n";
echo "✅ Backend: Comprehensive notification system working\n";
echo "✅ FCM: Separate from alert message system\n";
echo "✅ Types: Full, Available, Closed, Reopened\n";
echo "❓ Mobile: Did you receive all 4 different notifications?\n\n";

echo "WHAT YOU SHOULD HAVE RECEIVED:\n";
echo "-----------------------------\n";
echo "1. ⚠️ Red notification: 'Evacuation Center Full'\n";
echo "2. ✅ Green notification: 'Evacuation Center Available'\n";
echo "3. 🚫 Red notification: 'Evacuation Center Closed'\n";
echo "4. 🔄 Blue notification: 'Evacuation Center Reopened'\n\n";

echo "Each notification should have:\n";
echo "- Different icons and colors\n";
echo "- Specific messages for each status\n";
echo "- Center name and barangay details\n";
echo "- Completely separate from alert messages\n\n";

echo "=== END DIRECT TEST ===\n";
