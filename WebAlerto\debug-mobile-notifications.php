<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Mobile Notification Debug - Real-time Test ===\n\n";

// Get the most recent valid token
$latestToken = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->orderBy('last_used_at', 'desc')
    ->first();

if (!$latestToken) {
    echo "❌ No valid FCM tokens found!\n";
    echo "Your mobile app is not properly registering FCM tokens.\n";
    echo "Please check your mobile app FCM configuration.\n";
    exit;
}

echo "📱 USING MOST RECENT TOKEN:\n";
echo "   Token ID: {$latestToken->id}\n";
echo "   User ID: {$latestToken->user_id}\n";
echo "   Device: {$latestToken->device_type}\n";
echo "   Last Used: " . ($latestToken->last_used_at ? $latestToken->last_used_at->diffForHumans() : 'Never') . "\n";
echo "   Token Preview: " . substr($latestToken->token, 0, 50) . "...\n\n";

echo "🔔 SENDING TEST NOTIFICATION...\n";
echo "Please check your mobile device for notifications.\n\n";

try {
    $fcmService = app(App\Services\FCMService::class);
    
    // Send a simple test notification
    $testData = [
        'title' => '🚨 WebAlerto Test',
        'message' => 'This is a test notification sent at ' . date('H:i:s') . '. If you see this, FCM is working!',
        'category' => 'test',
        'test_timestamp' => time(),
        'sound' => 'default',
        'priority' => 'high'
    ];
    
    echo "📤 Sending notification with data:\n";
    echo "   Title: {$testData['title']}\n";
    echo "   Message: {$testData['message']}\n";
    echo "   Category: {$testData['category']}\n\n";
    
    $result = $fcmService->sendToToken($latestToken->token, $testData);
    
    if ($result['success']) {
        echo "✅ SUCCESS: Notification sent to FCM servers!\n";
        echo "📱 CHECK YOUR MOBILE DEVICE NOW\n\n";
        
        // Also send an evacuation center full notification
        echo "🏥 SENDING EVACUATION CENTER FULL NOTIFICATION...\n";
        
        $center = App\Models\Evacuation::first();
        if ($center) {
            $evacuationData = [
                'title' => '⚠️ Evacuation Center Full',
                'message' => "The evacuation center '{$center->name}' is now FULL. Please find alternative centers.",
                'category' => 'evacuation_center_full',
                'center_id' => $center->id,
                'center_name' => $center->name,
                'status' => 'Full',
                'sound' => 'default',
                'priority' => 'high'
            ];
            
            $result2 = $fcmService->sendToToken($latestToken->token, $evacuationData);
            
            if ($result2['success']) {
                echo "✅ Evacuation center notification sent!\n";
                echo "📱 You should see TWO notifications on your device\n\n";
            } else {
                echo "❌ Failed to send evacuation notification: " . ($result2['error'] ?? 'Unknown error') . "\n";
            }
        }
        
    } else {
        echo "❌ FAILED: " . ($result['error'] ?? 'Unknown error') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "TROUBLESHOOTING CHECKLIST:\n";
echo str_repeat("=", 60) . "\n\n";

echo "If you DON'T see notifications on your device:\n\n";

echo "1. 📱 CHECK APP PERMISSIONS:\n";
echo "   - Go to Settings > Apps > WebAlerto\n";
echo "   - Tap 'Notifications'\n";
echo "   - Make sure 'Allow notifications' is ON\n";
echo "   - Check all notification categories are enabled\n\n";

echo "2. 🔔 CHECK DEVICE SETTINGS:\n";
echo "   - Go to Settings > Notifications\n";
echo "   - Make sure 'Do Not Disturb' is OFF\n";
echo "   - Check notification sound/vibration settings\n\n";

echo "3. 📶 CHECK NETWORK:\n";
echo "   - Make sure device has internet connection\n";
echo "   - Try switching between WiFi and mobile data\n\n";

echo "4. 🔄 RESTART MOBILE APP:\n";
echo "   - Force close WebAlerto app completely\n";
echo "   - Reopen the app\n";
echo "   - Wait for FCM to reconnect\n";
echo "   - Run this test again\n\n";

echo "5. 🔧 CHECK MOBILE APP LOGS:\n";
echo "   - Open WebAlerto app\n";
echo "   - Check browser console for FCM errors\n";
echo "   - Look for Firebase initialization messages\n\n";

echo "6. 🧪 TEST WITH OTHER APPS:\n";
echo "   - Check if other apps can send you notifications\n";
echo "   - Try sending yourself a test notification from another app\n\n";

echo "If you DO see notifications:\n";
echo "✅ FCM is working! The evacuation center notifications should work.\n";
echo "✅ Test by marking a center as 'Full' in the web dashboard.\n\n";

echo "=== END DEBUG TEST ===\n";
