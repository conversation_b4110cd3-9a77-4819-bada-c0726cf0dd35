import{a as oe}from"./chunk-KE2FHCRG.js";import{a as N}from"./chunk-G7IUXWVS.js";import{b as x}from"./chunk-ICWJVXBH.js";import{$ as u,Ca as J,D as d,E as $,H as M,Hb as Q,I as y,J as O,Lb as X,M as c,Mb as Z,N as r,O as m,R as F,S,T as C,Zb as W,_ as s,_b as ee,aa as j,d as I,fa as U,fc as k,gc as te,hc as ie,i as q,l as p,n as z,nb as V,qa as B,s as T,sa as G,t as P,va as H,yb as K,zb as Y}from"./chunk-Q5Y64KIB.js";import{a as L,b as R,h as l}from"./chunk-B7O3QC5Z.js";var g=x("FirebaseMessaging",{web:()=>import("./chunk-S7ITX3DD.js").then(n=>new n.FirebaseMessagingWeb)});var _=x("LocalNotifications",{web:()=>import("./chunk-KOQYFS4S.js").then(n=>new n.LocalNotificationsWeb)});function ce(n,E){if(n&1&&(c(0,"div",12)(1,"h3",13),m(2,"ion-icon",23),s(3," Location "),r(),c(4,"p",24),s(5),r()()),n&2){let e=C();d(5),u(e.notification.barangay)}}function se(n,E){if(n&1){let e=F();c(0,"div",12)(1,"h3",13),m(2,"ion-icon",25),s(3," Affected Areas "),r(),c(4,"div",26)(5,"p",27),s(6),r(),c(7,"ion-button",28),S("click",function(){T(e);let i=C();return P(i.viewOnMap())}),m(8,"ion-icon",29),s(9," View on Map "),r()()()}if(n&2){let e=C();d(6),u(e.getAffectedAreasSummary())}}function le(n,E){if(n&1&&(c(0,"div",12)(1,"h3",13),s(2,"Reference ID"),r(),c(3,"p",30),s(4),r()()),n&2){let e=C();d(4),u(e.notification.notification_id)}}function de(n,E){if(n&1){let e=F();c(0,"ion-button",31),S("click",function(){T(e);let i=C();return P(i.viewOnMap())}),m(1,"ion-icon",32),s(2," Navigate to Area "),r()}}var ne=(()=>{class n{constructor(e){this.modalController=e}ngOnInit(){console.log("Notification detail opened:",this.notification)}closeModal(){return l(this,null,function*(){yield this.modalController.dismiss()})}getCategoryDisplayName(e){switch(e.toLowerCase()){case"earthquake":return"Earthquake Alert";case"typhoon":return"Typhoon Warning";case"flood":return"Flood Alert";case"fire":return"Fire Emergency";case"emergency":return"Emergency Alert";case"evacuation":return"Evacuation Notice";case"general":return"General Announcement";case"announcement":return"Public Announcement";default:return"Alert"}}getCategoryIcon(e){switch(e.toLowerCase()){case"earthquake":return"earth-outline";case"typhoon":return"thunderstorm-outline";case"flood":return"water-outline";case"fire":return"flame-outline";case"emergency":return"warning-outline";case"evacuation":return"walk-outline";case"general":return"megaphone-outline";case"announcement":return"chatbubble-outline";default:return"alert-circle-outline"}}getCategoryColor(e){switch(e.toLowerCase()){case"earthquake":return"#FFA500";case"typhoon":return"#008000";case"flood":return"#0066CC";case"fire":return"#FF0000";case"emergency":return"#FF0000";case"evacuation":return"#FF6600";case"general":return"#666666";case"announcement":return"#0066CC";default:return"#666666"}}getSeverityDisplayName(e){switch(e.toLowerCase()){case"high":return"High Priority";case"medium":return"Medium Priority";case"low":return"Low Priority";default:return"Normal Priority"}}getSeverityColor(e){switch(e.toLowerCase()){case"high":return"#FF0000";case"medium":return"#FFA500";case"low":return"#008000";default:return"#666666"}}getFormattedTimestamp(e){try{return new Date(e).toLocaleString()}catch{return e}}getAffectedAreasSummary(){if(!this.notification.affected_areas||!Array.isArray(this.notification.affected_areas))return"No specific areas defined";let e=this.notification.affected_areas.length;return e===1?"1 area affected":`${e} areas affected`}hasMapData(){return!!(this.notification.affected_areas&&Array.isArray(this.notification.affected_areas)&&this.notification.affected_areas.length>0)}viewOnMap(){console.log("View on map clicked for areas:",this.notification.affected_areas)}static{this.\u0275fac=function(t){return new(t||n)($(k))}}static{this.\u0275cmp=z({type:n,selectors:[["app-notification-detail"]],inputs:{notification:"notification"},standalone:!0,features:[U],decls:42,vars:14,consts:[["slot","end"],[3,"click"],["name","close"],[1,"notification-detail-content"],[1,"notification-detail-container"],[1,"category-header"],[1,"category-info"],[1,"category-icon",3,"name"],[1,"category-text"],[1,"category-title"],[1,"severity-badge"],[1,"notification-content"],[1,"content-section"],[1,"section-title"],[1,"notification-title"],[1,"notification-message"],["class","content-section",4,"ngIf"],["name","time-outline",1,"section-icon"],[1,"timestamp"],[1,"action-buttons"],["expand","block","fill","solid","color","primary",3,"click"],["name","checkmark","slot","start"],["expand","block","fill","outline","color","medium",3,"click",4,"ngIf"],["name","location-outline",1,"section-icon"],[1,"location-info"],["name","map-outline",1,"section-icon"],[1,"affected-areas-info"],[1,"areas-summary"],["fill","outline","size","small",1,"view-map-button",3,"click"],["name","map","slot","start"],[1,"notification-id"],["expand","block","fill","outline","color","medium",3,"click"],["name","navigate","slot","start"]],template:function(t,i){t&1&&(c(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),s(3,"Notification Details"),r(),c(4,"ion-buttons",0)(5,"ion-button",1),S("click",function(){return i.closeModal()}),m(6,"ion-icon",2),r()()()(),c(7,"ion-content",3)(8,"div",4)(9,"div",5)(10,"div",6),m(11,"ion-icon",7),c(12,"div",8)(13,"h2",9),s(14),r(),c(15,"p",10),s(16),r()()()(),c(17,"div",11)(18,"div",12)(19,"h3",13),s(20,"Alert Title"),r(),c(21,"p",14),s(22),r()(),c(23,"div",12)(24,"h3",13),s(25,"Message"),r(),c(26,"p",15),s(27),r()(),M(28,ce,6,1,"div",16)(29,se,10,1,"div",16),c(30,"div",12)(31,"h3",13),m(32,"ion-icon",17),s(33," Received "),r(),c(34,"p",18),s(35),r()(),M(36,le,5,1,"div",16),r(),c(37,"div",19)(38,"ion-button",20),S("click",function(){return i.closeModal()}),m(39,"ion-icon",21),s(40," Mark as Read "),r(),M(41,de,3,0,"ion-button",22),r()()()),t&2&&(d(9),O("background-color",i.getCategoryColor(i.notification.category)),d(2),y("name",i.getCategoryIcon(i.notification.category)),d(3),u(i.getCategoryDisplayName(i.notification.category)),d(),O("background-color",i.getSeverityColor(i.notification.severity)),d(),j(" ",i.getSeverityDisplayName(i.notification.severity)," "),d(6),u(i.notification.title),d(5),u(i.notification.body),d(),y("ngIf",i.notification.barangay),d(),y("ngIf",i.hasMapData()),d(6),u(i.getFormattedTimestamp(i.notification.timestamp)),d(),y("ngIf",i.notification.notification_id),d(5),y("ngIf",i.hasMapData()))},dependencies:[ie,K,Y,Q,X,Z,W,ee,G,B],styles:['@charset "UTF-8";.notification-detail-content[_ngcontent-%COMP%]{--background: #f8f9fa}.notification-detail-container[_ngcontent-%COMP%]{padding:0;min-height:100%}.category-header[_ngcontent-%COMP%]{padding:20px;color:#fff;position:relative;background:linear-gradient(135deg,var(--ion-color-primary),var(--ion-color-primary-shade))}.category-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-10px;left:0;right:0;height:20px;background:#f8f9fa;border-radius:20px 20px 0 0}.category-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.category-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff}.category-text[_ngcontent-%COMP%]{flex:1}.category-title[_ngcontent-%COMP%]{margin:0 0 8px;font-size:1.4rem;font-weight:600;color:#fff}.severity-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 12px;border-radius:12px;font-size:.8rem;font-weight:600;color:#fff;margin:0}.notification-content[_ngcontent-%COMP%]{padding:20px;background:#fff;margin:0 16px 16px;border-radius:12px;box-shadow:0 2px 8px #0000001a}.content-section[_ngcontent-%COMP%]{margin-bottom:24px}.content-section[_ngcontent-%COMP%]:last-child{margin-bottom:0}.section-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 8px;font-size:1rem;font-weight:600;color:var(--ion-color-dark)}.section-icon[_ngcontent-%COMP%]{font-size:1.1rem;color:var(--ion-color-medium)}.notification-title[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark);line-height:1.4}.notification-message[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);line-height:1.5;white-space:pre-wrap}.location-info[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);display:flex;align-items:center;gap:8px}.location-info[_ngcontent-%COMP%]:before{content:"\\1f4cd";font-size:1.1rem}.affected-areas-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.areas-summary[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);display:flex;align-items:center;gap:8px}.areas-summary[_ngcontent-%COMP%]:before{content:"\\1f5fa\\fe0f";font-size:1.1rem}.view-map-button[_ngcontent-%COMP%]{align-self:flex-start;--border-radius: 8px}.timestamp[_ngcontent-%COMP%]{margin:0;font-size:.95rem;color:var(--ion-color-medium)}.notification-id[_ngcontent-%COMP%]{margin:0;font-size:.85rem;color:var(--ion-color-medium);font-family:monospace;background:var(--ion-color-light);padding:8px;border-radius:6px}.action-buttons[_ngcontent-%COMP%]{padding:16px;display:flex;flex-direction:column;gap:12px}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;font-weight:600}@media (max-width: 768px){.category-header[_ngcontent-%COMP%]{padding:16px}.category-title[_ngcontent-%COMP%]{font-size:1.2rem}.category-icon[_ngcontent-%COMP%]{font-size:2rem}.notification-content[_ngcontent-%COMP%]{margin:0 12px 12px;padding:16px}}@media (prefers-color-scheme: dark){.notification-detail-content[_ngcontent-%COMP%]{--background: #1a1a1a}.notification-content[_ngcontent-%COMP%]{background:#2a2a2a;color:#fff}.section-title[_ngcontent-%COMP%], .notification-title[_ngcontent-%COMP%], .notification-message[_ngcontent-%COMP%], .location-info[_ngcontent-%COMP%], .areas-summary[_ngcontent-%COMP%]{color:#fff}.notification-id[_ngcontent-%COMP%]{background:#3a3a3a;color:#ccc}}']})}}return n})();var Oe=(()=>{class n{constructor(e,t,i,o,a,f){this.platform=e,this.http=t,this.modalController=i,this.emergencyOverlay=o,this.router=a,this.toastController=f,this.fcmToken="",this.isInitialized=!1}initializeFCM(){return l(this,null,function*(){try{this.platform.is("capacitor")?(yield this.requestPermissions(),yield this.getFCMToken(),this.listenForTokenRefresh(),this.listenForMessages(),this.listenForLocalNotificationActions(),this.isInitialized=!0,console.log("FCM initialized successfully")):console.log("FCM not available on this platform")}catch(e){console.error("Error initializing FCM:",e),this.isInitialized=!1}})}requestPermissions(){return l(this,null,function*(){try{let e=yield g.requestPermissions();console.log("FCM permissions result:",e),e.receive==="granted"?console.log("FCM permissions granted"):console.warn("FCM permissions denied")}catch(e){console.error("Error requesting FCM permissions:",e)}})}getFCMToken(){return l(this,null,function*(){try{let e=yield g.getToken();return this.fcmToken=e.token,console.log("FCM Token:",this.fcmToken),yield this.registerTokenWithBackend(this.fcmToken),this.fcmToken}catch(e){return console.error("Error getting FCM token:",e),""}})}listenForTokenRefresh(){g.addListener("tokenReceived",e=>l(this,null,function*(){console.log("FCM token refreshed:",e.token),this.fcmToken=e.token,yield this.registerTokenWithBackend(e.token)}))}listenForMessages(){g.addListener("notificationReceived",e=>{console.log("\u{1F4F1} FCM notification received:",{notification:e.notification,timestamp:new Date().toISOString()}),this.isEmergencyNotification(e.notification)?this.handleEmergencyNotification(e.notification):this.showLocalNotification(e.notification)}),g.addListener("notificationActionPerformed",e=>{console.log("\u{1F4F1} FCM notification action performed:",{action:e,timestamp:new Date().toISOString()}),this.handleNotificationAction(e)})}listenForLocalNotificationActions(){_.addListener("localNotificationActionPerformed",e=>{console.log("Local notification action performed:",{action:e,timestamp:new Date().toISOString()}),this.handleNotificationAction(e)})}isEmergencyNotification(e){let t=e.data||{},i=t.category?.toLowerCase()||"",o=t.severity?.toLowerCase()||"";return["earthquake","flood","typhoon","fire","landslide"].includes(i)||o==="high"||o==="critical"||t.emergency==="true"||t.emergency===!0}handleEmergencyNotification(e){return l(this,null,function*(){try{console.log("\u{1F6A8} EMERGENCY NOTIFICATION RECEIVED:",e);let t=e.data||{},i={id:t.notification_id||`emergency_${Date.now()}`,title:e.title||"Emergency Alert",message:e.body||"Emergency notification received",category:this.mapToEmergencyCategory(t.category||"General"),severity:this.mapToEmergencySeverity(t.severity||"medium"),timestamp:new Date().toISOString(),data:t};yield this.emergencyOverlay.showEmergencyNotification(i)}catch(t){console.error("Error handling emergency notification:",t),this.showLocalNotification(e)}})}mapToEmergencyCategory(e){return{earthquake:"Earthquake",flood:"Flood",typhoon:"Typhoon",fire:"Fire",landslide:"Landslide",general:"General",emergency:"General"}[e.toLowerCase()]||"General"}mapToEmergencySeverity(e){return{low:"low",medium:"medium",high:"high",critical:"critical",urgent:"critical"}[e.toLowerCase()]||"medium"}showLocalNotification(e){return l(this,null,function*(){try{console.log("\uFFFD [FOREGROUND] Attempting to show notification:",JSON.stringify(e,null,2));let t=yield _.checkPermissions();if(console.log("\u{1F4CB} [PERMISSIONS] Current status:",JSON.stringify(t,null,2)),t.display!=="granted"){console.log("\u{1F512} [PERMISSIONS] Requesting notification permissions...");let D=yield _.requestPermissions();if(console.log("\u{1F4DD} [PERMISSIONS] Request result:",JSON.stringify(D,null,2)),D.display!=="granted"){console.error("\u274C [PERMISSIONS] Notification permissions denied - cannot show foreground notifications"),console.error("\u274C [PERMISSIONS] User needs to enable notifications in Android settings");return}}let i=this.generateUniqueNotificationId(),o=e.data||{},a=o.category||"general",f=o.severity||"medium",h=o.affected_areas?this.parseAffectedAreas(o.affected_areas):null,b=o.barangay||"",w=e.title||"New Alert",v=e.body||"You have received a new notification";this.isRichTitle(w)||(w=this.createRichTitle(w,a,f)),this.isRichBody(v)||(v=this.createRichBody(v,h,b,f)),console.log("\u{1F4E4} [SCHEDULING] Preparing local notification:",{id:i,title:w,body:v,category:a,severity:f,originalNotification:e});let A={title:w,body:v,id:i,schedule:{at:new Date(Date.now()+500)},sound:this.getNotificationSound(f),attachments:[],actionTypeId:"",extra:R(L({},o),{original_title:e.title,original_body:e.body,notification_id:o.notification_id,category:a,severity:f,affected_areas:h,barangay:b,timestamp:new Date().toISOString()})};console.log("\u{1F4E4} [SCHEDULING] Full notification payload:",JSON.stringify(A,null,2)),yield _.schedule({notifications:[A]}),console.log("\u2705 [SUCCESS] Local notification scheduled successfully with ID:",i),console.log("\u2705 [SUCCESS] Notification should appear in Android notification panel")}catch(t){console.error("\u274C [ERROR] Failed to show local notification:",t),console.error("\u274C [ERROR] Error details:",JSON.stringify(t,null,2));try{console.log("\u{1F504} [FALLBACK] Attempting immediate notification..."),yield _.schedule({notifications:[{title:e.title||"New Alert",body:e.body||"You have a new notification",id:Date.now(),schedule:{at:new Date(Date.now()+100)},sound:"default"}]}),console.log("\u2705 [FALLBACK] Simple notification scheduled")}catch(i){console.error("\u274C [FALLBACK] Simple notification also failed:",i),console.error("\u274C [FALLBACK] This indicates a deeper issue with local notifications on this device")}}})}generateUniqueNotificationId(){let e=Date.now(),t=Math.floor(Math.random()*1e3);return parseInt(`${e}${t}`)}createRichTitle(e,t,i){let o=this.getCategoryEmoji(t),a=this.getSeverityText(i);return`${o} ${a}: ${e}`}createRichBody(e,t,i,o){let a=e;if(o){let h=this.getPriorityIcon(o),b=this.getPriorityDescription(o);a+=`
${h} Priority: ${b}`}let f=this.formatAffectedAreas(t);return f?a+=`
\u{1F5FA}\uFE0F Affected Area/s: ${f}`:i&&i!=="All Areas"&&(a+=`
\uFFFD Area: ${i}`),a}getCategoryEmoji(e){switch(e.toLowerCase()){case"earthquake":return"\u{1F30D}";case"typhoon":return"\u{1F32A}\uFE0F";case"flood":return"\u{1F30A}";case"fire":return"\u{1F525}";case"emergency":return"\u{1F6A8}";case"evacuation":return"\u{1F3C3}\u200D\u2642\uFE0F";case"general":return"\u{1F4E2}";case"announcement":return"\u{1F4E3}";default:return"\u26A0\uFE0F"}}getSeverityText(e){switch(e.toLowerCase()){case"high":return"URGENT";case"medium":return"ALERT";case"low":return"INFO";default:return"NOTICE"}}getPriorityIcon(e){switch(e.toLowerCase()){case"high":return"\u{1F534}";case"medium":return"\u{1F7E1}";case"low":return"\u{1F7E2}";default:return"\u{1F535}"}}getPriorityDescription(e){switch(e.toLowerCase()){case"high":return"Critical - Immediate Action Required";case"medium":return"High - Action Required Soon";case"low":return"Normal - For Your Information";default:return"Standard - General Notice"}}getNotificationSound(e){switch(e.toLowerCase()){case"high":return"emergency.wav";case"medium":return"alert.wav";case"low":return"beep.wav";default:return"default"}}parseAffectedAreas(e){try{return JSON.parse(e)}catch(t){return console.error("Error parsing affected areas:",t),null}}formatAffectedAreas(e){if(!e)return null;try{let t=[];if(typeof e=="string")t=JSON.parse(e);else if(Array.isArray(e))t=e;else return null;if(!Array.isArray(t)||t.length===0)return null;let i=[];for(let o of t)typeof o=="object"&&o.name?i.push(o.name):typeof o=="string"&&i.push(o);if(i.length===0)return null;if(i.length===1)return i[0];if(i.length===2)return i.join(" and ");{let o=i.pop();return i.join(", ")+", and "+o}}catch(t){return console.error("Error formatting affected areas:",t),null}}isRichTitle(e){return/[🌍🌪️🌊🔥🚨🏃‍♂️📢📣⚠️]/.test(e)}isRichBody(e){return e.includes("\uFFFD Priority:")||e.includes("\u{1F7E1} Priority:")||e.includes("\u{1F7E2} Priority:")||e.includes("\uFFFD Priority:")}handleNotificationAction(e){return l(this,null,function*(){console.log("\u{1F514} Handling notification action:",e);let t=e.notification?.extra||e.notification?.data||{};if(t){let i=t.category?.toLowerCase()||"",o=t.severity?.toLowerCase()||"";console.log("\u{1F4F1} Notification data:",{category:i,severity:o,title:t.title||t.original_title,emergency:this.isEmergencyNotification({data:t})}),this.shouldRouteToDisasterMap(i,o)?yield this.routeToDisasterMap(i,t):yield this.showNotificationDetail(t)}})}shouldRouteToDisasterMap(e,t){let i=["earthquake","flood","typhoon","fire","landslide"],o=["high","critical","emergency"];return i.includes(e)||o.includes(t)}routeToDisasterMap(e,t){return l(this,null,function*(){try{console.log(`\u{1F5FA}\uFE0F Routing for ${e} disaster notification...`);let i,o,a={earthquake:"/tabs/earthquake-map",flood:"/tabs/flood-map",typhoon:"/tabs/typhoon-map",landslide:"/tabs/landslide-map",fire:"/tabs/fire-map"};a[e]?(i=a[e],o=`${e} evacuation centers`):(i="/tabs/map",o="evacuation centers"),yield this.router.navigate([i],{queryParams:{emergency:!0,autoRoute:!0,notification:!0,category:e,severity:t.severity||"medium",timestamp:Date.now(),title:t.title||t.original_title,message:t.message||t.body}}),yield(yield this.toastController.create({message:`\u{1F6A8} Emergency: Navigating to ${o} for ${e} alert`,duration:4e3,color:"danger",position:"top",cssClass:"emergency-toast"})).present(),console.log(`\u2705 Successfully routed to ${i} for ${e} emergency`)}catch(i){console.error("\u274C Error routing to disaster map:",i),yield this.showNotificationDetail(t)}})}showNotificationDetail(e){return l(this,null,function*(){try{yield(yield this.modalController.create({component:ne,componentProps:{notification:{id:e.notification_id||Date.now(),title:e.title||"Notification",body:e.body||"No message content",category:e.category||"general",severity:e.severity||"medium",barangay:e.barangay||"",affected_areas:e.affected_areas||null,timestamp:e.timestamp||new Date().toISOString(),notification_id:e.notification_id||""}},cssClass:"notification-detail-modal"})).present()}catch(t){console.error("Error showing notification detail:",t)}})}registerTokenWithBackend(e){return l(this,null,function*(){try{let t=this.getCurrentUserId(),i={token:e,device_type:"android",project_id:N.firebase.projectId,user_id:t};console.log("Registering FCM token with backend:",{token:e.substring(0,20)+"...",device_type:i.device_type,project_id:i.project_id,user_id:i.user_id});let o=`${N.apiUrl}/device-token`;try{let a=yield I(this.http.post(o,i));console.log("FCM token registered successfully:",a),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","true");return}catch(a){if(console.error("Error registering token with public endpoint:",a),t)try{let f=`${N.apiUrl}/device-token/register`,h=yield I(this.http.post(f,i));console.log("FCM token registered with protected endpoint:",h),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","true");return}catch(f){console.error("Error registering token with protected endpoint:",f)}throw localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","false"),a}}catch(t){console.error("Error registering FCM token with backend:",t),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","false")}})}getCurrentUserId(){let e=["user","currentUser","authUser","userData"];for(let t of e){let i=localStorage.getItem(t);if(i)try{let o=JSON.parse(i);if(o&&(o.id||o.user_id||o.userId))return o.id||o.user_id||o.userId}catch(o){console.error(`Error parsing ${t} data:`,o)}}return console.log("No authenticated user found, registering token anonymously"),null}retryTokenRegistration(){return l(this,null,function*(){let e=localStorage.getItem("fcm_token"),t=localStorage.getItem("fcm_token_registered");e&&t==="false"&&(console.log("Retrying FCM token registration..."),yield this.registerTokenWithBackend(e))})}getCurrentToken(){if(this.fcmToken)return this.fcmToken;let e=localStorage.getItem("fcm_token");return e?(this.fcmToken=e,e):""}subscribeToTopic(e){return l(this,null,function*(){try{yield g.subscribeToTopic({topic:e}),console.log(`Subscribed to topic: ${e}`)}catch(t){console.error(`Error subscribing to topic ${e}:`,t)}})}unsubscribeFromTopic(e){return l(this,null,function*(){try{yield g.unsubscribeFromTopic({topic:e}),console.log(`Unsubscribed from topic: ${e}`)}catch(t){console.error(`Error unsubscribing from topic ${e}:`,t)}})}deleteToken(){return l(this,null,function*(){try{yield g.deleteToken(),this.fcmToken="",localStorage.removeItem("fcm_token"),console.log("FCM token deleted")}catch(e){console.error("Error deleting FCM token:",e)}})}getServiceStatus(){return{isInitialized:this.isInitialized,hasToken:!!this.fcmToken,token:this.fcmToken?this.fcmToken.substring(0,20)+"...":"No token",timestamp:new Date().toISOString()}}static{this.\u0275fac=function(t){return new(t||n)(p(V),p(H),p(k),p(oe),p(J),p(te))}}static{this.\u0275prov=q({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();export{Oe as a};
