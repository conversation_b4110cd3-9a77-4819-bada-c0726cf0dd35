import{$b as u,Ha as l,M as e,N as t,O as n,Pb as b,Wb as c,Xb as p,_ as o,fa as r,hc as d,n as m,sa as s}from"./chunk-Q5Y64KIB.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-B7O3QC5Z.js";var I=(()=>{class a{constructor(){}static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275cmp=m({type:a,selectors:[["app-tabs"]],standalone:!0,features:[r],decls:18,vars:0,consts:[["slot","bottom"],["tab","home"],["src","assets/home1.png",1,"tab-icon"],["tab","search"],["src","assets/search1.png",1,"tab-icon"],["tab","map"],["src","assets/map1.png",1,"tab-icon"],["tab","profile"],["src","assets/lamp1.png",1,"tab-icon"]],template:function(i,g){i&1&&(e(0,"ion-tabs")(1,"ion-tab-bar",0)(2,"ion-tab-button",1),n(3,"img",2),e(4,"ion-label"),o(5,"Home"),t()(),e(6,"ion-tab-button",3),n(7,"img",4),e(8,"ion-label"),o(9,"Search"),t()(),e(10,"ion-tab-button",5),n(11,"img",6),e(12,"ion-label"),o(13,"Map"),t()(),e(14,"ion-tab-button",7),n(15,"img",8),e(16,"ion-label"),o(17,"Tips"),t()()()())},dependencies:[d,b,c,p,u,s,l],encapsulation:2})}}return a})();export{I as TabsPage};
