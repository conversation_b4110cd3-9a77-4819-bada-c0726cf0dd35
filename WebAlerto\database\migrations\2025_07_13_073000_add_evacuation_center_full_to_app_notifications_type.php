<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to alter the enum column to add the new value
        DB::statement("ALTER TABLE app_notifications MODIFY COLUMN type ENUM('evacuation_center_added', 'evacuation_center_full', 'emergency_alert', 'system_update', 'general') DEFAULT 'general'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the evacuation_center_full type (be careful - this will fail if there are records with this type)
        DB::statement("ALTER TABLE app_notifications MODIFY COLUMN type ENUM('evacuation_center_added', 'emergency_alert', 'system_update', 'general') DEFAULT 'general'");
    }
};
