<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Testing All Evacuation Center Notification Types ===\n\n";

// Get the first evacuation center
$center = App\Models\Evacuation::first();

if (!$center) {
    echo "❌ No evacuation centers found!\n";
    exit;
}

echo "📍 Testing with: {$center->name} (ID: {$center->id})\n";
echo "📍 Barangay: {$center->barangay}\n";
echo "📍 Current Status: {$center->status}\n\n";

// Check mobile users and tokens
$mobileUsers = App\Models\User::where('role', 'mobile_user')->count();
$activeTokens = App\Models\DeviceToken::where('is_active', true)
    ->whereNotNull('token')
    ->where('token', '!=', 'null')
    ->whereRaw('LENGTH(token) > 50')
    ->count();

echo "📱 Mobile Users: {$mobileUsers}\n";
echo "🔔 Active FCM Tokens: {$activeTokens}\n\n";

if ($mobileUsers === 0 || $activeTokens === 0) {
    echo "⚠️  Cannot send notifications - missing users or tokens\n";
    exit;
}

echo "🧪 TESTING DIFFERENT NOTIFICATION SCENARIOS...\n\n";

// Test scenarios
$testScenarios = [
    [
        'name' => 'Center becomes FULL',
        'from_status' => 'Active',
        'to_status' => 'Full',
        'description' => 'When a center reaches capacity'
    ],
    [
        'name' => 'Center becomes AVAILABLE',
        'from_status' => 'Full',
        'to_status' => 'Active',
        'description' => 'When a full center has space again'
    ],
    [
        'name' => 'Center CLOSES',
        'from_status' => 'Active',
        'to_status' => 'Inactive',
        'description' => 'When a center stops operations'
    ],
    [
        'name' => 'Center REOPENS',
        'from_status' => 'Inactive',
        'to_status' => 'Active',
        'description' => 'When a closed center reopens'
    ]
];

foreach ($testScenarios as $index => $scenario) {
    echo "📋 TEST " . ($index + 1) . ": {$scenario['name']}\n";
    echo "   Description: {$scenario['description']}\n";
    echo "   Status Change: {$scenario['from_status']} → {$scenario['to_status']}\n";
    
    try {
        // Set the initial status
        $center->update(['status' => $scenario['from_status']]);
        echo "   ✅ Set initial status to: {$scenario['from_status']}\n";
        
        // Wait a moment
        sleep(1);
        
        // Simulate the status change via API call
        $postData = json_encode(['status' => $scenario['to_status']]);
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\nContent-Length: " . strlen($postData),
                'content' => $postData
            ]
        ]);
        
        $response = file_get_contents("http://127.0.0.1:8000/api/evacuation-centers/{$center->id}/status", false, $context);
        $result = json_decode($response, true);
        
        if ($result && $result['success']) {
            echo "   ✅ Status updated successfully\n";
            echo "   📤 Notification sent: {$result['message']}\n";
            echo "   📱 CHECK YOUR MOBILE DEVICE for notification!\n";
        } else {
            echo "   ❌ Failed to update status: " . ($result['message'] ?? 'Unknown error') . "\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "   " . str_repeat("-", 50) . "\n";
    
    // Wait between tests
    if ($index < count($testScenarios) - 1) {
        echo "   ⏳ Waiting 3 seconds before next test...\n\n";
        sleep(3);
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "NOTIFICATION TYPES TESTED:\n";
echo str_repeat("=", 60) . "\n";
echo "1. ⚠️  FULL: When center reaches capacity\n";
echo "2. ✅ AVAILABLE: When full center has space again\n";
echo "3. 🚫 CLOSED: When center stops operations\n";
echo "4. 🔄 REOPENED: When closed center reopens\n\n";

echo "WHAT TO EXPECT ON YOUR MOBILE DEVICE:\n";
echo "------------------------------------\n";
echo "You should have received 4 different notifications with:\n";
echo "- Different titles and icons\n";
echo "- Different colors and priorities\n";
echo "- Specific messages for each status change\n";
echo "- All completely separate from alert messages\n\n";

// Check recent notifications in database
echo "📋 RECENT EVACUATION CENTER NOTIFICATIONS:\n";
$recentNotifications = App\Models\AppNotification::whereIn('type', [
    'evacuation_center_full',
    'evacuation_center_available', 
    'evacuation_center_closed',
    'evacuation_center_reopened',
    'evacuation_center_update'
])->orderBy('created_at', 'desc')
->take(10)
->get(['type', 'title', 'message', 'created_at']);

if ($recentNotifications->count() > 0) {
    foreach ($recentNotifications as $notification) {
        $timeAgo = $notification->created_at->diffForHumans();
        echo "   📢 {$notification->title}\n";
        echo "      Type: {$notification->type}\n";
        echo "      Message: " . substr($notification->message, 0, 60) . "...\n";
        echo "      Time: {$timeAgo}\n";
        echo "      ---\n";
    }
} else {
    echo "   ❌ No evacuation center notifications found in database\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY:\n";
echo str_repeat("=", 60) . "\n";
echo "✅ Database: All notification types supported\n";
echo "✅ Backend: Comprehensive notification system\n";
echo "✅ FCM: Separate from alert message system\n";
echo "✅ Types: Full, Available, Closed, Reopened\n";
echo "❓ Mobile: Check if you received all 4 notifications\n\n";

echo "NEXT STEPS:\n";
echo "----------\n";
echo "1. Check your mobile device for 4 different notifications\n";
echo "2. Test the web dashboard by changing center statuses\n";
echo "3. Each status change should trigger appropriate notifications\n";
echo "4. Notifications are now completely separate from alert messages\n\n";

echo "=== END COMPREHENSIVE TEST ===\n";
